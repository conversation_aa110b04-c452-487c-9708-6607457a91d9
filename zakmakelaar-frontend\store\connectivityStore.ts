import { create } from "zustand";

export interface OfflineMutationRecord {
  id: number;
  mutationKey: string;
  variablesSummary?: unknown;
  timestamp: number;
  status: "queued" | "retrying";
}

interface ConnectivityState {
  isOnline: boolean;
  queuedMutations: OfflineMutationRecord[];
  setIsOnline: (isOnline: boolean) => void;
  setQueuedMutations: (mutations: OfflineMutationRecord[]) => void;
  updateMutationStatus: (
    id: number,
    status: OfflineMutationRecord["status"]
  ) => void;
}

export const useConnectivityStore = create<ConnectivityState>((set) => ({
  isOnline: true,
  queuedMutations: [],
  setIsOnline: (isOnline) => set({ isOnline }),
  setQueuedMutations: (mutations) => set({ queuedMutations: mutations }),
  updateMutationStatus: (id, status) =>
    set((state) => ({
      queuedMutations: state.queuedMutations.map((record) =>
        record.id === id ? { ...record, status } : record
      ),
    })),
}));
