// MongoDB initialization script
// This script runs when the MongoDB container starts for the first time

// Switch to the zakmakelaar database
db = db.getSiblingDB('zakmakelaar');

// Create a user for the application
db.createUser({
  user: 'zakmakelaar_user',
  pwd: 'zakmakelaar_password',
  roles: [
    {
      role: 'readWrite',
      db: 'zakmakelaar'
    }
  ]
});

// Create indexes for better performance
db.listings.createIndex({ "location.city": 1 });
db.listings.createIndex({ "price": 1 });
db.listings.createIndex({ "propertyType": 1 });
db.listings.createIndex({ "rooms": 1 });
db.listings.createIndex({ "createdAt": -1 });
db.listings.createIndex({ "updatedAt": -1 });
db.listings.createIndex({ "url": 1 }, { unique: true });

db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "createdAt": -1 });

print('Database initialized successfully');
