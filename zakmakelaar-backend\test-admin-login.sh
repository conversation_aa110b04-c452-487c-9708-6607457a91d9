#!/bin/bash

# Test Admin Account Script for Linux Production Server
# Tests the admin account login with correct credentials

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
BASE_URL="http://localhost:3000"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="Admin123!"

echo -e "${BLUE}=== Testing Admin Account Login ===${NC}"

# Function to test API endpoint
test_api() {
    local method="$1"
    local endpoint="$2"
    local data="$3"
    local description="$4"
    local auth_header="$5"
    
    echo -e "\n${YELLOW}🔄 Testing: $description${NC}"
    echo -e "   $method $endpoint"
    
    local curl_cmd="curl -s -w 'HTTP_STATUS:%{http_code}' -X $method '$BASE_URL$endpoint'"
    
    if [ -n "$data" ]; then
        curl_cmd="$curl_cmd -H 'Content-Type: application/json' -d '$data'"
    fi
    
    if [ -n "$auth_header" ]; then
        curl_cmd="$curl_cmd -H 'Authorization: Bearer $auth_header'"
    fi
    
    local response=$(eval $curl_cmd)
    local body=$(echo "$response" | sed 's/HTTP_STATUS:.*$//')
    local status=$(echo "$response" | grep -o 'HTTP_STATUS:[0-9]*' | cut -d: -f2)
    
    if [ "$status" -eq 200 ] || [ "$status" -eq 201 ]; then
        echo -e "   ${GREEN}✅ SUCCESS (HTTP $status)${NC}"
        if command -v jq &> /dev/null; then
            echo "$body" | jq '.' 2>/dev/null || echo "$body"
        else
            echo "$body"
        fi
        return 0
    else
        echo -e "   ${RED}❌ FAILED (HTTP $status)${NC}"
        echo -e "   ${YELLOW}Response: $body${NC}"
        return 1
    fi
}

# Test 1: Check server health
echo -e "\n${YELLOW}🏥 Checking server health...${NC}"
if test_api "GET" "/health" "" "Server Health Check"; then
    echo -e "   ${GREEN}✅ Server is running${NC}"
else
    echo -e "   ${RED}❌ Server is not responding${NC}"
    exit 1
fi

# Test 2: Admin login
echo -e "\n${YELLOW}🔐 Testing admin login...${NC}"
LOGIN_DATA="{\"email\":\"$ADMIN_EMAIL\",\"password\":\"$ADMIN_PASSWORD\"}"

if response=$(test_api "POST" "/api/auth/login" "$LOGIN_DATA" "Admin Login"); then
    # Extract token from response
    if command -v jq &> /dev/null; then
        TOKEN=$(echo "$response" | sed 's/HTTP_STATUS:.*$//' | jq -r '.token // empty')
    else
        # Fallback token extraction without jq
        TOKEN=$(echo "$response" | sed 's/HTTP_STATUS:.*$//' | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    fi
    
    if [ -n "$TOKEN" ] && [ "$TOKEN" != "null" ]; then
        echo -e "   ${GREEN}✅ Admin login successful${NC}"
        echo -e "   ${CYAN}Token: ${TOKEN:0:20}...${NC}"
    else
        echo -e "   ${RED}❌ No token received in response${NC}"
        exit 1
    fi
else
    echo -e "   ${RED}❌ Admin login failed${NC}"
    echo -e "\n${CYAN}💡 Troubleshooting:${NC}"
    echo -e "   1. Check if admin user exists: node create-admin.js"
    echo -e "   2. Verify credentials: <EMAIL> / Admin123!"
    echo -e "   3. Check MongoDB connection"
    exit 1
fi

# Test 3: Test admin-protected endpoint
echo -e "\n${YELLOW}🛡️ Testing admin-protected endpoint...${NC}"
TEST_USER_ID="000000000000000000000001"  # Dummy user ID for testing

if test_api "GET" "/api/admin/user-status/$TEST_USER_ID" "" "Admin User Status Endpoint" "$TOKEN"; then
    echo -e "   ${GREEN}✅ Admin endpoint access successful${NC}"
else
    echo -e "   ${YELLOW}⚠️ Admin endpoint test failed (this is normal if test user doesn't exist)${NC}"
fi

# Test 4: Test admin services
echo -e "\n${YELLOW}👑 Testing admin services availability...${NC}"

# Check if admin services are properly loaded
if test_api "GET" "/api-docs" "" "API Documentation" "$TOKEN"; then
    echo -e "   ${GREEN}✅ API documentation accessible${NC}"
    echo -e "   ${CYAN}💡 Admin endpoints should be documented at: $BASE_URL/api-docs${NC}"
fi

echo -e "\n${GREEN}=== Admin Account Test Complete ===${NC}"
echo -e "\n${CYAN}📋 Admin Account Details:${NC}"
echo -e "   ${NC}Email: $ADMIN_EMAIL${NC}"
echo -e "   ${NC}Password: $ADMIN_PASSWORD${NC}"
echo -e "   ${NC}Role: admin${NC}"
echo -e "\n${CYAN}🔗 Useful Endpoints:${NC}"
echo -e "   ${NC}API Docs: $BASE_URL/api-docs${NC}"
echo -e "   ${NC}Health Check: $BASE_URL/health${NC}"
echo -e "   ${NC}Admin Routes: $BASE_URL/api/admin/*${NC}"

echo -e "\n${YELLOW}🔧 If login fails, try:${NC}"
echo -e "   ${NC}1. Create/recreate admin user: node create-admin.js${NC}"
echo -e "   ${NC}2. Check MongoDB connection: node test-mongo-connection.js${NC}"
echo -e "   ${NC}3. Verify server is running: curl $BASE_URL/health${NC}"