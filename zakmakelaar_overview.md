# Zakmakelaar Backend - Project Overview

**Last Updated:** October 2025  
**Purpose:** LLM-optimized context document for understanding the Zakmakelaar platform architecture, capabilities, and codebase structure.

---

## Executive Summary

Zakmakelaar is a Dutch real estate automation platform that combines web scraping, AI-powered matching, automated application submission, and document management for rental property seekers. The backend is built on Express.js with MongoDB, Redis caching, real-time WebSocket updates, and AI service integration.

---

## Technology Stack

### Core Technologies
- **Runtime:** Node.js with Express.js
- **Database:** MongoDB with Mongoose ODM
- **Cache:** Redis with ioredis client
- **Real-time:** Socket.IO for WebSocket connections
- **Browser Automation:** Puppeteer for scraping and form filling
- **Documentation:** Swagger/OpenAPI
- **Security:** JWT authentication, rate limiting, encryption

### Deployment
- **Containerization:** Docker + docker-compose
- **Services:** MongoDB, Redis, Backend (all containerized)
- **Config Files:** 
  - `zakmakelaar-backend/docker-compose.yml`
  - `zakmakelaar-backend/package.json`

---

## Project Structure

### Entry Points
- **Server Bootstrap:** `zakmakelaar-backend/src/index.js`
- **Configuration:** 
  - `zakmakelaar-backend/src/config/config.js`
  - `zakmakelaar-backend/src/config/database.js`

### Key Directories
```
zakmakelaar-backend/src/
├── routes/           # API endpoint definitions
├── services/         # Business logic layer
├── models/           # Mongoose schemas
├── middleware/       # Auth, rate limiting, validation
└── config/           # Environment and database setup
```

---

## Core Domain Features

### 1. Listings & Search System
**Purpose:** Advanced property search with filtering, caching, and performance optimization

**Key Files:**
- Routes: `zakmakelaar-backend/src/routes/listing.js`
- Service: `zakmakelaar-backend/src/services/searchService.js`
- Model: `zakmakelaar-backend/src/models/Listing.js`

**Capabilities:**
- Advanced multi-criteria filtering
- Quick statistics aggregation
- Search suggestions and autocomplete
- Pagination support
- Redis caching for performance
- Performance metrics collection

**Data Model (Listing):**
- Text fields for price, rooms, size
- Unique URL constraint
- Timestamps for created/updated
- Source platform tracking

### 2. Web Scraping Agent
**Purpose:** Automated property data collection from Dutch rental platforms

**Key Files:**
- Routes: `zakmakelaar-backend/src/routes/scraper.js`
- Service: `zakmakelaar-backend/src/services/scraper.js`

**Supported Platforms:**
- Funda
- Pararius
- Huurwoningen

**Features:**
- Scheduled/periodic scraping
- Manual trigger endpoints
- Scraper metrics and health monitoring
- Integration with auto-application system
- Force-stop and cleanup controls
- Enable/disable individual scrapers

### 3. Authentication & User Management
**Purpose:** Secure user accounts with profiles, preferences, and activity tracking

**Key Files:**
- Routes: `zakmakelaar-backend/src/routes/auth.js`
- Model: `zakmakelaar-backend/src/models/User.js`

**Features:**
- JWT-based authentication
- User profiles and preferences
- Role-based access control
- GDPR consent tracking
- Tenant scoring system
- Activity logging
- Session and device management (model support)

**User Model Highlights:**
- Comprehensive profile fields
- Search and notification preferences
- AI settings and personalization
- Auto-application configuration
- Security and compliance fields
- Indexed for common queries

### 4. AI-Powered Features
**Purpose:** Intelligent property matching, document analysis, and content generation

**Key Files:**
- Routes: `zakmakelaar-backend/src/routes/ai.js`
- Service: `zakmakelaar-backend/src/services/aiService.js`

**Capabilities:**
- **Listing Matching:** AI-powered property recommendations
- **Contract Analysis:** Automated rental agreement review
- **Application Generation:** Personalized application messages
- **Market Analysis:** Rental market insights and trends
- **Translation & Summarization:** Multi-language support

**AI Provider Strategy:**
- Multi-provider fallback system (Gemini → OpenRouter → OpenAI)
- Graceful degradation on provider failures
- Provider selection based on availability

### 5. Auto-Application System
**Purpose:** Automated application submission to rental listings

**Key Files:**
- Routes: `zakmakelaar-backend/src/routes/autoApplication.js`
- Service: `zakmakelaar-backend/src/services/formAutomationEngine.js`
- Model: `zakmakelaar-backend/src/models/AutoApplicationSettings.js`

**Features:**
- Application queue management
- Form detection and intelligent filling
- Anti-detection measures for bot prevention
- Real-time monitoring and status updates
- Results tracking and reporting
- User-configurable settings and criteria

**Settings Model:**
- Rich criteria configuration
- Personal information storage
- Application statistics
- Status tracking
- Helper methods and virtual properties

**⚠️ Known Issue:** Personal info encryption is commented out but still referenced, risking runtime errors or data exposure.

### 6. Document Management
**Purpose:** Secure document storage with encryption and access control

**Key Files:**
- Routes: `zakmakelaar-backend/src/routes/documents.js`
- Service: `zakmakelaar-backend/src/services/documentVaultService.js`

**Features:**
- Encrypted document uploads
- Granular access control
- Verification workflow
- Admin reporting
- Document lifecycle management (upload/list/download/delete)

**⚠️ Critical Issue:** Uses non-existent crypto APIs (`createCipherGCM`/`createDecipherGCM`). Should use `crypto.createCipheriv('aes-256-gcm', key, iv)`.

**Security Concern:** Stores raw per-document encryption keys in database. Should implement envelope encryption (DEK encrypted with master key) or use KMS/HSM.

### 7. Monitoring & Observability
**Purpose:** System health, metrics, and operational dashboards

**Key Files:**
- Routes: `zakmakelaar-backend/src/routes/monitoring.js`
- Service: `zakmakelaar-backend/src/services/logger.js`

**Features:**
- Health check endpoints
- Performance metrics dashboards
- Alert system integration
- Quick-stats monitoring
- Structured logging

### 8. Real-time Updates
**Purpose:** WebSocket-based live notifications and status updates

**Key Files:**
- Service: `zakmakelaar-backend/src/services/websocketService.js`

**Features:**
- User-scoped channels
- Queue status updates
- Application results broadcasting
- Alert notifications
- Real-time event streaming

**⚠️ Improvement Needed:** Add per-user rate limiting, heartbeat/idle timeouts, backpressure handling, and auth checks for room joins.

---

## API Reference

### Base URL Structure
All endpoints prefixed with `/api`

### Endpoint Categories

#### Authentication (`/api/auth`)
- User registration
- Login with JWT
- Current user profile (`/me`)
- Preferences management
- **Security:** Rate-limited, JWT-protected

#### Listings (`/api/listings`, `/api/search`)
- List properties with filters
- Get single listing details
- Quick statistics aggregation
- Search suggestions
- Full-text search
- **Performance:** Redis-cached, monitored

#### Scraper (`/api/scraper`)
- Trigger manual scrape
- Get scraper status and metrics
- Enable/disable individual scrapers
- Agent control commands (start/stop/cleanup)

#### AI Services (`/api/ai`)
- `/match` - Property matching recommendations
- `/contract/analyze` - Contract review
- `/application/generate` - Generate application messages
- `/application/submit` - Submit applications
- `/market/analyze` - Market analysis
- `/listing/summarize` - Listing summarization

#### Auto-Application (`/api/auto-application`)
- Settings management
- Queue operations
- Monitoring dashboard
- Results retrieval
- Status updates

#### Documents (`/api/documents`)
- Upload documents
- List user documents
- Download documents
- Delete documents
- **Admin:** Verify documents, bulk download, reporting

#### Monitoring (`/api/monitoring`)
- System health checks
- Metrics dashboards
- Performance statistics
- Quick-stats health monitoring

### API Documentation
- **Swagger UI:** Available at `/api-docs`
- **Format:** OpenAPI/Swagger embedded in route files
- **Wiring:** Configured in `zakmakelaar-backend/src/index.js`

---

## Infrastructure & Services

### Caching Strategy
**Service:** `zakmakelaar-backend/src/services/cacheService.js`

- Redis-based caching
- Connection health monitoring
- TTL (Time-To-Live) helpers
- Cache invalidation patterns

### Task Scheduling
- **Scraping:** Periodic execution with configurable intervals
- **Auto-application:** Queue processing with concurrency control
- **Cleanup:** Graceful shutdown and resource cleanup
- **Implementation:** Native schedulers in scraper service and index.js

**⚠️ Recommended Improvement:** Migrate to BullMQ job queue for better concurrency, retries, backoff, and observability.

### Frontend Compatibility
**Service:** `zakmakelaar-backend/src/services/frontendCompatibilityLayer.js`

- Schema transformation for legacy frontend support
- Unified property schema migration path
- API response normalization

---

## Architecture Strengths

### 1. Error Handling
- Robust error handling in quick-stats aggregation
- Graceful degradation in AI service fallbacks
- Comprehensive error middleware

### 2. Code Organization
- Clear separation of concerns (routes/services/middleware)
- Modular service architecture
- Consistent naming conventions

### 3. Documentation
- Swagger documentation embedded in routes
- Clear API contracts
- Self-documenting endpoints

### 4. Real-time Capabilities
- WebSocket integration for live updates
- User-scoped event channels
- Scalable notification system

---

## Known Issues & Technical Debt

### 🔴 Critical Issues

#### 1. Document Encryption Bug
**Location:** `zakmakelaar-backend/src/services/documentVaultService.js:500`

**Problem:** Uses non-existent crypto APIs
```javascript
// Current (BROKEN):
createCipherGCM / createDecipherGCM

// Should be:
crypto.createCipheriv('aes-256-gcm', key, iv)
crypto.createDecipheriv('aes-256-gcm', key, iv)
```

**Impact:** Document encryption/decryption fails at runtime

**Additional Concern:** Auth tag handling needs verification

#### 2. Insecure Key Storage
**Location:** Document vault service

**Problem:** Raw per-document encryption keys stored in database

**Solution Required:** Implement envelope encryption
- Generate per-document DEK (Data Encryption Key)
- Encrypt DEK with master key from environment/KMS
- Store only encrypted DEK in database
- Alternative: Use KMS/HSM for key management

#### 3. Auto-Application Encryption Inconsistency
**Location:** `zakmakelaar-backend/src/models/AutoApplicationSettings.js`

**Problem:** 
- Personal info encryption code commented out
- Methods still reference `encryptionService`
- Risk of runtime errors or plain-text data leakage

**Solution:** Either implement proper encryption or remove dead code references

### 🟡 Performance Issues

#### 1. Inefficient Numeric Queries
**Location:** `zakmakelaar-backend/src/services/searchService.js`

**Problem:** 
- Price, rooms, size stored as strings
- Runtime conversion with `$addFields` on every query
- Missing indexes for range queries and sorting

**Solution:**
```javascript
// Add to Listing schema:
priceNumeric: Number,
roomsNumeric: Number,
sizeNumeric: Number

// Add indexes:
{ priceNumeric: 1 }
{ roomsNumeric: 1 }
{ sizeNumeric: 1 }

// Update scrapers to populate numeric fields
```

**Impact:** Faster range queries, better sort performance, reduced CPU usage

### 🟡 Security Improvements

#### 1. Authentication Enhancements
**Current State:** Basic JWT auth with rate limiting

**Recommended Additions:**
- Two-factor authentication (2FA) flows
- Session management endpoints (device list, revocation)
- Device fingerprinting and management
- Consistent per-user rate limits across sensitive routes

#### 2. CORS & CSP Hardening
**Location:** `zakmakelaar-backend/src/index.js`

**Issues:**
- Generic CORS configuration
- `'unsafe-inline'` in Content Security Policy

**Improvements:**
- Environment-specific CORS whitelist
- Stricter CSP without unsafe-inline
- CSP reporting endpoint

#### 3. Rate Limiting
**Location:** `zakmakelaar-backend/src/middleware/rateLimiter.js`

**Current:** Global rate limits

**Needed:** Per-user, per-endpoint granular limits for sensitive operations

### 🟢 Architectural Improvements

#### 1. Job Queue Migration
**Current:** Native schedulers with limited features

**Recommended:** Migrate to BullMQ
- Better concurrency control
- Built-in retries and exponential backoff
- Job observability and monitoring
- Priority queues
- Rate limiting per job type

**Affected Services:**
- Scraper periodic jobs
- Auto-application queue processing

#### 2. Browser Automation Resilience
**Current:** Direct Puppeteer usage

**Improvements:**
- Centralized browser pooling
- `puppeteer-extra-stealth` for anti-detection
- Per-site SLA monitoring
- Failure alerts and circuit breakers

#### 3. Observability Enhancement
**Current:** Basic health checks and logging

**Recommended Additions:**
- Prometheus metrics export
  - HTTP request durations
  - Queue sizes and processing times
  - Scraper success/failure rates
  - Cache hit/miss ratios
- Structured request IDs (correlation tracking)
- `/metrics` endpoint for scraping
- Grafana dashboards

#### 4. Testing Coverage
**Current:** Jest and mongodb-memory-server present but no tests

**Needed:**
- Integration tests for:
  - Auth flows (register, login, JWT refresh)
  - Listing search with various filters
  - Quick-stats aggregation
  - Document upload/download
  - AI service fallback scenarios
- CI pipeline to run docker-compose services
- Automated test execution on pull requests

#### 5. API Schema Evolution
**Current:** Mixed schema formats with compatibility layer

**Goal:** 
- Migrate fully to unified property schema
- Remove branching logic in controllers
- Publish OpenAPI schemas for all unified objects
- Version API with `/api/v1`, `/api/v2` pattern

**Related:** `zakmakelaar-backend/src/services/frontendCompatibilityLayer.js`

---

## Development Guidelines

### When Adding New Features
1. **Route Definition:** Add to appropriate route file in `src/routes/`
2. **Business Logic:** Implement in service layer (`src/services/`)
3. **Data Model:** Define/update Mongoose schema in `src/models/`
4. **Middleware:** Add auth, validation, rate limiting as needed
5. **Documentation:** Include Swagger annotations in route file
6. **Caching:** Consider Redis caching for expensive operations
7. **Real-time:** Add WebSocket events if live updates needed
8. **Testing:** Write integration tests for new endpoints

### Code Patterns to Follow
- **Error Handling:** Use try-catch with proper error responses
- **Async/Await:** Prefer over callback hell
- **Service Layer:** Keep routes thin, logic in services
- **Validation:** Use middleware for input validation
- **Logging:** Use structured logging service
- **Configuration:** Environment variables for all config

### Code Patterns to Avoid
- **Direct Database Queries in Routes:** Use service layer
- **Synchronous Crypto Operations:** Use async variants
- **Storing Secrets in Code:** Use environment variables
- **Commented-Out Code:** Remove or implement properly
- **Hardcoded Values:** Use configuration

---

## Quick Reference: File Locations

### Configuration
- Server entry: `zakmakelaar-backend/src/index.js`
- App config: `zakmakelaar-backend/src/config/config.js`
- Database: `zakmakelaar-backend/src/config/database.js`
- Docker: `zakmakelaar-backend/docker-compose.yml`
- Dependencies: `zakmakelaar-backend/package.json`

### Routes (API Endpoints)
- Auth: `zakmakelaar-backend/src/routes/auth.js`
- Listings: `zakmakelaar-backend/src/routes/listing.js`
- Scraper: `zakmakelaar-backend/src/routes/scraper.js`
- AI: `zakmakelaar-backend/src/routes/ai.js`
- Auto-app: `zakmakelaar-backend/src/routes/autoApplication.js`
- Documents: `zakmakelaar-backend/src/routes/documents.js`
- Monitoring: `zakmakelaar-backend/src/routes/monitoring.js`

### Services (Business Logic)
- Search: `zakmakelaar-backend/src/services/searchService.js`
- Scraper: `zakmakelaar-backend/src/services/scraper.js`
- AI: `zakmakelaar-backend/src/services/aiService.js`
- Form automation: `zakmakelaar-backend/src/services/formAutomationEngine.js`
- Document vault: `zakmakelaar-backend/src/services/documentVaultService.js`
- Cache: `zakmakelaar-backend/src/services/cacheService.js`
- WebSocket: `zakmakelaar-backend/src/services/websocketService.js`
- Logger: `zakmakelaar-backend/src/services/logger.js`
- Frontend compat: `zakmakelaar-backend/src/services/frontendCompatibilityLayer.js`

### Models (Data Schemas)
- User: `zakmakelaar-backend/src/models/User.js`
- Listing: `zakmakelaar-backend/src/models/Listing.js`
- Auto-app settings: `zakmakelaar-backend/src/models/AutoApplicationSettings.js`

### Middleware
- Rate limiter: `zakmakelaar-backend/src/middleware/rateLimiter.js`

---

## Future Roadmap Considerations

### Near-term (1-3 months)
1. Fix critical encryption bugs
2. Add numeric fields and indexes for listings
3. Implement comprehensive test suite
4. Set up CI/CD pipeline
5. Add Prometheus metrics

### Mid-term (3-6 months)
1. Migrate to BullMQ job queue
2. Implement 2FA and session management
3. Complete unified schema migration
4. Add Grafana dashboards
5. Browser pooling and stealth improvements

### Long-term (6-12 months)
1. Microservices extraction (scraper, AI as separate services)
2. Implement KMS for encryption key management
3. Multi-tenancy support
4. API versioning strategy
5. Horizontal scaling architecture

---

## Contact & Support

- **API Documentation:** `/api-docs` (Swagger UI)
- **Support:** `https://support.claude.com`
- **Developer Docs:** `https://docs.claude.com`

---

## Document Metadata

**Version:** 1.0  
**Format:** Markdown (LLM-optimized)  
**Intended Use:** Providing comprehensive context to LLMs for code assistance, debugging, and feature development  
**Maintenance:** Update when major architectural changes occur