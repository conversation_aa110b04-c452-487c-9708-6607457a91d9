const cheerio = require('cheerio');
const BaseScraper = require('./BaseScraper');
const { getScraperConfig } = require('./configs/scraperConfigs');
const { ErrorClassifier, ErrorRecovery, errorMetrics } = require('./errors/ScrapingErrors');
const { rateLimiter, antiDetection } = require('./utils/RateLimiter');

/**
 * Enhanced Pararius scraper using the new architecture
 */
class ParariusScraper extends BaseScraper {
  constructor() {
    const config = getScraperConfig('pararius');
    super('pararius', config);
  }

  /**
   * Get site-specific selectors
   */
  getSelectors() {
    return this.config.selectors;
  }

  /**
   * Generate search URLs based on configuration
   */
  getSearchUrls() {
    // Use the same URL pattern as the working V1 scraper
    return ['https://www.pararius.nl/huurwoningen/nederland'];
  }

  /**
   * Configure page with site-specific settings
   */
  async configurePage(page) {
    // Apply anti-detection measures
    await antiDetection.setupAntiDetection(page, this.siteName);

    // Set Pararius-specific headers
    await page.setExtraHTTPHeaders({
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.9,nl;q=0.8',
      'Referer': 'https://www.pararius.com/'
    });

    // Allow all resources to load properly
    // Note: Resource blocking disabled to ensure page content loads
  }

  /**
   * Extract listing data from a single listing element
   */
  async extractListingData($, element) {
    try {
      const listing = {
        source: 'pararius',
        scrapedAt: new Date()
      };

      // Extract basic information
      listing.title = this.extractText(element, this.selectors.title);
      listing.location = this.extractText(element, this.selectors.location);
      listing.price = this.extractPrice($, element);
      
      // Extract URL
      const urlElement = element.find(this.selectors.url);
      if (urlElement.length) {
        const relativeUrl = urlElement.attr('href');
        listing.url = relativeUrl ? 
          (relativeUrl.startsWith('http') ? relativeUrl : `${this.config.baseUrl}${relativeUrl}`) : null;
      }

      // Extract image
      const imageElement = element.find(this.selectors.image);
      if (imageElement.length) {
        listing.image = imageElement.attr('src') || imageElement.attr('data-src');
      }

      // Extract size and rooms from listing preview
      listing.size = this.extractText(element, this.selectors.size);
      listing.rooms = this.extractText(element, this.selectors.rooms);

      // Clean up extracted data
      listing.title = this.cleanTitle(listing.title);
      listing.location = this.cleanLocation(listing.location);
      listing.size = this.cleanSize(listing.size);
      listing.rooms = this.cleanRooms(listing.rooms);

      // Fetch additional details if URL is available
      if (listing.url && this.shouldFetchDetails()) {
        try {
          await rateLimiter.waitForDelay(this.siteName);
          const detailData = await this.fetchListingDetails(listing.url);
          Object.assign(listing, detailData);
        } catch (error) {
          console.warn(`Failed to fetch details for ${listing.url}:`, error.message);
        }
      }

      return this.validateListing(listing) ? listing : null;

    } catch (error) {
      console.error('Error extracting Pararius listing:', error.message);
      return null;
    }
  }

  /**
   * Fetch detailed information from listing page
   */
  async fetchListingDetails(url) {
    const browser = await this.getBrowserInstance();
    let page = null;

    try {
      page = await browser.newPage();
      await this.configurePage(page);

      await page.goto(url, {
        waitUntil: "networkidle2",
        timeout: this.config.timeout,
      });

      // Add human-like behavior
      await antiDetection.addHumanBehavior(page);

      const html = await page.content();
      const $ = cheerio.load(html);

      const details = {};

      // Extract detailed information using configured selectors
      const { detailSelectors } = this.selectors;

      // Description
      details.description = this.extractText($, detailSelectors.description);
      if (details.description) {
        details.description = details.description.substring(0, 1000);
      }

      // Extract property features from the features list
      $(detailSelectors.features).each((i, el) => {
        const label = this.extractText($(el), detailSelectors.featureLabel).toLowerCase();
        const value = this.extractText($(el), detailSelectors.featureValue);
        
        if (value) {
          this.mapFeatureToProperty(label, value, details);
        }
      });

      // Extract images
      details.images = [];
      $('img[src*="pararius"], img[data-src*="pararius"]').each((i, img) => {
        const src = $(img).attr('src') || $(img).attr('data-src');
        if (src && !details.images.includes(src)) {
          details.images.push(src);
        }
      });

      return details;

    } catch (error) {
      const classifiedError = ErrorClassifier.classify(error, this.siteName);
      errorMetrics.recordError(classifiedError, this.siteName);
      
      console.error(`Error fetching Pararius details from ${url}:`, error.message);
      throw classifiedError;
    } finally {
      if (page) {
        await page.close().catch(() => {});
      }
    }
  }

  /**
   * Map feature labels to property fields
   */
  mapFeatureToProperty(label, value, details) {
    if (label.includes('floor area') || label.includes('woonoppervlakte')) {
      details.size = value;
    } else if (label.includes('bedrooms') || label.includes('slaapkamers')) {
      details.bedrooms = value;
    } else if (label.includes('rooms') || label.includes('kamers')) {
      details.rooms = value;
    } else if (label.includes('construction year') || label.includes('bouwjaar')) {
      details.year = value;
    } else if (label.includes('interior') || label.includes('interieur') || label.includes('furnishing')) {
      details.interior = this.normalizeInterior(value);
    } else if (label.includes('available from') || label.includes('beschikbaar vanaf')) {
      details.availableFrom = value;
    } else if (label.includes('energy label') || label.includes('energielabel')) {
      details.energyLabel = value;
    } else if (label.includes('type') || label.includes('soort')) {
      details.propertyType = value;
    } else if (label.includes('garden') || label.includes('tuin')) {
      details.garden = value;
    } else if (label.includes('parking') || label.includes('parkeren')) {
      details.parking = value;
    }
  }

  /**
   * Normalize interior/furnishing terms
   */
  normalizeInterior(interior) {
    const normalized = interior.toLowerCase();
    if (normalized.includes('furnished') || normalized.includes('gemeubileerd')) {
      return 'Gemeubileerd';
    } else if (normalized.includes('unfurnished') || normalized.includes('kaal')) {
      return 'Kaal';
    } else if (normalized.includes('semi-furnished') || normalized.includes('gestoffeerd')) {
      return 'Gestoffeerd';
    }
    return interior;
  }

  /**
   * Extract listing data from a single listing element
   */
  async extractListingData($, element) {
    try {
      const titleElement = element.find(this.selectors.title);
      const linkElement = element.find(this.selectors.link);
      const priceElement = element.find(this.selectors.price);
      const locationElement = element.find(this.selectors.location);
      const imageElement = element.find(this.selectors.image);

      const title = titleElement.text().trim();
      const link = linkElement.attr('href');
      const price = priceElement.text().trim();
      const location = locationElement.text().trim();
      const image = imageElement.attr('src') || imageElement.attr('data-src');

      if (!title || !link) {
        return null;
      }

      // Ensure absolute URL
      const fullLink = link.startsWith('http') ? link : `https://www.pararius.nl${link}`;

      return {
        title: this.cleanTitle(title),
        price: this.extractPrice(price),
        location: this.cleanLocation(location),
        link: fullLink,
        image: image ? (image.startsWith('http') ? image : `https:${image}`) : null,
        site: 'pararius',
        dateScraped: new Date()
      };
    } catch (error) {
      console.error('Error extracting Pararius listing:', error.message);
      return null;
    }
  }

  /**
   * Clean extracted title
   */
  cleanTitle(title) {
    if (!title) return null;
    return title.replace(/\s+/g, ' ').trim();
  }

  /**
   * Clean extracted location
   */
  cleanLocation(location) {
    if (!location) return null;
    return location.replace(/\s+/g, ' ').trim();
  }

  /**
   * Clean extracted size
   */
  cleanSize(size) {
    if (!size) return null;
    const match = size.match(/(\d+)\s*m²/);
    return match ? `${match[1]} m²` : size;
  }

  /**
   * Clean extracted rooms
   */
  cleanRooms(rooms) {
    if (!rooms) return null;
    const match = rooms.match(/(\d+)/);
    return match ? match[1] : rooms;
  }

  /**
   * Enhanced scraping with error handling
   */
  async scrape(retryCount = 0) {
    try {
      await rateLimiter.waitForDelay(this.siteName);
      return await super.scrape(retryCount);
      
    } catch (error) {
      const classifiedError = ErrorClassifier.classify(error, this.siteName);
      errorMetrics.recordError(classifiedError, this.siteName);
      
      const recovery = await ErrorRecovery.handleError(classifiedError, {
        siteName: this.siteName,
        retryCount,
        maxRetries: this.config.retries
      });

      if (recovery.shouldRetry && retryCount < this.config.retries) {
        console.log(`🔄 Applying recovery strategy for ${this.siteName}`);
        
        if (recovery.increaseTimeout) {
          this.config.timeout = Math.min(this.config.timeout * 1.5, 60000);
        }
        
        if (recovery.reduceSpeed) {
          this.config.delayMin *= 1.5;
          this.config.delayMax *= 1.5;
        }

        rateLimiter.recordError(this.siteName, classifiedError);
        return this.scrape(retryCount + 1);
      }

      throw classifiedError;
    }
  }

  /**
   * Validate Pararius-specific listing data
   */
  validateListing(listing) {
    if (!super.validateListing(listing)) {
      return false;
    }

    // Pararius-specific validations
    if (!listing.url || !listing.url.includes('pararius.com')) {
      return false;
    }

    return true;
  }

  /**
   * Determine if we should fetch detailed information
   */
  shouldFetchDetails() {
    return Math.random() < 0.4; // Fetch details for 40% of listings
  }

  /**
   * Get browser instance
   */
  async getBrowserInstance() {
    const { browserPool } = require('../scraperUtils');
    return browserPool.getBrowser();
  }

  /**
   * Override scrape page to add Pararius-specific logic
   */
  async scrapePage(browser, url) {
    try {
      await rateLimiter.waitForDelay(this.siteName);
      return await super.scrapePage(browser, url);
      
    } catch (error) {
      const classifiedError = ErrorClassifier.classify(error, this.siteName);
      rateLimiter.recordError(this.siteName, classifiedError);
      throw classifiedError;
    }
  }
}

// Export both class and factory function
const createParariusScraper = () => new ParariusScraper();

module.exports = {
  ParariusScraper,
  createParariusScraper,
  // Export default scraping function for backward compatibility
  scrapePararius: async () => {
    const scraper = new ParariusScraper();
    return scraper.scrape();
  }
};
