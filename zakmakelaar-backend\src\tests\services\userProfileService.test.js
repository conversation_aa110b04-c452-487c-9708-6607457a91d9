const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const User = require('../../models/User');
const userProfileService = require('../../services/userProfileService');
const documentVaultService = require('../../services/documentVaultService');

// Mock the document vault service
jest.mock('../../services/documentVaultService');

describe('UserProfileService', () => {
  let testUser;
  let userId;
  let mongoServer;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    await mongoose.connect(mongoServer.getUri(), {
      dbName: 'zakmakelaar-test-user-profile',
    });
  });

  afterAll(async () => {
    await mongoose.connection.close();
    if (mongoServer) {
      await mongoServer.stop();
    }
  });

  beforeEach(async () => {
    // Create a test user
    testUser = new User({
      email: '<EMAIL>',
      password: 'password123',
      role: 'user'
    });
    await testUser.save();
    userId = testUser._id.toString();
  });

  afterEach(async () => {
    await User.deleteMany({});
    jest.clearAllMocks();
  });

  describe('initializeAutoApplicationProfile', () => {
    it('should initialize auto-application profile for new user', async () => {
      const result = await userProfileService.initializeAutoApplicationProfile(userId);

      expect(result).toHaveProperty('enabled', false);
      expect(result).toHaveProperty('settings');
      expect(result).toHaveProperty('criteria');
      expect(result).toHaveProperty('personalInfo');
      expect(result).toHaveProperty('requiredDocuments');
      expect(result).toHaveProperty('profileCompleteness');
      expect(result).toHaveProperty('applicationHistory');

      // Check required documents are initialized
      expect(result.requiredDocuments).toHaveLength(4);
      expect(result.requiredDocuments.map(doc => doc.type)).toEqual([
        'income_proof', 'employment_contract', 'bank_statement', 'id_document'
      ]);
    });

    it('should not reinitialize existing profile', async () => {
      // Initialize once
      await userProfileService.initializeAutoApplicationProfile(userId);
      
      // Update settings
      await userProfileService.updateSettings(userId, { maxApplicationsPerDay: 10 });
      
      // Initialize again
      const result = await userProfileService.initializeAutoApplicationProfile(userId);
      
      expect(result.settings.maxApplicationsPerDay).toBe(10);
    });

    it('should throw error for non-existent user', async () => {
      const fakeUserId = new mongoose.Types.ObjectId().toString();
      
      await expect(userProfileService.initializeAutoApplicationProfile(fakeUserId))
        .rejects.toThrow('User not found');
    });
  });

  describe('updatePersonalInfo', () => {
    beforeEach(async () => {
      await userProfileService.initializeAutoApplicationProfile(userId);
    });

    it('should update personal information successfully', async () => {
      const personalInfo = {
        fullName: 'John Doe',
        email: '<EMAIL>',
        phone: '+***********',
        dateOfBirth: new Date('1990-01-01'),
        nationality: 'Dutch',
        occupation: 'Software Engineer',
        employer: 'Tech Company',
        monthlyIncome: 5000,
        moveInDate: new Date('2024-03-01'),
        leaseDuration: 12,
        numberOfOccupants: 1
      };

      const result = await userProfileService.updatePersonalInfo(userId, personalInfo);

      expect(result.personalInfo).toMatchObject(personalInfo);
      expect(result.profileCompleteness.personalInfo).toBeGreaterThan(0);
    });

    it('should validate email format', async () => {
      const personalInfo = {
        email: 'invalid-email'
      };

      await expect(userProfileService.updatePersonalInfo(userId, personalInfo))
        .rejects.toThrow('Invalid email format');
    });

    it('should validate phone number format', async () => {
      const personalInfo = {
        phone: 'invalid-phone'
      };

      await expect(userProfileService.updatePersonalInfo(userId, personalInfo))
        .rejects.toThrow('Invalid phone number format');
    });

    it('should validate age range', async () => {
      const personalInfo = {
        dateOfBirth: new Date('2010-01-01') // Too young
      };

      await expect(userProfileService.updatePersonalInfo(userId, personalInfo))
        .rejects.toThrow('Age must be between 18 and 100 years');
    });

    it('should validate monthly income', async () => {
      const personalInfo = {
        monthlyIncome: -1000
      };

      await expect(userProfileService.updatePersonalInfo(userId, personalInfo))
        .rejects.toThrow('Monthly income cannot be negative');
    });

    it('should validate lease duration', async () => {
      const personalInfo = {
        leaseDuration: 100 // Too long
      };

      await expect(userProfileService.updatePersonalInfo(userId, personalInfo))
        .rejects.toThrow('Lease duration must be between 1 and 60 months');
    });

    it('should validate number of occupants', async () => {
      const personalInfo = {
        numberOfOccupants: 15 // Too many
      };

      await expect(userProfileService.updatePersonalInfo(userId, personalInfo))
        .rejects.toThrow('Number of occupants must be between 1 and 10');
    });
  });

  describe('updateSettings', () => {
    beforeEach(async () => {
      await userProfileService.initializeAutoApplicationProfile(userId);
    });

    it('should update settings successfully', async () => {
      const settings = {
        maxApplicationsPerDay: 10,
        applicationTemplate: 'student',
        autoSubmit: true,
        requireManualReview: false
      };

      const result = await userProfileService.updateSettings(userId, settings);

      expect(result.settings).toMatchObject(settings);
    });

    it('should validate max applications per day', async () => {
      const settings = {
        maxApplicationsPerDay: 25 // Too many
      };

      await expect(userProfileService.updateSettings(userId, settings))
        .rejects.toThrow('Max applications per day must be between 1 and 20');
    });

    it('should validate application template', async () => {
      const settings = {
        applicationTemplate: 'invalid-template'
      };

      await expect(userProfileService.updateSettings(userId, settings))
        .rejects.toThrow('Invalid application template');
    });
  });

  describe('updateCriteria', () => {
    beforeEach(async () => {
      await userProfileService.initializeAutoApplicationProfile(userId);
    });

    it('should update criteria successfully', async () => {
      const criteria = {
        maxPrice: 2000,
        minRooms: 2,
        maxRooms: 4,
        propertyTypes: ['apartment', 'house'],
        locations: ['Amsterdam', 'Utrecht'],
        excludeKeywords: ['no pets'],
        includeKeywords: ['balcony']
      };

      const result = await userProfileService.updateCriteria(userId, criteria);

      expect(result.criteria).toMatchObject(criteria);
    });

    it('should validate price range', async () => {
      const criteria = {
        maxPrice: -1000
      };

      await expect(userProfileService.updateCriteria(userId, criteria))
        .rejects.toThrow('Max price cannot be negative');
    });

    it('should validate room range', async () => {
      const criteria = {
        minRooms: 5,
        maxRooms: 3
      };

      await expect(userProfileService.updateCriteria(userId, criteria))
        .rejects.toThrow('Min rooms cannot be greater than max rooms');
    });
  });

  describe('checkDocumentRequirements', () => {
    beforeEach(async () => {
      await userProfileService.initializeAutoApplicationProfile(userId);
    });

    it('should check document requirements with no uploaded documents', async () => {
      documentVaultService.getDocuments.mockResolvedValue([]);

      const result = await userProfileService.checkDocumentRequirements(userId);

      expect(result.allRequiredUploaded).toBe(false);
      expect(result.missingDocuments).toHaveLength(4);
      expect(result.documentCompleteness).toBe(0);
    });

    it('should check document requirements with some uploaded documents', async () => {
      documentVaultService.getDocuments.mockResolvedValue([
        { id: 'doc1', type: 'income_proof' },
        { id: 'doc2', type: 'id_document' }
      ]);

      const result = await userProfileService.checkDocumentRequirements(userId);

      expect(result.allRequiredUploaded).toBe(false);
      expect(result.missingDocuments).toHaveLength(2);
      expect(result.missingDocuments).toEqual(['employment_contract', 'bank_statement']);
      expect(result.documentCompleteness).toBe(50);
    });

    it('should check document requirements with all uploaded documents', async () => {
      documentVaultService.getDocuments.mockResolvedValue([
        { id: 'doc1', type: 'income_proof' },
        { id: 'doc2', type: 'employment_contract' },
        { id: 'doc3', type: 'bank_statement' },
        { id: 'doc4', type: 'id_document' }
      ]);

      const result = await userProfileService.checkDocumentRequirements(userId);

      expect(result.allRequiredUploaded).toBe(true);
      expect(result.missingDocuments).toHaveLength(0);
      expect(result.documentCompleteness).toBe(100);
    });
  });

  describe('calculateProfileCompleteness', () => {
    beforeEach(async () => {
      await userProfileService.initializeAutoApplicationProfile(userId);
    });

    it('should calculate completeness with empty profile', async () => {
      const result = await userProfileService.calculateProfileCompleteness(userId);

      expect(result.personalInfo).toBe(0);
      expect(result.documents).toBe(0);
      expect(result.overall).toBe(0);
    });

    it('should calculate completeness with partial personal info', async () => {
      await userProfileService.updatePersonalInfo(userId, {
        fullName: 'John Doe',
        email: '<EMAIL>',
        phone: '+***********',
        dateOfBirth: new Date('1990-01-01'),
        nationality: 'Dutch'
      });

      const result = await userProfileService.calculateProfileCompleteness(userId);

      expect(result.personalInfo).toBe(45); // 5 out of 11 fields
      expect(result.documents).toBe(0);
      expect(result.overall).toBe(27); // 45 * 0.6 + 0 * 0.4
    });

    it('should calculate completeness with complete profile', async () => {
      // Complete personal info
      await userProfileService.updatePersonalInfo(userId, {
        fullName: 'John Doe',
        email: '<EMAIL>',
        phone: '+***********',
        dateOfBirth: new Date('1990-01-01'),
        nationality: 'Dutch',
        occupation: 'Software Engineer',
        employer: 'Tech Company',
        monthlyIncome: 5000,
        moveInDate: new Date('2024-03-01'),
        leaseDuration: 12,
        numberOfOccupants: 1
      });

      // Mock all documents uploaded
      documentVaultService.getDocuments.mockResolvedValue([
        { id: 'doc1', type: 'income_proof' },
        { id: 'doc2', type: 'employment_contract' },
        { id: 'doc3', type: 'bank_statement' },
        { id: 'doc4', type: 'id_document' }
      ]);

      await userProfileService.checkDocumentRequirements(userId);
      const result = await userProfileService.calculateProfileCompleteness(userId);

      expect(result.personalInfo).toBe(100);
      expect(result.documents).toBe(100);
      expect(result.overall).toBe(100);
    });
  });

  describe('getProfileGuidance', () => {
    beforeEach(async () => {
      await userProfileService.initializeAutoApplicationProfile(userId);
    });

    it('should provide guidance for empty profile', async () => {
      const result = await userProfileService.getProfileGuidance(userId);

      expect(result.nextSteps).toHaveLength(2);
      expect(result.nextSteps[0].type).toBe('personal_info');
      expect(result.nextSteps[1].type).toBe('documents');
      expect(result.warnings).toHaveLength(1);
      expect(result.warnings[0].type).toBe('low_completeness');
    });

    it('should provide guidance for partially complete profile', async () => {
      // Add some personal info
      await userProfileService.updatePersonalInfo(userId, {
        fullName: 'John Doe',
        email: '<EMAIL>',
        phone: '+***********',
        dateOfBirth: new Date('1990-01-01'),
        nationality: 'Dutch',
        occupation: 'Software Engineer'
      });

      const result = await userProfileService.getProfileGuidance(userId);

      expect(result.warnings).toHaveLength(1);
      expect(result.warnings[0].type).toBe('medium_completeness');
    });

    it('should provide ready guidance for complete profile', async () => {
      // Complete personal info
      await userProfileService.updatePersonalInfo(userId, {
        fullName: 'John Doe',
        email: '<EMAIL>',
        phone: '+***********',
        dateOfBirth: new Date('1990-01-01'),
        nationality: 'Dutch',
        occupation: 'Software Engineer',
        employer: 'Tech Company',
        monthlyIncome: 5000,
        moveInDate: new Date('2024-03-01'),
        leaseDuration: 12,
        numberOfOccupants: 1
      });

      // Mock all documents uploaded
      documentVaultService.getDocuments.mockResolvedValue([
        { id: 'doc1', type: 'income_proof' },
        { id: 'doc2', type: 'employment_contract' },
        { id: 'doc3', type: 'bank_statement' },
        { id: 'doc4', type: 'id_document' }
      ]);

      await userProfileService.checkDocumentRequirements(userId);
      const result = await userProfileService.getProfileGuidance(userId);

      expect(result.recommendations).toHaveLength(1);
      expect(result.recommendations[0].type).toBe('ready');
    });
  });

  describe('setAutoApplicationEnabled', () => {
    beforeEach(async () => {
      await userProfileService.initializeAutoApplicationProfile(userId);
    });

    it('should enable auto-application for ready profile', async () => {
      // Complete personal info
      await userProfileService.updatePersonalInfo(userId, {
        fullName: 'John Doe',
        email: '<EMAIL>',
        phone: '+***********',
        dateOfBirth: new Date('1990-01-01'),
        nationality: 'Dutch',
        occupation: 'Software Engineer',
        employer: 'Tech Company',
        monthlyIncome: 5000,
        moveInDate: new Date('2024-03-01'),
        leaseDuration: 12,
        numberOfOccupants: 1
      });

      // Mock all documents uploaded
      documentVaultService.getDocuments.mockResolvedValue([
        { id: 'doc1', type: 'income_proof' },
        { id: 'doc2', type: 'employment_contract' },
        { id: 'doc3', type: 'bank_statement' },
        { id: 'doc4', type: 'id_document' }
      ]);

      await userProfileService.checkDocumentRequirements(userId);
      const result = await userProfileService.setAutoApplicationEnabled(userId, true);

      expect(result.enabled).toBe(true);
    });

    it('should not enable auto-application for incomplete profile', async () => {
      await expect(userProfileService.setAutoApplicationEnabled(userId, true))
        .rejects.toThrow('Profile not ready for auto-application');
    });

    it('should disable auto-application regardless of profile status', async () => {
      const result = await userProfileService.setAutoApplicationEnabled(userId, false);

      expect(result.enabled).toBe(false);
    });
  });

  describe('Virtual fields', () => {
    beforeEach(async () => {
      await userProfileService.initializeAutoApplicationProfile(userId);
    });

    it('should calculate isAutoApplicationReady correctly', async () => {
      let user = await User.findById(userId);
      expect(user.isAutoApplicationReady).toBe(false);

      // Complete profile
      await userProfileService.updatePersonalInfo(userId, {
        fullName: 'John Doe',
        email: '<EMAIL>',
        phone: '+***********',
        dateOfBirth: new Date('1990-01-01'),
        nationality: 'Dutch',
        occupation: 'Software Engineer',
        employer: 'Tech Company',
        monthlyIncome: 5000,
        moveInDate: new Date('2024-03-01'),
        leaseDuration: 12,
        numberOfOccupants: 1
      });

      documentVaultService.getDocuments.mockResolvedValue([
        { id: 'doc1', type: 'income_proof' },
        { id: 'doc2', type: 'employment_contract' },
        { id: 'doc3', type: 'bank_statement' },
        { id: 'doc4', type: 'id_document' }
      ]);

      await userProfileService.checkDocumentRequirements(userId);
      
      user = await User.findById(userId);
      expect(user.isAutoApplicationReady).toBe(true);
    });

    it('should calculate canApplyToday correctly', async () => {
      let user = await User.findById(userId);
      expect(user.canApplyToday).toBe(false); // Not enabled

      user.autoApplication.enabled = true;
      await user.save();

      user = await User.findById(userId);
      expect(user.canApplyToday).toBe(true); // Enabled and under daily limit

      // Simulate reaching daily limit
      user.autoApplication.applicationHistory.dailyApplicationCount = 5;
      user.autoApplication.applicationHistory.dailyResetDate = new Date();
      await user.save();

      user = await User.findById(userId);
      expect(user.canApplyToday).toBe(false); // At daily limit
    });

    it('should calculate autoApplicationSuccessRate correctly', async () => {
      let user = await User.findById(userId);
      expect(user.autoApplicationSuccessRate).toBe(0); // No applications

      user.autoApplication.applicationHistory.totalApplications = 10;
      user.autoApplication.applicationHistory.successfulApplications = 3;
      await user.save();

      user = await User.findById(userId);
      expect(user.autoApplicationSuccessRate).toBe(30);
    });
  });
});
