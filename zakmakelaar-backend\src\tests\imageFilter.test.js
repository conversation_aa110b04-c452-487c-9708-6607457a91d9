/**
 * Test suite for image filtering utility
 * 
 * Tests the filtering of placeholder images (SVG logos, map tiles) to ensure
 * only valid property photos are returned to the frontend.
 */

const {
  isPlaceholderImage,
  isValidPropertyImage,
  filterPlaceholderImages,
  filterImagesWithFallback,
  getFilteringStats
} = require('../utils/imageFilter');

describe('Image Filter Utility', () => {
  // Test data based on the provided examples
  const testImages = {
    placeholders: [
      'https://assets.fstatic.nl/shared/images/funda-logo-blue.svg',
      'https://marketinsightsassets.funda.nl/maps/<EMAIL>',
      'https://example.com/logo.png',
      'https://example.com/site-logo.svg',
      'https://example.com/brand-image.jpg',
      'https://example.com/icon_32x32.png'
    ],
    validImages: [
      'https://cloud.funda.nl/valentina_media/216/599/040_720x480.jpg',
      'https://cloud.funda.nl/valentina_media/216/599/041_720x480.jpg',
      'https://cloud.funda.nl/valentina_media/216/599/042_1080x720.jpg',
      'https://example.com/property_photo_800x600.png',
      'https://example.com/house_interior.webp',
      'https://example.com/garden_view_1920x1080.jpg'
    ]
  };

  describe('isPlaceholderImage', () => {
    test('should identify SVG logos as placeholders', () => {
      expect(isPlaceholderImage('https://assets.fstatic.nl/shared/images/funda-logo-blue.svg')).toBe(true);
      expect(isPlaceholderImage('https://example.com/site-logo.svg')).toBe(true);
    });

    test('should identify map tiles as placeholders', () => {
      expect(isPlaceholderImage('https://marketinsightsassets.funda.nl/maps/<EMAIL>')).toBe(true);
    });

    test('should identify small dimension images as placeholders', () => {
      expect(isPlaceholderImage('https://example.com/icon_32x32.png')).toBe(true);
      expect(isPlaceholderImage('https://example.com/thumb_64x64.jpg')).toBe(true);
    });

    test('should identify generic logo patterns as placeholders', () => {
      expect(isPlaceholderImage('https://example.com/logo.png')).toBe(true);
      expect(isPlaceholderImage('https://example.com/company-logo.jpg')).toBe(true);
    });

    test('should not identify valid property images as placeholders', () => {
      expect(isPlaceholderImage('https://cloud.funda.nl/valentina_media/216/599/040_720x480.jpg')).toBe(false);
      expect(isPlaceholderImage('https://example.com/property_photo_800x600.png')).toBe(false);
    });

    test('should handle invalid inputs', () => {
      expect(isPlaceholderImage(null)).toBe(true);
      expect(isPlaceholderImage(undefined)).toBe(true);
      expect(isPlaceholderImage('')).toBe(true);
      expect(isPlaceholderImage(123)).toBe(true);
    });
  });

  describe('isValidPropertyImage', () => {
    test('should return opposite of isPlaceholderImage', () => {
      testImages.placeholders.forEach(img => {
        expect(isValidPropertyImage(img)).toBe(false);
      });

      testImages.validImages.forEach(img => {
        expect(isValidPropertyImage(img)).toBe(true);
      });
    });
  });

  describe('filterPlaceholderImages', () => {
    test('should filter out placeholder images from mixed array', () => {
      const mixedImages = [
        'https://assets.fstatic.nl/shared/images/funda-logo-blue.svg',
        'https://cloud.funda.nl/valentina_media/216/599/040_720x480.jpg',
        'https://cloud.funda.nl/valentina_media/216/599/041_720x480.jpg',
        'https://marketinsightsassets.funda.nl/maps/<EMAIL>'
      ];

      const filtered = filterPlaceholderImages(mixedImages);
      
      expect(filtered).toHaveLength(2);
      expect(filtered).toEqual([
        'https://cloud.funda.nl/valentina_media/216/599/040_720x480.jpg',
        'https://cloud.funda.nl/valentina_media/216/599/041_720x480.jpg'
      ]);
    });

    test('should return empty array for all placeholder images', () => {
      const result = filterPlaceholderImages(testImages.placeholders);
      expect(result).toHaveLength(0);
    });

    test('should return all images when none are placeholders', () => {
      const result = filterPlaceholderImages(testImages.validImages);
      expect(result).toEqual(testImages.validImages);
    });

    test('should handle empty or invalid arrays', () => {
      expect(filterPlaceholderImages([])).toEqual([]);
      expect(filterPlaceholderImages(null)).toEqual([]);
      expect(filterPlaceholderImages(undefined)).toEqual([]);
      expect(filterPlaceholderImages('not-an-array')).toEqual([]);
    });
  });

  describe('filterImagesWithFallback', () => {
    test('should use fallback when no valid images remain', () => {
      const fallback = 'https://example.com/default_property.jpg';
      const result = filterImagesWithFallback(testImages.placeholders, fallback);
      
      expect(result).toEqual([fallback]);
    });

    test('should not use fallback when valid images exist', () => {
      const fallback = 'https://example.com/default_property.jpg';
      const mixedImages = [...testImages.placeholders, ...testImages.validImages];
      const result = filterImagesWithFallback(mixedImages, fallback);
      
      expect(result).toEqual(testImages.validImages);
      expect(result).not.toContain(fallback);
    });

    test('should not use invalid fallback images', () => {
      const invalidFallback = 'https://assets.fstatic.nl/shared/images/funda-logo-blue.svg';
      const result = filterImagesWithFallback(testImages.placeholders, invalidFallback);
      
      expect(result).toEqual([]);
    });
  });

  describe('getFilteringStats', () => {
    test('should provide correct statistics', () => {
      const mixedImages = [
        'https://assets.fstatic.nl/shared/images/funda-logo-blue.svg', // placeholder
        'https://cloud.funda.nl/valentina_media/216/599/040_720x480.jpg', // valid
        'https://cloud.funda.nl/valentina_media/216/599/041_720x480.jpg', // valid
        'https://marketinsightsassets.funda.nl/maps/map_test_64x64.jpg', // placeholder
        'https://example.com/logo.png' // placeholder
      ];

      const stats = getFilteringStats(mixedImages);
      
      expect(stats.original).toBe(5);
      expect(stats.filtered).toBe(2);
      expect(stats.removed).toBe(3);
      expect(stats.placeholders).toHaveLength(3);
      expect(stats.placeholders).toContain('https://assets.fstatic.nl/shared/images/funda-logo-blue.svg');
    });

    test('should handle empty arrays', () => {
      const stats = getFilteringStats([]);
      
      expect(stats.original).toBe(0);
      expect(stats.filtered).toBe(0);
      expect(stats.removed).toBe(0);
      expect(stats.placeholders).toEqual([]);
    });

    test('should handle invalid inputs', () => {
      const stats = getFilteringStats(null);
      
      expect(stats.original).toBe(0);
      expect(stats.filtered).toBe(0);
      expect(stats.removed).toBe(0);
      expect(stats.placeholders).toEqual([]);
    });
  });

  describe('Real-world scenario', () => {
    test('should handle typical Funda listing response', () => {
      // Based on the example from the issue description
      const fundaListingImages = [
        "https://assets.fstatic.nl/shared/images/funda-logo-blue.svg",
        "https://cloud.funda.nl/valentina_media/216/599/040_720x480.jpg",
        "https://cloud.funda.nl/valentina_media/216/599/041_720x480.jpg",
        "https://marketinsightsassets.funda.nl/maps/<EMAIL>"
      ];

      const filtered = filterPlaceholderImages(fundaListingImages);
      
      // Should have 2 valid property photos, no logo or map
      expect(filtered).toHaveLength(2);
      expect(filtered[0]).toBe("https://cloud.funda.nl/valentina_media/216/599/040_720x480.jpg");
      expect(filtered[1]).toBe("https://cloud.funda.nl/valentina_media/216/599/041_720x480.jpg");
      
      // Should not contain SVG or map images
      expect(filtered).not.toContain("https://assets.fstatic.nl/shared/images/funda-logo-blue.svg");
      expect(filtered).not.toContain("https://marketinsightsassets.funda.nl/maps/<EMAIL>");
    });

    test('should ensure first image is always a valid property photo', () => {
      const problematicImages = [
        "https://assets.fstatic.nl/shared/images/funda-logo-blue.svg", // This would be first and cause blank image
        "https://cloud.funda.nl/valentina_media/216/599/040_720x480.jpg", // This should become first
        "https://cloud.funda.nl/valentina_media/216/599/041_720x480.jpg"
      ];

      const filtered = filterPlaceholderImages(problematicImages);
      
      expect(filtered).toHaveLength(2);
      // First image should now be the actual property photo, not the SVG
      expect(filtered[0]).toBe("https://cloud.funda.nl/valentina_media/216/599/040_720x480.jpg");
      expect(filtered[0]).toMatch(/\.(jpg|jpeg|png|webp)$/i);
      expect(filtered[0]).not.toMatch(/\.svg$/i);
    });
  });
});

// Integration test with frontend compatibility layer
describe('Frontend Compatibility Layer Integration', () => {
  // Mock the frontend compatibility layer
  const { filterPlaceholderImages } = require('../utils/imageFilter');

  test('should work correctly when integrated with convertToFrontendFormat', () => {
    // Simulate a property with problematic images
    const mockProperty = {
      _id: 'test123',
      title: 'Test Property',
      images: [
        "https://assets.fstatic.nl/shared/images/funda-logo-blue.svg",
        "https://cloud.funda.nl/valentina_media/216/599/040_720x480.jpg",
        "https://cloud.funda.nl/valentina_media/216/599/041_720x480.jpg",
        "https://marketinsightsassets.funda.nl/maps/<EMAIL>"
      ]
    };

    // Apply the same filtering logic that would be used in convertToFrontendFormat
    const filteredImages = filterPlaceholderImages(mockProperty.images);
    
    expect(filteredImages).toHaveLength(2);
    expect(filteredImages[0]).toBe("https://cloud.funda.nl/valentina_media/216/599/040_720x480.jpg");
    
    // This ensures the frontend will receive valid property images only
    // and the first image will be suitable for cover image display
    expect(filteredImages[0]).not.toMatch(/\.svg$/i);
    expect(filteredImages[0]).not.toMatch(/marketinsightsassets.*maps/i);
  });
});
