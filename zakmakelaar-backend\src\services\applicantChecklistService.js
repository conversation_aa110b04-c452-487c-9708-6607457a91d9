const mongoose = require('mongoose');
const Application = require('../models/Application');
const Property = require('../models/Property');
const User = require('../models/User');
const { loggers } = require('./logger');

// Applicant Checklist Schema
const applicantChecklistSchema = new mongoose.Schema({
  application: {
    applicationId: { type: mongoose.Schema.Types.ObjectId, ref: 'Application', required: true },
    applicantName: { type: String, required: true },
    propertyTitle: { type: String, required: true }
  },
  
  propertyOwner: {
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    name: { type: String, required: true }
  },
  
  // Checklist categories and items
  checklist: {
    // Document verification
    documents: {
      status: { 
        type: String, 
        enum: ['not_started', 'in_progress', 'completed', 'issues'], 
        default: 'not_started' 
      },
      items: [{
        name: { type: String, required: true },
        type: { 
          type: String, 
          enum: ['income_proof', 'employment_contract', 'bank_statement', 'id_document', 'rental_reference', 'other'] 
        },
        required: { type: Boolean, default: true },
        status: { 
          type: String, 
          enum: ['pending', 'received', 'verified', 'rejected'], 
          default: 'pending' 
        },
        notes: { type: String },
        uploadedAt: { type: Date },
        verifiedAt: { type: Date },
        verifiedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
        rejectionReason: { type: String }
      }],
      completionPercentage: { type: Number, default: 0 },
      lastUpdated: { type: Date, default: Date.now }
    },
    
    // Background checks
    backgroundCheck: {
      status: { 
        type: String, 
        enum: ['not_started', 'in_progress', 'completed', 'issues'], 
        default: 'not_started' 
      },
      items: [{
        name: { type: String, required: true },
        type: { 
          type: String, 
          enum: ['credit_check', 'criminal_background', 'employment_verification', 'rental_history', 'reference_check'] 
        },
        required: { type: Boolean, default: true },
        status: { 
          type: String, 
          enum: ['pending', 'in_progress', 'completed', 'failed'], 
          default: 'pending' 
        },
        result: { type: String },
        score: { type: Number },
        notes: { type: String },
        completedAt: { type: Date },
        completedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
      }],
      overallScore: { type: Number },
      completionPercentage: { type: Number, default: 0 },
      lastUpdated: { type: Date, default: Date.now }
    },
    
    // Interview and viewing
    interview: {
      status: { 
        type: String, 
        enum: ['not_scheduled', 'scheduled', 'completed', 'cancelled'], 
        default: 'not_scheduled' 
      },
      scheduledDate: { type: Date },
      completedDate: { type: Date },
      type: { 
        type: String, 
        enum: ['phone', 'video', 'in_person', 'property_viewing'], 
        default: 'in_person' 
      },
      duration: { type: Number }, // in minutes
      attendees: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
      
      // Interview assessment
      assessment: {
        communication: { type: Number, min: 1, max: 5 },
        professionalism: { type: Number, min: 1, max: 5 },
        reliability: { type: Number, min: 1, max: 5 },
        compatibility: { type: Number, min: 1, max: 5 },
        overall: { type: Number, min: 1, max: 5 },
        notes: { type: String },
        positives: [{ type: String }],
        concerns: [{ type: String }],
        recommendation: { 
          type: String, 
          enum: ['strongly_recommend', 'recommend', 'neutral', 'not_recommend', 'reject'] 
        }
      },
      
      // Property viewing specific
      viewing: {
        attended: { type: Boolean },
        punctuality: { type: String, enum: ['early', 'on_time', 'late', 'no_show'] },
        interest_level: { type: String, enum: ['very_high', 'high', 'moderate', 'low', 'very_low'] },
        questions_asked: [{ type: String }],
        concerns_raised: [{ type: String }],
        follow_up_required: { type: Boolean, default: false }
      },
      
      completionPercentage: { type: Number, default: 0 },
      lastUpdated: { type: Date, default: Date.now }
    },
    
    // Financial assessment
    financial: {
      status: { 
        type: String, 
        enum: ['not_started', 'in_progress', 'completed', 'issues'], 
        default: 'not_started' 
      },
      items: [{
        name: { type: String, required: true },
        type: { 
          type: String, 
          enum: ['income_verification', 'employment_stability', 'debt_analysis', 'savings_verification', 'guarantor_check'] 
        },
        required: { type: Boolean, default: true },
        status: { 
          type: String, 
          enum: ['pending', 'verified', 'failed'], 
          default: 'pending' 
        },
        value: { type: String },
        result: { type: String },
        notes: { type: String },
        verifiedAt: { type: Date }
      }],
      
      // Financial summary
      summary: {
        monthlyIncome: { type: Number },
        incomeToRentRatio: { type: Number },
        employmentStability: { type: String, enum: ['excellent', 'good', 'fair', 'poor'] },
        creditScore: { type: Number },
        debtToIncomeRatio: { type: Number },
        hasGuarantor: { type: Boolean },
        riskLevel: { type: String, enum: ['low', 'medium', 'high'] }
      },
      
      completionPercentage: { type: Number, default: 0 },
      lastUpdated: { type: Date, default: Date.now }
    },
    
    // References
    references: {
      status: { 
        type: String, 
        enum: ['not_started', 'in_progress', 'completed', 'issues'], 
        default: 'not_started' 
      },
      items: [{
        type: { type: String, enum: ['landlord', 'employer', 'personal'], required: true },
        name: { type: String, required: true },
        relationship: { type: String },
        contactInfo: {
          email: { type: String },
          phone: { type: String }
        },
        status: { 
          type: String, 
          enum: ['pending', 'contacted', 'responded', 'unreachable'], 
          default: 'pending' 
        },
        contactedAt: { type: Date },
        responseReceived: { type: Date },
        
        // Reference feedback
        feedback: {
          reliability: { type: Number, min: 1, max: 5 },
          cleanliness: { type: Number, min: 1, max: 5 },
          communication: { type: Number, min: 1, max: 5 },
          paymentHistory: { type: Number, min: 1, max: 5 },
          overallRating: { type: Number, min: 1, max: 5 },
          wouldRecommend: { type: Boolean },
          comments: { type: String },
          concerns: [{ type: String }]
        },
        
        notes: { type: String }
      }],
      completionPercentage: { type: Number, default: 0 },
      lastUpdated: { type: Date, default: Date.now }
    }
  },
  
  // Overall progress and summary
  summary: {
    overallProgress: { type: Number, default: 0 },
    currentStage: { 
      type: String, 
      enum: ['initial_review', 'document_collection', 'background_checks', 'interview_scheduled', 'interview_completed', 'final_review', 'decision_pending', 'completed'], 
      default: 'initial_review' 
    },
    
    // Key insights
    strengths: [{ type: String }],
    concerns: [{ type: String }],
    recommendations: [{ type: String }],
    
    // Decision support
    recommendedAction: { 
      type: String, 
      enum: ['approve', 'approve_conditional', 'request_more_info', 'reject', 'undecided'] 
    },
    confidence: { type: Number, min: 0, max: 100 },
    
    // Priority and urgency
    priority: { type: String, enum: ['low', 'medium', 'high', 'urgent'], default: 'medium' },
    nextActions: [{ 
      action: { type: String },
      assignedTo: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      dueDate: { type: Date },
      completed: { type: Boolean, default: false }
    }]
  },
  
  // Activity log
  activityLog: [{
    action: { type: String, required: true },
    category: { type: String },
    description: { type: String },
    performedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    timestamp: { type: Date, default: Date.now },
    metadata: { type: mongoose.Schema.Types.Mixed }
  }],
  
  // Timestamps
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  completedAt: { type: Date }
});

// Indexes
applicantChecklistSchema.index({ 'application.applicationId': 1 });
applicantChecklistSchema.index({ 'propertyOwner.userId': 1 });
applicantChecklistSchema.index({ 'summary.currentStage': 1 });
applicantChecklistSchema.index({ 'summary.priority': 1 });

const ApplicantChecklist = mongoose.model('ApplicantChecklist', applicantChecklistSchema);

class ApplicantChecklistService {
  constructor() {
    this.logger = loggers.checklistLogger;
    
    // Default checklist templates
    this.defaultTemplates = {
      documents: [
        { name: 'Proof of Income', type: 'income_proof', required: true },
        { name: 'Employment Contract', type: 'employment_contract', required: true },
        { name: 'Bank Statements (3 months)', type: 'bank_statement', required: true },
        { name: 'Valid ID Document', type: 'id_document', required: true },
        { name: 'Previous Rental Reference', type: 'rental_reference', required: false }
      ],
      backgroundCheck: [
        { name: 'Credit Check', type: 'credit_check', required: true },
        { name: 'Employment Verification', type: 'employment_verification', required: true },
        { name: 'Rental History Check', type: 'rental_history', required: false },
        { name: 'Reference Verification', type: 'reference_check', required: true }
      ],
      financial: [
        { name: 'Monthly Income Verification', type: 'income_verification', required: true },
        { name: 'Employment Stability Assessment', type: 'employment_stability', required: true },
        { name: 'Debt-to-Income Analysis', type: 'debt_analysis', required: false },
        { name: 'Savings Verification', type: 'savings_verification', required: false }
      ]
    };
  }

  /**
   * Create checklist for an application
   */
  async createChecklist(applicationId, ownerUserId, customTemplate = {}) {
    try {
      const application = await Application.findById(applicationId)
        .populate('applicant.userId', 'profile')
        .populate('property.propertyId');

      if (!application) {
        throw new Error('Application not found');
      }

      // Verify owner permission
      const property = await Property.findById(application.property.propertyId);
      if (!property || property.owner.userId.toString() !== ownerUserId.toString()) {
        throw new Error('Unauthorized: Not the property owner');
      }

      // Check if checklist already exists
      const existingChecklist = await ApplicantChecklist.findOne({
        'application.applicationId': applicationId
      });
      
      if (existingChecklist) {
        return existingChecklist;
      }

      // Create new checklist
      const checklist = new ApplicantChecklist({
        application: {
          applicationId: applicationId,
          applicantName: application.applicant.snapshot.name,
          propertyTitle: application.property.snapshot.title
        },
        propertyOwner: {
          userId: ownerUserId,
          name: `${property.owner.userId.profile?.firstName || ''} ${property.owner.userId.profile?.lastName || ''}`.trim()
        },
        checklist: this.initializeChecklistItems(customTemplate)
      });

      await checklist.save();

      // Log creation
      await this.addActivityLog(
        checklist._id,
        'checklist_created',
        'general',
        'Applicant checklist created',
        ownerUserId
      );

      this.logger.info('Checklist created', { 
        checklistId: checklist._id,
        applicationId,
        ownerUserId 
      });

      return checklist;
    } catch (error) {
      this.logger.error('Error creating checklist', { error: error.message, applicationId });
      throw error;
    }
  }

  /**
   * Initialize checklist items with default templates
   */
  initializeChecklistItems(customTemplate) {
    const template = { ...this.defaultTemplates, ...customTemplate };

    return {
      documents: {
        status: 'not_started',
        items: template.documents?.map(item => ({
          ...item,
          status: 'pending'
        })) || [],
        completionPercentage: 0
      },
      backgroundCheck: {
        status: 'not_started',
        items: template.backgroundCheck?.map(item => ({
          ...item,
          status: 'pending'
        })) || [],
        completionPercentage: 0
      },
      interview: {
        status: 'not_scheduled',
        completionPercentage: 0
      },
      financial: {
        status: 'not_started',
        items: template.financial?.map(item => ({
          ...item,
          status: 'pending'
        })) || [],
        completionPercentage: 0,
        summary: {}
      },
      references: {
        status: 'not_started',
        items: [],
        completionPercentage: 0
      }
    };
  }

  /**
   * Update checklist item status
   */
  async updateChecklistItem(checklistId, category, itemId, updates, ownerUserId) {
    try {
      const checklist = await ApplicantChecklist.findById(checklistId);
      if (!checklist) {
        throw new Error('Checklist not found');
      }

      // Verify owner permission
      if (checklist.propertyOwner.userId.toString() !== ownerUserId.toString()) {
        throw new Error('Unauthorized: Not the property owner');
      }

      const categoryData = checklist.checklist[category];
      if (!categoryData) {
        throw new Error('Invalid category');
      }

      // Find and update item
      let item;
      if (category === 'interview') {
        // Interview is a single object
        Object.assign(categoryData, updates);
        item = categoryData;
      } else {
        // Find item in array
        item = categoryData.items.id(itemId);
        if (!item) {
          throw new Error('Item not found');
        }
        Object.assign(item, updates);
      }

      // Update category status and completion
      this.updateCategoryStatus(categoryData, category);
      
      // Update overall progress
      this.updateOverallProgress(checklist);
      
      checklist.updatedAt = new Date();
      await checklist.save();

      // Log update
      await this.addActivityLog(
        checklistId,
        'item_updated',
        category,
        `${category} item updated: ${item.name || 'Interview'}`,
        ownerUserId,
        { itemId, updates }
      );

      this.logger.info('Checklist item updated', { 
        checklistId,
        category,
        itemId,
        updates 
      });

      return checklist;
    } catch (error) {
      this.logger.error('Error updating checklist item', { 
        error: error.message, 
        checklistId, 
        category, 
        itemId 
      });
      throw error;
    }
  }

  /**
   * Add document to checklist
   */
  async addDocument(checklistId, documentInfo, ownerUserId) {
    try {
      const checklist = await ApplicantChecklist.findById(checklistId);
      if (!checklist) {
        throw new Error('Checklist not found');
      }

      // Verify owner permission
      if (checklist.propertyOwner.userId.toString() !== ownerUserId.toString()) {
        throw new Error('Unauthorized: Not the property owner');
      }

      const documentItem = {
        name: documentInfo.name,
        type: documentInfo.type,
        required: documentInfo.required || false,
        status: 'received',
        uploadedAt: new Date(),
        notes: documentInfo.notes || ''
      };

      checklist.checklist.documents.items.push(documentItem);
      
      // Update category status
      this.updateCategoryStatus(checklist.checklist.documents, 'documents');
      this.updateOverallProgress(checklist);
      
      checklist.updatedAt = new Date();
      await checklist.save();

      // Log addition
      await this.addActivityLog(
        checklistId,
        'document_added',
        'documents',
        `Document added: ${documentInfo.name}`,
        ownerUserId,
        documentInfo
      );

      return checklist;
    } catch (error) {
      this.logger.error('Error adding document', { error: error.message, checklistId });
      throw error;
    }
  }

  /**
   * Schedule interview
   */
  async scheduleInterview(checklistId, interviewDetails, ownerUserId) {
    try {
      const checklist = await ApplicantChecklist.findById(checklistId);
      if (!checklist) {
        throw new Error('Checklist not found');
      }

      // Verify owner permission
      if (checklist.propertyOwner.userId.toString() !== ownerUserId.toString()) {
        throw new Error('Unauthorized: Not the property owner');
      }

      checklist.checklist.interview = {
        ...checklist.checklist.interview,
        status: 'scheduled',
        scheduledDate: new Date(interviewDetails.scheduledDate),
        type: interviewDetails.type || 'in_person',
        duration: interviewDetails.duration || 30,
        completionPercentage: 25 // Partially complete when scheduled
      };

      // Update overall progress
      this.updateOverallProgress(checklist);
      
      checklist.updatedAt = new Date();
      await checklist.save();

      // Log scheduling
      await this.addActivityLog(
        checklistId,
        'interview_scheduled',
        'interview',
        `Interview scheduled for ${interviewDetails.scheduledDate}`,
        ownerUserId,
        interviewDetails
      );

      return checklist;
    } catch (error) {
      this.logger.error('Error scheduling interview', { error: error.message, checklistId });
      throw error;
    }
  }

  /**
   * Complete interview with assessment
   */
  async completeInterview(checklistId, assessment, ownerUserId) {
    try {
      const checklist = await ApplicantChecklist.findById(checklistId);
      if (!checklist) {
        throw new Error('Checklist not found');
      }

      // Verify owner permission
      if (checklist.propertyOwner.userId.toString() !== ownerUserId.toString()) {
        throw new Error('Unauthorized: Not the property owner');
      }

      checklist.checklist.interview = {
        ...checklist.checklist.interview,
        status: 'completed',
        completedDate: new Date(),
        assessment: assessment,
        completionPercentage: 100
      };

      // Update overall progress and stage
      this.updateOverallProgress(checklist);
      if (checklist.summary.currentStage === 'interview_scheduled') {
        checklist.summary.currentStage = 'interview_completed';
      }
      
      checklist.updatedAt = new Date();
      await checklist.save();

      // Log completion
      await this.addActivityLog(
        checklistId,
        'interview_completed',
        'interview',
        `Interview completed with overall rating: ${assessment.overall || 'N/A'}`,
        ownerUserId,
        { assessment }
      );

      return checklist;
    } catch (error) {
      this.logger.error('Error completing interview', { error: error.message, checklistId });
      throw error;
    }
  }

  /**
   * Add reference check
   */
  async addReference(checklistId, referenceInfo, ownerUserId) {
    try {
      const checklist = await ApplicantChecklist.findById(checklistId);
      if (!checklist) {
        throw new Error('Checklist not found');
      }

      // Verify owner permission
      if (checklist.propertyOwner.userId.toString() !== ownerUserId.toString()) {
        throw new Error('Unauthorized: Not the property owner');
      }

      const reference = {
        type: referenceInfo.type,
        name: referenceInfo.name,
        relationship: referenceInfo.relationship,
        contactInfo: referenceInfo.contactInfo,
        status: 'pending'
      };

      checklist.checklist.references.items.push(reference);
      
      // Update category status
      this.updateCategoryStatus(checklist.checklist.references, 'references');
      this.updateOverallProgress(checklist);
      
      checklist.updatedAt = new Date();
      await checklist.save();

      // Log addition
      await this.addActivityLog(
        checklistId,
        'reference_added',
        'references',
        `Reference added: ${referenceInfo.name} (${referenceInfo.type})`,
        ownerUserId,
        referenceInfo
      );

      return checklist;
    } catch (error) {
      this.logger.error('Error adding reference', { error: error.message, checklistId });
      throw error;
    }
  }

  /**
   * Update category status based on item completions
   */
  updateCategoryStatus(categoryData, categoryType) {
    if (!categoryData.items || categoryData.items.length === 0) {
      if (categoryType === 'interview') {
        categoryData.completionPercentage = categoryData.status === 'completed' ? 100 : 
                                          categoryData.status === 'scheduled' ? 50 : 0;
      }
      return;
    }

    const totalItems = categoryData.items.length;
    const completedItems = categoryData.items.filter(item => 
      ['verified', 'completed', 'responded'].includes(item.status)
    ).length;
    
    const inProgressItems = categoryData.items.filter(item => 
      ['in_progress', 'contacted', 'received'].includes(item.status)
    ).length;

    categoryData.completionPercentage = Math.round((completedItems / totalItems) * 100);

    if (completedItems === totalItems) {
      categoryData.status = 'completed';
    } else if (inProgressItems > 0 || completedItems > 0) {
      categoryData.status = 'in_progress';
    } else {
      categoryData.status = 'not_started';
    }

    categoryData.lastUpdated = new Date();
  }

  /**
   * Update overall progress and stage
   */
  updateOverallProgress(checklist) {
    const categories = checklist.checklist;
    const weights = {
      documents: 25,
      backgroundCheck: 20,
      interview: 25,
      financial: 20,
      references: 10
    };

    let totalProgress = 0;
    Object.keys(weights).forEach(category => {
      const categoryData = categories[category];
      const progress = categoryData.completionPercentage || 0;
      totalProgress += (progress * weights[category]) / 100;
    });

    checklist.summary.overallProgress = Math.round(totalProgress);

    // Update current stage based on progress
    const progress = checklist.summary.overallProgress;
    if (progress === 0) {
      checklist.summary.currentStage = 'initial_review';
    } else if (progress < 25) {
      checklist.summary.currentStage = 'document_collection';
    } else if (progress < 50) {
      checklist.summary.currentStage = 'background_checks';
    } else if (progress < 75 && categories.interview.status === 'scheduled') {
      checklist.summary.currentStage = 'interview_scheduled';
    } else if (progress < 90 && categories.interview.status === 'completed') {
      checklist.summary.currentStage = 'interview_completed';
    } else if (progress < 100) {
      checklist.summary.currentStage = 'final_review';
    } else {
      checklist.summary.currentStage = 'completed';
      checklist.completedAt = new Date();
    }
  }

  /**
   * Get checklist summary with insights
   */
  async getChecklistSummary(checklistId, ownerUserId) {
    try {
      const checklist = await ApplicantChecklist.findById(checklistId);
      if (!checklist) {
        throw new Error('Checklist not found');
      }

      // Verify owner permission
      if (checklist.propertyOwner.userId.toString() !== ownerUserId.toString()) {
        throw new Error('Unauthorized: Not the property owner');
      }

      // Generate insights
      const insights = this.generateInsights(checklist);
      
      return {
        checklist,
        insights,
        nextActions: this.getNextActions(checklist),
        riskAssessment: this.assessRisk(checklist),
        recommendation: this.generateRecommendation(checklist)
      };
    } catch (error) {
      this.logger.error('Error getting checklist summary', { error: error.message, checklistId });
      throw error;
    }
  }

  /**
   * Generate insights from checklist data
   */
  generateInsights(checklist) {
    const insights = {
      strengths: [],
      concerns: [],
      progress: {
        overall: checklist.summary.overallProgress,
        stage: checklist.summary.currentStage,
        timeInStage: this.calculateTimeInStage(checklist)
      },
      completion: {
        documents: checklist.checklist.documents.completionPercentage,
        backgroundCheck: checklist.checklist.backgroundCheck.completionPercentage,
        interview: checklist.checklist.interview.completionPercentage,
        financial: checklist.checklist.financial.completionPercentage,
        references: checklist.checklist.references.completionPercentage
      }
    };

    // Analyze strengths
    if (checklist.checklist.documents.completionPercentage >= 80) {
      insights.strengths.push('Complete documentation provided');
    }
    
    if (checklist.checklist.interview.assessment?.overall >= 4) {
      insights.strengths.push('Excellent interview performance');
    }

    if (checklist.checklist.financial.summary?.riskLevel === 'low') {
      insights.strengths.push('Strong financial profile');
    }

    // Analyze concerns
    if (checklist.checklist.documents.completionPercentage < 50) {
      insights.concerns.push('Incomplete documentation');
    }

    if (checklist.checklist.backgroundCheck.items?.some(item => item.status === 'failed')) {
      insights.concerns.push('Background check issues detected');
    }

    if (insights.progress.timeInStage > 7) { // More than 7 days
      insights.concerns.push('Process taking longer than expected');
    }

    return insights;
  }

  /**
   * Get next recommended actions
   */
  getNextActions(checklist) {
    const actions = [];

    // Check documents
    if (checklist.checklist.documents.status !== 'completed') {
      const pendingDocs = checklist.checklist.documents.items?.filter(item => 
        item.status === 'pending'
      ) || [];
      
      if (pendingDocs.length > 0) {
        actions.push({
          action: 'Follow up on missing documents',
          priority: 'high',
          description: `${pendingDocs.length} documents still pending`
        });
      }
    }

    // Check interview
    if (checklist.checklist.interview.status === 'not_scheduled') {
      actions.push({
        action: 'Schedule interview',
        priority: 'medium',
        description: 'Interview not yet scheduled'
      });
    }

    // Check references
    const pendingRefs = checklist.checklist.references.items?.filter(item => 
      item.status === 'pending'
    ) || [];
    
    if (pendingRefs.length > 0) {
      actions.push({
        action: 'Contact references',
        priority: 'medium',
        description: `${pendingRefs.length} references to contact`
      });
    }

    return actions;
  }

  /**
   * Assess overall risk
   */
  assessRisk(checklist) {
    let riskScore = 0;
    const factors = [];

    // Document completeness
    if (checklist.checklist.documents.completionPercentage < 50) {
      riskScore += 20;
      factors.push('Incomplete documentation');
    }

    // Financial risk
    if (checklist.checklist.financial.summary?.riskLevel === 'high') {
      riskScore += 30;
      factors.push('High financial risk');
    } else if (checklist.checklist.financial.summary?.riskLevel === 'medium') {
      riskScore += 15;
      factors.push('Moderate financial risk');
    }

    // Interview concerns
    if (checklist.checklist.interview.assessment?.overall < 3) {
      riskScore += 25;
      factors.push('Poor interview performance');
    }

    // Background check issues
    const failedChecks = checklist.checklist.backgroundCheck.items?.filter(item => 
      item.status === 'failed'
    ).length || 0;
    
    if (failedChecks > 0) {
      riskScore += failedChecks * 15;
      factors.push(`${failedChecks} background check failures`);
    }

    return {
      score: Math.min(100, riskScore),
      level: riskScore > 50 ? 'high' : riskScore > 25 ? 'medium' : 'low',
      factors
    };
  }

  /**
   * Generate recommendation
   */
  generateRecommendation(checklist) {
    const riskAssessment = this.assessRisk(checklist);
    const completionRate = checklist.summary.overallProgress;

    let recommendation = 'undecided';
    let confidence = 50;
    let reasoning = [];

    if (completionRate < 70) {
      recommendation = 'request_more_info';
      reasoning.push('Insufficient information to make decision');
      confidence = 30;
    } else {
      if (riskAssessment.level === 'low' && 
          checklist.checklist.interview.assessment?.overall >= 4) {
        recommendation = 'approve';
        confidence = 85;
        reasoning.push('Low risk profile with good interview');
      } else if (riskAssessment.level === 'high' || 
                 checklist.checklist.interview.assessment?.overall < 3) {
        recommendation = 'reject';
        confidence = 75;
        reasoning.push('High risk factors identified');
      } else {
        recommendation = 'approve_conditional';
        confidence = 60;
        reasoning.push('Moderate risk - consider conditional approval');
      }
    }

    return {
      action: recommendation,
      confidence,
      reasoning
    };
  }

  /**
   * Add activity log entry
   */
  async addActivityLog(checklistId, action, category, description, userId, metadata = {}) {
    try {
      const checklist = await ApplicantChecklist.findById(checklistId);
      if (checklist) {
        checklist.activityLog.push({
          action,
          category,
          description,
          performedBy: userId,
          timestamp: new Date(),
          metadata
        });
        await checklist.save();
      }
    } catch (error) {
      this.logger.error('Error adding activity log', { error: error.message, checklistId });
    }
  }

  /**
   * Calculate time spent in current stage
   */
  calculateTimeInStage(checklist) {
    const lastStageChange = checklist.activityLog
      .filter(log => log.category === 'stage_change')
      .sort((a, b) => b.timestamp - a.timestamp)[0];

    const startTime = lastStageChange ? lastStageChange.timestamp : checklist.createdAt;
    return Math.floor((new Date() - startTime) / (1000 * 60 * 60 * 24)); // Days
  }

  /**
   * Get all checklists for property owner
   */
  async getOwnerChecklists(ownerUserId, filters = {}) {
    try {
      const query = { 'propertyOwner.userId': ownerUserId };
      
      if (filters.stage) {
        query['summary.currentStage'] = filters.stage;
      }
      
      if (filters.priority) {
        query['summary.priority'] = filters.priority;
      }

      const checklists = await ApplicantChecklist.find(query)
        .sort({ 'summary.priority': -1, updatedAt: -1 })
        .limit(filters.limit || 50);

      return checklists;
    } catch (error) {
      this.logger.error('Error getting owner checklists', { error: error.message, ownerUserId });
      throw error;
    }
  }
}

module.exports = { ApplicantChecklistService: new ApplicantChecklistService(), ApplicantChecklist };