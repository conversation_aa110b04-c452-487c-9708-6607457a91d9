import React from "react";
import {
  SafeAreaView,
  ScrollView,
  View,
  Text,
  TouchableOpacity,
  Switch,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { StatusBar } from "expo-status-bar";
import { useRouter } from "expo-router";

// Define theme colors
const THEME = {
  primary: "#4361ee",
  secondary: "#7209b7",
  lightGray: "#f3f4f6",
};

export default function SettingsScreen() {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const [pushEnabled, setPushEnabled] = React.useState(true);
  const [emailEnabled, setEmailEnabled] = React.useState(true);
  const [language, setLanguage] = React.useState<"en" | "nl">("nl");

  return (
    <SafeAreaView
      style={{ flex: 1, backgroundColor: THEME.lightGray }}
      edges={["left", "right", "bottom"]}
    >
      <StatusBar style="light" />
      {/* Header with gradient */}
      <LinearGradient
        colors={[THEME.primary, THEME.secondary]}
        style={{
          paddingHorizontal: 20,
          paddingBottom: 20,
          paddingTop: Math.max(insets.top, 16),
        }}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <View style={{ width: 44 }} />
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            <View
              style={{
                width: 44,
                height: 44,
                backgroundColor: "#FFFFFF",
                justifyContent: "center",
                alignItems: "center",
                marginRight: 12,
                borderRadius: 24,
                shadowColor: "#000",
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 3,
              }}
            >
              <Text
                style={{
                  fontSize: 18,
                  fontWeight: "800",
                  color: THEME.primary,
                }}
              >
                ZM
              </Text>
            </View>
            <View>
              <Text
                style={{ fontSize: 20, fontWeight: "700", color: "#FFFFFF" }}
              >
                Settings
              </Text>
              <Text style={{ fontSize: 12, color: "#E0E7FF" }}>
                Manage your account
              </Text>
            </View>
          </View>
          <View style={{ width: 44 }} />
        </View>
      </LinearGradient>

      <ScrollView contentContainerStyle={{ padding: 16 }}>
        {/* Language */}
        <View
          style={{
            backgroundColor: "#FFFFFF",
            borderRadius: 12,
            padding: 16,
            marginBottom: 12,
          }}
        >
          <Text
            style={{
              fontSize: 16,
              fontWeight: "700",
              color: "#0F172A",
              marginBottom: 8,
            }}
          >
            Language
          </Text>
          <View style={{ flexDirection: "row", gap: 8 }}>
            <TouchableOpacity
              onPress={() => setLanguage("nl")}
              style={{
                paddingVertical: 8,
                paddingHorizontal: 12,
                borderRadius: 8,
                backgroundColor: language === "nl" ? "#2563EB" : "#F1F5F9",
              }}
            >
              <Text
                style={{
                  color: language === "nl" ? "#FFFFFF" : "#0F172A",
                  fontWeight: "600",
                }}
              >
                Nederlands
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => setLanguage("en")}
              style={{
                paddingVertical: 8,
                paddingHorizontal: 12,
                borderRadius: 8,
                backgroundColor: language === "en" ? "#2563EB" : "#F1F5F9",
              }}
            >
              <Text
                style={{
                  color: language === "en" ? "#FFFFFF" : "#0F172A",
                  fontWeight: "600",
                }}
              >
                English
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Notifications */}
        <View
          style={{
            backgroundColor: "#FFFFFF",
            borderRadius: 12,
            padding: 16,
            marginBottom: 12,
          }}
        >
          <Text
            style={{
              fontSize: 16,
              fontWeight: "700",
              color: "#0F172A",
              marginBottom: 12,
            }}
          >
            Notifications
          </Text>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: 12,
            }}
          >
            <Text style={{ color: "#0F172A" }}>Push Notifications</Text>
            <Switch value={pushEnabled} onValueChange={setPushEnabled} />
          </View>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Text style={{ color: "#0F172A" }}>Email Updates</Text>
            <Switch value={emailEnabled} onValueChange={setEmailEnabled} />
          </View>
        </View>

        {/* Account */}
        <View
          style={{
            backgroundColor: "#FFFFFF",
            borderRadius: 12,
            padding: 16,
            marginBottom: 12,
          }}
        >
          <Text
            style={{
              fontSize: 16,
              fontWeight: "700",
              color: "#0F172A",
              marginBottom: 12,
            }}
          >
            Account
          </Text>
          <TouchableOpacity
            onPress={() => router.push("/property-owner/profile" as any)}
            style={{
              flexDirection: "row",
              alignItems: "center",
              paddingVertical: 12,
            }}
          >
            <Ionicons name="person" size={20} color="#2563EB" />
            <Text style={{ marginLeft: 8, color: "#0F172A" }}>Profile</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => router.push("/property-owner/verification" as any)}
            style={{
              flexDirection: "row",
              alignItems: "center",
              paddingVertical: 12,
            }}
          >
            <Ionicons name="shield-checkmark" size={20} color="#2563EB" />
            <Text style={{ marginLeft: 8, color: "#0F172A" }}>
              Verification
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() =>
              router.push("/property-owner/change-password" as any)
            }
            style={{
              flexDirection: "row",
              alignItems: "center",
              paddingVertical: 12,
            }}
          >
            <Ionicons name="key" size={20} color="#2563EB" />
            <Text style={{ marginLeft: 8, color: "#0F172A" }}>
              Change Password
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
