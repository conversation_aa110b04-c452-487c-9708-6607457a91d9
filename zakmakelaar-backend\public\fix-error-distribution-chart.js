// Manual Fix for Error Distribution Chart
// Run this in browser console (F12) to populate the chart

function fixErrorDistributionChart() {
    console.log('🔧 Fixing Error Distribution chart...');
    
    if (!window.dashboard || !window.dashboard.charts || !window.dashboard.charts.errorDistribution) {
        console.error('❌ Error Distribution chart not found!');
        return;
    }
    
    const chart = window.dashboard.charts.errorDistribution;
    
    // Use transformation error metrics data
    const errorLabels = ['Validation', 'Mapping', 'Transformation'];
    const errorValues = [12794, 20493, 9]; // From your transformation error metrics
    
    // Update chart data
    chart.data.labels = errorLabels;
    chart.data.datasets[0].data = errorValues;
    
    // Force chart update
    chart.update('active');
    
    console.log(`✅ Error Distribution chart updated!`);
    console.log(`   Validation Errors: ${errorValues[0].toLocaleString()}`);
    console.log(`   Mapping Errors: ${errorValues[1].toLocaleString()}`);
    console.log(`   Transformation Errors: ${errorValues[2].toLocaleString()}`);
    console.log(`   Total Errors: ${errorValues.reduce((a, b) => a + b, 0).toLocaleString()}`);
}

// Auto-run the fix
fixErrorDistributionChart();
