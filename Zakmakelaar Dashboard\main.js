// ZakMakelaar Dashboard JavaScript
// Main functionality for real estate dashboard

// Mock data for properties
const mockProperties = [
    {
        id: 1,
        title: "Modern Apartment in Amsterdam Center",
        price: 425000,
        type: "Apartment",
        bedrooms: 2,
        bathrooms: 1,
        area: 85,
        city: "Amsterdam",
        image: "https://kimi-web-img.moonshot.cn/img/img.freepik.com/44de3ee8992f7caa749ede54d006fb1815b3006a.jpg",
        features: ["Furnished", "Balcony", "Parking"],
        status: "Available"
    },
    {
        id: 2,
        title: "Luxury Canal House in Jordaan",
        price: 850000,
        type: "House",
        bedrooms: 3,
        bathrooms: 2,
        area: 150,
        city: "Amsterdam",
        image: "https://kimi-web-img.moonshot.cn/img/img.jamesedition.com/c8de772d5fe3c4d51f4a37d4d7989c9219d5b501.jpg",
        features: ["Historic", "Canal View", "Renovated"],
        status: "Available"
    },
    {
        id: 3,
        title: "Contemporary Office Building",
        price: 1200000,
        type: "Commercial",
        bedrooms: 0,
        bathrooms: 4,
        area: 450,
        city: "Rotterdam",
        image: "https://kimi-web-img.moonshot.cn/img/archello.s3.eu-central-1.amazonaws.com/e68f5c11f478a0669779d5422a04c826a028dca0.jpg",
        features: ["Modern", "Parking", "Elevator"],
        status: "Available"
    },
    {
        id: 4,
        title: "Stylish Studio in De Pijp",
        price: 285000,
        type: "Studio",
        bedrooms: 1,
        bathrooms: 1,
        area: 45,
        city: "Amsterdam",
        image: "https://kimi-web-img.moonshot.cn/img/static.tildacdn.com/617087e4f01d5c1abe7614950322713666937551.jpg",
        features: ["Modern", "Central", "Furnished"],
        status: "Available"
    },
    {
        id: 5,
        title: "Family House in Utrecht",
        price: 675000,
        type: "House",
        bedrooms: 4,
        bathrooms: 2,
        area: 180,
        city: "Utrecht",
        image: "https://kimi-web-img.moonshot.cn/img/www.ritagermandesign.com/130ff12aee69d308070312e5993f8c20698cc727.jpg",
        features: ["Garden", "Parking", "Family Friendly"],
        status: "Available"
    },
    {
        id: 6,
        title: "Luxury Villa in The Hague",
        price: 1450000,
        type: "House",
        bedrooms: 5,
        bathrooms: 3,
        area: 320,
        city: "The Hague",
        image: "https://kimi-web-img.moonshot.cn/img/antonovich-design.com/c4dc761e62d99ae8976671614b35f23c72bda4df.jpg",
        features: ["Luxury", "Pool", "Garden", "Garage"],
        status: "Available"
    }
];

// Market data for charts
const marketData = {
    priceTrends: {
        months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        prices: [380, 395, 410, 425, 440, 435, 450, 465, 470, 475, 480, 485]
    },
    propertyTypes: [
        { name: 'Apartments', value: 45, color: '#5a9bd4' },
        { name: 'Houses', value: 35, color: '#f4a261' },
        { name: 'Commercial', value: 12, color: '#e76f51' },
        { name: 'Studios', value: 8, color: '#2a9d8f' }
    ],
    marketDistribution: [
        { name: 'Amsterdam', value: 35 },
        { name: 'Rotterdam', value: 20 },
        { name: 'The Hague', value: 18 },
        { name: 'Utrecht', value: 15 },
        { name: 'Other', value: 12 }
    ],
    monthlyActivity: {
        months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        listings: [120, 135, 148, 162, 175, 188],
        applications: [89, 95, 108, 115, 125, 132]
    }
};

// System alerts and recent activity
const systemData = {
    alerts: [
        { type: 'success', message: 'System health check passed', time: '2 minutes ago' },
        { type: 'warning', message: 'High application volume detected', time: '15 minutes ago' },
        { type: 'info', message: 'New market analysis available', time: '1 hour ago' },
        { type: 'error', message: 'API rate limit approaching', time: '2 hours ago' }
    ],
    recentListings: [
        { title: 'Modern Apartment - Amsterdam', price: 425000, time: '5 minutes ago' },
        { title: 'Canal House - Jordaan', price: 850000, time: '12 minutes ago' },
        { title: 'Office Building - Rotterdam', price: 1200000, time: '25 minutes ago' },
        { title: 'Family Home - Utrecht', price: 675000, time: '45 minutes ago' }
    ]
};

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeParticles();
    initializeTypedText();
    initializeCounters();
    initializeAuthentication();
    initializeApiIntegration();
    initializeAnimations();
});

// Initialize authentication
function initializeAuthentication() {
    const loginBtn = document.getElementById('login-btn');
    const logoutBtn = document.getElementById('logout-btn');
    const loginModal = document.getElementById('login-modal');
    const loginForm = document.getElementById('login-form');
    const cancelLogin = document.getElementById('cancel-login');
    const userMenu = document.getElementById('user-menu');
    
    // Check if user is already authenticated
    updateAuthUI();
    
    // Login button event
    loginBtn?.addEventListener('click', showLoginModal);
    
    // Logout button event
    logoutBtn?.addEventListener('click', handleLogout);
    
    // Cancel login
    cancelLogin?.addEventListener('click', hideLoginModal);
    
    // Login form submit
    loginForm?.addEventListener('submit', handleLogin);
    
    // Close modal on background click
    loginModal?.addEventListener('click', function(e) {
        if (e.target === loginModal) {
            hideLoginModal();
        }
    });
}

// Show login modal
function showLoginModal() {
    const modal = document.getElementById('login-modal');
    if (modal) {
        modal.classList.remove('hidden');
        document.getElementById('email')?.focus();
    }
}

// Hide login modal
function hideLoginModal() {
    const modal = document.getElementById('login-modal');
    const errorDiv = document.getElementById('login-error');
    if (modal) {
        modal.classList.add('hidden');
    }
    if (errorDiv) {
        errorDiv.classList.add('hidden');
    }
    // Clear form
    document.getElementById('login-form')?.reset();
}

// Handle login form submission
async function handleLogin(e) {
    e.preventDefault();
    
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const errorDiv = document.getElementById('login-error');
    const submitBtn = document.getElementById('submit-login');
    
    // Show loading state
    submitBtn.textContent = 'Signing In...';
    submitBtn.disabled = true;
    
    try {
        // Attempt login
        const response = await apiService.login(email, password);
        
        console.log('Login successful:', response);
        
        // Hide modal and update UI
        hideLoginModal();
        updateAuthUI();
        
        // Refresh dashboard with authenticated data
        await initializeApiIntegration();
        
    } catch (error) {
        console.error('Login failed:', error);
        
        // Show error message
        if (errorDiv) {
            const errorText = errorDiv.querySelector('p');
            errorText.textContent = error.message || 'Login failed. Please check your credentials and try again.';
            errorDiv.classList.remove('hidden');
        }
    } finally {
        // Reset button state
        submitBtn.textContent = 'Sign In';
        submitBtn.disabled = false;
    }
}

// Handle logout
async function handleLogout() {
    try {
        await apiService.logout();
    } catch (error) {
        console.error('Logout error:', error);
    } finally {
        updateAuthUI();
        // Refresh dashboard with public data
        await initializeApiIntegration();
    }
}

// Update authentication UI
function updateAuthUI() {
    const loginBtn = document.getElementById('login-btn');
    const userMenu = document.getElementById('user-menu');
    const userInitials = document.getElementById('user-initials');
    
    if (apiService.isAuthenticated()) {
        // Show user menu, hide login button
        loginBtn?.classList.add('hidden');
        userMenu?.classList.remove('hidden');
        userMenu?.classList.add('flex');
        
        // You could fetch user profile here and update initials
        // For now, we'll keep the default "JD"
    } else {
        // Show login button, hide user menu
        loginBtn?.classList.remove('hidden');
        userMenu?.classList.add('hidden');
        userMenu?.classList.remove('flex');
    }
}

// Initialize API integration
async function initializeApiIntegration() {
    try {
        // Check API health
        const health = await apiService.checkApiHealth();
        console.log('API Health:', health);
        
        // Initialize different sections
        await Promise.all([
            initializePropertyGrid(),
            initializeDashboardStats(),
            initializeCharts(),
            initializeRecentActivity()
        ]);
        
        // Initialize search functionality
        initializePropertySearch();
        
        console.log('Dashboard initialized with API data');
    } catch (error) {
        console.error('Failed to initialize API integration:', error);
        // Fallback to mock data if API fails
        console.log('Falling back to mock data');
        initializePropertyGrid();
        initializeCharts();
        initializeRecentActivity();
    }
}

// Particle system for hero background
function initializeParticles() {
    const container = document.getElementById('particles');
    if (!container) return;

    // Create canvas for particles
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    container.appendChild(canvas);

    // Set canvas size
    function resizeCanvas() {
        canvas.width = container.offsetWidth;
        canvas.height = container.offsetHeight;
    }
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Particle system
    const particles = [];
    const particleCount = 50;

    class Particle {
        constructor() {
            this.x = Math.random() * canvas.width;
            this.y = Math.random() * canvas.height;
            this.vx = (Math.random() - 0.5) * 0.5;
            this.vy = (Math.random() - 0.5) * 0.5;
            this.radius = Math.random() * 2 + 1;
            this.opacity = Math.random() * 0.5 + 0.2;
        }

        update() {
            this.x += this.vx;
            this.y += this.vy;

            if (this.x < 0 || this.x > canvas.width) this.vx *= -1;
            if (this.y < 0 || this.y > canvas.height) this.vy *= -1;
        }

        draw() {
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(74, 144, 226, ${this.opacity})`;
            ctx.fill();
        }
    }

    // Initialize particles
    for (let i = 0; i < particleCount; i++) {
        particles.push(new Particle());
    }

    // Animation loop
    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        particles.forEach(particle => {
            particle.update();
            particle.draw();
        });

        // Draw connections
        particles.forEach((particle, i) => {
            particles.slice(i + 1).forEach(otherParticle => {
                const dx = particle.x - otherParticle.x;
                const dy = particle.y - otherParticle.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < 100) {
                    ctx.beginPath();
                    ctx.moveTo(particle.x, particle.y);
                    ctx.lineTo(otherParticle.x, otherParticle.y);
                    ctx.strokeStyle = `rgba(74, 144, 226, ${0.1 * (1 - distance / 100)})`;
                    ctx.stroke();
                }
            });
        });

        requestAnimationFrame(animate);
    }
    animate();
}

// Typed text animation
function initializeTypedText() {
    const element = document.getElementById('typed-text');
    if (!element) return;

    new Typed('#typed-text', {
        strings: [
            'Real Estate Intelligence',
            'Market Analytics Platform',
            'Property Management Hub'
        ],
        typeSpeed: 80,
        backSpeed: 50,
        backDelay: 2000,
        loop: true,
        showCursor: false
    });
}

// Animated counters
function initializeCounters() {
    const counters = document.querySelectorAll('.stats-counter');
    
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    counters.forEach(counter => observer.observe(counter));
}

function animateCounter(element) {
    const target = parseInt(element.getAttribute('data-count'));
    const duration = 2000;
    const start = performance.now();

    function updateCounter(currentTime) {
        const elapsed = currentTime - start;
        const progress = Math.min(elapsed / duration, 1);
        
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = Math.floor(target * easeOutQuart);

        if (element.textContent.includes('€')) {
            element.textContent = `€${current}K`;
        } else {
            element.textContent = current.toLocaleString();
        }

        if (progress < 1) {
            requestAnimationFrame(updateCounter);
        }
    }

    requestAnimationFrame(updateCounter);
}

// Property grid initialization
async function initializePropertyGrid() {
    const grid = document.getElementById('property-grid');
    if (!grid) return;

    // Show loading state
    grid.innerHTML = '<div class="col-span-full text-center py-8"><div class="text-gray-600">Loading properties...</div></div>';

    try {
        let properties;
        
        // Try to fetch from API first
        if (apiService.isAuthenticated()) {
            try {
                const response = await apiService.getProperties({ limit: 6 });
                properties = response.data || response;
                console.log('Loaded properties from API:', properties.length);
            } catch (error) {
                console.warn('Failed to load properties from API, using public search:', error);
                const response = await apiService.searchProperties({ limit: 6 });
                properties = response.data || response.properties || [];
            }
        } else {
            // Use public search for unauthenticated users
            try {
                const response = await apiService.searchProperties({ limit: 6 });
                properties = response.data || response.properties || [];
            } catch (error) {
                console.warn('Public search failed, using mock data:', error);
                properties = mockProperties;
            }
        }
        
        // Clear loading state
        grid.innerHTML = '';
        
        // If no properties from API, use mock data
        if (!properties || properties.length === 0) {
            console.log('No API data available, using mock properties');
            properties = mockProperties;
        }

        properties.slice(0, 6).forEach((property, index) => {
            const propertyCard = createPropertyCard(property, index);
            grid.appendChild(propertyCard);
        });

        // Animate cards on scroll
        const cards = grid.querySelectorAll('.property-card');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    anime({
                        targets: entry.target,
                        opacity: [0, 1],
                        translateY: [50, 0],
                        delay: index * 100,
                        duration: 800,
                        easing: 'easeOutQuart'
                    });
                    observer.unobserve(entry.target);
                }
            });
        });

        cards.forEach(card => observer.observe(card));
        
    } catch (error) {
        console.error('Error loading properties:', error);
        grid.innerHTML = '<div class="col-span-full text-center py-8"><div class="text-red-600">Error loading properties. Please try again later.</div></div>';
    }
}

function createPropertyCard(property, index) {
    const card = document.createElement('div');
    card.className = 'property-card bg-white rounded-xl shadow-lg overflow-hidden card-hover opacity-0';
    
    // Handle both API and mock data formats
    const propertyData = normalizePropertyData(property);
    
    card.innerHTML = `
        <div class="relative">
            <img src="${propertyData.image}" alt="${propertyData.title}" class="w-full h-48 object-cover" onerror="this.src='https://via.placeholder.com/400x300/e2e8f0/64748b?text=Property+Image'">
            <div class="absolute top-4 right-4">
                <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    ${propertyData.status}
                </span>
            </div>
            <div class="absolute bottom-4 left-4">
                <span class="bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
                    ${propertyData.area} m²
                </span>
            </div>
        </div>
        <div class="p-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">${propertyData.title}</h3>
            <p class="text-2xl font-bold text-blue-600 mb-4">€${propertyData.price.toLocaleString()}</p>
            <div class="flex items-center text-gray-600 mb-4">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                ${propertyData.city}
            </div>
            <div class="flex items-center justify-between text-gray-600 mb-4">
                <span>${propertyData.bedrooms} beds</span>
                <span>${propertyData.bathrooms} baths</span>
                <span>${propertyData.type}</span>
            </div>
            <div class="flex flex-wrap gap-2 mb-4">
                ${propertyData.features.map(feature => 
                    `<span class="bg-gray-100 text-gray-700 px-2 py-1 rounded text-sm">${feature}</span>`
                ).join('')}
            </div>
            <div class="flex space-x-3">
                <button class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors" onclick="viewPropertyDetails('${propertyData.id}')">
                    View Details
                </button>
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-lg font-medium transition-colors">
                    ♡
                </button>
            </div>
        </div>
    `;

    return card;
}

// Initialize dashboard statistics
async function initializeDashboardStats() {
    try {
        if (!apiService.isAuthenticated()) {
            console.log('User not authenticated, using mock stats');
            return;
        }
        
        // Fetch dashboard data and statistics
        const [dashboardResponse, statsResponse] = await Promise.allSettled([
            apiService.getPropertyOwnerDashboard(),
            apiService.getPropertyOwnerStatistics('30d')
        ]);
        
        let dashboardData = null;
        let statsData = null;
        
        if (dashboardResponse.status === 'fulfilled') {
            dashboardData = dashboardResponse.value.data;
        }
        
        if (statsResponse.status === 'fulfilled') {
            statsData = statsResponse.value.data;
        }
        
        // Update stats counters
        updateStatsCounters(dashboardData, statsData);
        
        console.log('Dashboard statistics updated from API');
        
    } catch (error) {
        console.error('Error loading dashboard statistics:', error);
    }
}

// Update statistics counters with API data
function updateStatsCounters(dashboardData, statsData) {
    // Total Properties
    const totalPropertiesElement = document.querySelector('[data-count="2847"]');
    if (totalPropertiesElement && dashboardData?.properties?.total) {
        totalPropertiesElement.setAttribute('data-count', dashboardData.properties.total);
    }
    
    // Active Listings
    const activeListingsElement = document.querySelector('[data-count="1923"]');
    if (activeListingsElement && dashboardData?.properties?.active) {
        activeListingsElement.setAttribute('data-count', dashboardData.properties.active);
    }
    
    // Applications
    const applicationsElement = document.querySelector('[data-count="847"]');
    if (applicationsElement && statsData?.applications?.total) {
        applicationsElement.setAttribute('data-count', statsData.applications.total);
    }
    
    // Average Price (this might need to be calculated from property data)
    const avgPriceElement = document.querySelector('[data-count="425"]');
    if (avgPriceElement && statsData?.averageRent) {
        const avgPriceInK = Math.round(statsData.averageRent / 1000);
        avgPriceElement.setAttribute('data-count', avgPriceInK);
    }
    
    // Re-trigger counter animations for updated values
    setTimeout(() => {
        const counters = document.querySelectorAll('.stats-counter');
        counters.forEach(counter => {
            const currentValue = parseInt(counter.textContent.replace(/[^\d]/g, '')) || 0;
            const targetValue = parseInt(counter.getAttribute('data-count')) || 0;
            
            if (currentValue !== targetValue) {
                animateCounter(counter);
            }
        });
    }, 100);
}

// Normalize property data to handle different API formats
function normalizePropertyData(property) {
    return {
        id: property._id || property.id,
        title: property.title || 'Property Title',
        price: property.rent?.amount || property.price || 0,
        type: property.propertyType || property.type || 'Property',
        bedrooms: property.bedrooms || 0,
        bathrooms: property.bathrooms || 0,
        area: property.size || property.area || 0,
        city: property.address?.city || property.city || 'Unknown',
        image: (property.images && property.images.length > 0) 
               ? (property.images.find(img => img.isPrimary)?.url || property.images[0]?.url)
               : property.image || 'https://via.placeholder.com/400x300/e2e8f0/64748b?text=Property+Image',
        features: extractFeatures(property),
        status: normalizeStatus(property.status)
    };
}

// Extract features from API property data
function extractFeatures(property) {
    const features = [];
    
    if (property.features) {
        if (property.features.furnished) features.push('Furnished');
        if (property.features.parking) features.push('Parking');
        if (property.features.balcony) features.push('Balcony');
        if (property.features.garden) features.push('Garden');
        if (property.features.elevator) features.push('Elevator');
    }
    
    if (property.policies) {
        if (property.policies.petsAllowed) features.push('Pets Allowed');
        if (property.policies.studentsAllowed) features.push('Student Friendly');
        if (property.policies.expatFriendly) features.push('Expat Friendly');
    }
    
    // Fallback to mock features if no API features
    return features.length > 0 ? features : (property.features || ['Modern']);
}

// Normalize status from API format
function normalizeStatus(status) {
    const statusMap = {
        'active': 'Available',
        'draft': 'Draft',
        'rented': 'Rented',
        'maintenance': 'Maintenance',
        'inactive': 'Inactive'
    };
    
    return statusMap[status] || status || 'Available';
}

// Handle property details view
function viewPropertyDetails(propertyId) {
    console.log('Viewing property details for:', propertyId);
    // You can implement navigation to property details page here
    alert(`Property details for ${propertyId} - Feature coming soon!`);
}

// Initialize charts
async function initializeCharts() {
    try {
        let chartData = null;
        
        // Try to fetch statistics data for charts
        if (apiService.isAuthenticated()) {
            try {
                const response = await apiService.getPropertyOwnerStatistics('1y');
                chartData = response.data;
                console.log('Loaded chart data from API');
            } catch (error) {
                console.warn('Failed to load chart data from API:', error);
            }
        }
        
        // Initialize charts with API data or fallback to mock data
        initializePriceTrendChart(chartData);
        initializePropertyTypesChart(chartData);
        initializeMarketDistributionChart(chartData);
        initializeActivityChart(chartData);
        
    } catch (error) {
        console.error('Error initializing charts:', error);
        // Fallback to mock data
        initializePriceTrendChart();
        initializePropertyTypesChart();
        initializeMarketDistributionChart();
        initializeActivityChart();
    }
}

function initializePriceTrendChart(apiData) {
    const chartDom = document.getElementById('price-trend-chart');
    if (!chartDom) return;

    const myChart = echarts.init(chartDom);
    
    // Use API data if available, otherwise fallback to mock data
    let chartData = marketData.priceTrends;
    if (apiData && apiData.priceTrends) {
        chartData = apiData.priceTrends;
    } else if (apiData && apiData.monthlyStats) {
        // Transform API monthly stats to price trend format
        chartData = {
            months: apiData.monthlyStats.map(stat => stat.month) || marketData.priceTrends.months,
            prices: apiData.monthlyStats.map(stat => Math.round(stat.averagePrice / 1000)) || marketData.priceTrends.prices
        };
    }
    
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: chartData.months,
            axisLine: {
                lineStyle: {
                    color: '#e2e8f0'
                }
            },
            axisLabel: {
                color: '#64748b'
            }
        },
        yAxis: {
            type: 'value',
            name: 'Price (€K)',
            axisLine: {
                lineStyle: {
                    color: '#e2e8f0'
                }
            },
            axisLabel: {
                color: '#64748b'
            },
            splitLine: {
                lineStyle: {
                    color: '#f1f5f9'
                }
            }
        },
        series: [{
            name: 'Average Price',
            type: 'line',
            smooth: true,
            data: chartData.prices,
            lineStyle: {
                color: '#4a90e2',
                width: 3
            },
            itemStyle: {
                color: '#4a90e2'
            },
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: 'rgba(74, 144, 226, 0.3)'
                    }, {
                        offset: 1, color: 'rgba(74, 144, 226, 0.05)'
                    }]
                }
            }
        }]
    };

    myChart.setOption(option);
    
    // Animate chart on load
    setTimeout(() => {
        myChart.resize();
    }, 100);
}

function initializePropertyTypesChart(apiData) {
    const chartDom = document.getElementById('property-types-chart');
    if (!chartDom) return;

    const myChart = echarts.init(chartDom);
    
    // Use API data if available, otherwise fallback to mock data
    let chartData = marketData.propertyTypes;
    if (apiData && apiData.propertyTypes) {
        chartData = apiData.propertyTypes;
    } else if (apiData && apiData.propertyDistribution) {
        // Transform API property distribution to chart format
        const colors = ['#5a9bd4', '#f4a261', '#e76f51', '#2a9d8f', '#9b59b6'];
        chartData = apiData.propertyDistribution.map((item, index) => ({
            name: item.type || item.name,
            value: item.count || item.value,
            color: colors[index % colors.length]
        }));
    }
    
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c}% ({d}%)'
        },
        legend: {
            orient: 'vertical',
            left: 'left',
            textStyle: {
                color: '#64748b'
            }
        },
        series: [{
            name: 'Property Types',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['60%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
                borderRadius: 8,
                borderColor: '#fff',
                borderWidth: 2
            },
            label: {
                show: false,
                position: 'center'
            },
            emphasis: {
                label: {
                    show: true,
                    fontSize: '18',
                    fontWeight: 'bold',
                    color: '#374151'
                }
            },
            labelLine: {
                show: false
            },
            data: chartData.map(item => ({
                value: item.value,
                name: item.name,
                itemStyle: {
                    color: item.color
                }
            }))
        }]
    };

    myChart.setOption(option);
}

function initializeMarketDistributionChart(apiData) {
    const chartDom = document.getElementById('market-distribution-chart');
    if (!chartDom) return;

    const myChart = echarts.init(chartDom);
    
    // Use API data if available, otherwise fallback to mock data
    let chartData = marketData.marketDistribution;
    if (apiData && apiData.marketDistribution) {
        chartData = apiData.marketDistribution;
    } else if (apiData && apiData.cityDistribution) {
        chartData = apiData.cityDistribution.map(item => ({
            name: item.city || item.name,
            value: item.count || item.value
        }));
    }
    
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'value',
            axisLine: {
                lineStyle: {
                    color: '#e2e8f0'
                }
            },
            axisLabel: {
                color: '#64748b'
            },
            splitLine: {
                lineStyle: {
                    color: '#f1f5f9'
                }
            }
        },
        yAxis: {
            type: 'category',
            data: chartData.map(item => item.name),
            axisLine: {
                lineStyle: {
                    color: '#e2e8f0'
                }
            },
            axisLabel: {
                color: '#64748b'
            }
        },
        series: [{
            name: 'Market Share',
            type: 'bar',
            data: chartData.map(item => ({
                value: item.value,
                itemStyle: {
                    color: '#5a9bd4',
                    borderRadius: [0, 4, 4, 0]
                }
            })),
            barWidth: '60%'
        }]
    };

    myChart.setOption(option);
}

function initializeActivityChart(apiData) {
    const chartDom = document.getElementById('activity-chart');
    if (!chartDom) return;

    const myChart = echarts.init(chartDom);
    
    // Use API data if available, otherwise fallback to mock data
    let chartData = marketData.monthlyActivity;
    if (apiData && apiData.monthlyActivity) {
        chartData = apiData.monthlyActivity;
    } else if (apiData && apiData.monthlyStats) {
        // Transform API monthly stats to activity format
        chartData = {
            months: apiData.monthlyStats.map(stat => stat.month) || marketData.monthlyActivity.months,
            listings: apiData.monthlyStats.map(stat => stat.newListings || 0) || marketData.monthlyActivity.listings,
            applications: apiData.monthlyStats.map(stat => stat.applications || 0) || marketData.monthlyActivity.applications
        };
    }
    
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            }
        },
        legend: {
            data: ['New Listings', 'Applications'],
            textStyle: {
                color: '#64748b'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: chartData.months,
            axisLine: {
                lineStyle: {
                    color: '#e2e8f0'
                }
            },
            axisLabel: {
                color: '#64748b'
            }
        },
        yAxis: {
            type: 'value',
            axisLine: {
                lineStyle: {
                    color: '#e2e8f0'
                }
            },
            axisLabel: {
                color: '#64748b'
            },
            splitLine: {
                lineStyle: {
                    color: '#f1f5f9'
                }
            }
        },
        series: [
            {
                name: 'New Listings',
                type: 'bar',
                data: chartData.listings,
                itemStyle: {
                    color: '#5a9bd4',
                    borderRadius: [4, 4, 0, 0]
                }
            },
            {
                name: 'Applications',
                type: 'line',
                data: chartData.applications,
                lineStyle: {
                    color: '#f4a261',
                    width: 3
                },
                itemStyle: {
                    color: '#f4a261'
                }
            }
        ]
    };

    myChart.setOption(option);
}

// Initialize recent activity
async function initializeRecentActivity() {
    try {
        let recentData = null;
        let notifications = null;
        
        // Try to fetch recent activity from API
        if (apiService.isAuthenticated()) {
            try {
                // Fetch recent properties and notifications
                const [propertiesResponse, notificationsResponse] = await Promise.allSettled([
                    apiService.getProperties({ limit: 4, sort: 'createdAt:desc' }),
                    apiService.getNotifications({ limit: 4 })
                ]);
                
                if (propertiesResponse.status === 'fulfilled') {
                    recentData = propertiesResponse.value.data || propertiesResponse.value;
                }
                
                if (notificationsResponse.status === 'fulfilled') {
                    notifications = notificationsResponse.value.data || notificationsResponse.value;
                }
                
            } catch (error) {
                console.warn('Failed to load recent activity from API:', error);
            }
        }
        
        initializeRecentListings(recentData);
        initializeSystemAlerts(notifications);
        
    } catch (error) {
        console.error('Error loading recent activity:', error);
        // Fallback to mock data
        initializeRecentListings();
        initializeSystemAlerts();
    }
}

function initializeRecentListings(apiData) {
    const container = document.getElementById('recent-listings');
    if (!container) return;

    container.innerHTML = '';
    
    // Use API data if available, otherwise fallback to mock data
    let listings = systemData.recentListings;
    if (apiData && Array.isArray(apiData) && apiData.length > 0) {
        listings = apiData.slice(0, 4).map(property => ({
            title: property.title || 'Property',
            price: property.rent?.amount || property.price || 0,
            time: formatTimeAgo(property.createdAt || property.updatedAt) || 'Recently'
        }));
    }

    listings.forEach((listing, index) => {
        const item = document.createElement('div');
        item.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer';
        
        item.innerHTML = `
            <div>
                <h4 class="font-medium text-gray-900">${listing.title}</h4>
                <p class="text-sm text-gray-600">€${listing.price.toLocaleString()}</p>
            </div>
            <div class="text-sm text-gray-500">${listing.time}</div>
        `;

        container.appendChild(item);
    });
}

function initializeSystemAlerts(apiData) {
    const container = document.getElementById('system-alerts');
    if (!container) return;

    container.innerHTML = '';
    
    // Use API data if available, otherwise fallback to mock data
    let alerts = systemData.alerts;
    if (apiData && Array.isArray(apiData) && apiData.length > 0) {
        alerts = apiData.slice(0, 4).map(notification => ({
            type: mapNotificationType(notification.type) || 'info',
            message: notification.message || notification.title || 'New notification',
            time: formatTimeAgo(notification.createdAt) || 'Recently'
        }));
    }

    alerts.forEach((alert, index) => {
        const item = document.createElement('div');
        item.className = 'flex items-center p-3 rounded-lg cursor-pointer transition-colors';
        
        const typeColors = {
            success: 'bg-green-50 hover:bg-green-100',
            warning: 'bg-yellow-50 hover:bg-yellow-100',
            error: 'bg-red-50 hover:bg-red-100',
            info: 'bg-blue-50 hover:bg-blue-100'
        };

        const iconColors = {
            success: 'text-green-600',
            warning: 'text-yellow-600',
            error: 'text-red-600',
            info: 'text-blue-600'
        };

        item.className += ` ${typeColors[alert.type]}`;
        
        item.innerHTML = `
            <div class="w-8 h-8 rounded-full flex items-center justify-center mr-3 ${iconColors[alert.type]} bg-current bg-opacity-20">
                ${getAlertIcon(alert.type)}
            </div>
            <div class="flex-1">
                <p class="text-sm font-medium text-gray-900">${alert.message}</p>
                <p class="text-xs text-gray-500">${alert.time}</p>
            </div>
        `;

        container.appendChild(item);
    });
}

function getAlertIcon(type) {
    const icons = {
        success: '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>',
        warning: '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
        error: '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
        info: '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
    };
    return icons[type] || icons.info;
}

// Initialize general animations
function initializeAnimations() {
    // Filter chip animations are now handled in initializePropertySearch()
    // to integrate with search functionality

    // Animate cards on hover
    const cards = document.querySelectorAll('.card-hover');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            anime({
                targets: this,
                scale: 1.02,
                translateY: -8,
                duration: 300,
                easing: 'easeOutQuart'
            });
        });

        card.addEventListener('mouseleave', function() {
            anime({
                targets: this,
                scale: 1,
                translateY: 0,
                duration: 300,
                easing: 'easeOutQuart'
            });
        });
    });

    // Smooth scroll animations
    const scrollElements = document.querySelectorAll('section');
    const scrollObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const elements = entry.target.querySelectorAll('.card-hover, .chart-container');
                anime({
                    targets: elements,
                    opacity: [0, 1],
                    translateY: [30, 0],
                    delay: anime.stagger(100),
                    duration: 800,
                    easing: 'easeOutQuart'
                });
                scrollObserver.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    scrollElements.forEach(section => scrollObserver.observe(section));
}

// Utility functions
function formatPrice(price) {
    return new Intl.NumberFormat('nl-NL', {
        style: 'currency',
        currency: 'EUR'
    }).format(price);
}

function formatDate(date) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    }).format(date);
}

// Format time ago helper
function formatTimeAgo(dateString) {
    if (!dateString) return 'Recently';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    
    if (diffDay > 0) {
        return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
    } else if (diffHour > 0) {
        return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
    } else if (diffMin > 0) {
        return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
    } else {
        return 'Just now';
    }
}

// Map API notification types to alert types
function mapNotificationType(apiType) {
    const typeMap = {
        'application_received': 'info',
        'application_approved': 'success',
        'application_rejected': 'warning',
        'property_listed': 'success',
        'property_error': 'error',
        'system_maintenance': 'warning',
        'payment_due': 'warning',
        'payment_completed': 'success',
        'inspection_scheduled': 'info'
    };
    
    return typeMap[apiType] || 'info';
}

// Property search functionality
function initializePropertySearch() {
    const searchButton = document.querySelector('button:contains("Search Properties")');
    if (!searchButton) {
        // Find the search button by its text content
        const buttons = document.querySelectorAll('button');
        const searchBtn = Array.from(buttons).find(btn => btn.textContent.includes('Search Properties'));
        if (searchBtn) {
            searchBtn.addEventListener('click', handlePropertySearch);
        }
    }
    
    // Add event listeners to filter chips
    const filterChips = document.querySelectorAll('.filter-chip');
    filterChips.forEach(chip => {
        chip.addEventListener('click', function() {
            this.classList.toggle('bg-blue-600');
            this.classList.toggle('text-white');
            this.classList.toggle('bg-blue-100');
            this.classList.toggle('text-blue-800');
            
            // Trigger search with updated filters
            handlePropertySearch();
        });
    });
}

// Handle property search
async function handlePropertySearch() {
    try {
        // Collect search parameters
        const searchParams = collectSearchParameters();
        
        console.log('Searching properties with params:', searchParams);
        
        // Show loading state
        const grid = document.getElementById('property-grid');
        if (grid) {
            grid.innerHTML = '<div class="col-span-full text-center py-8"><div class="text-gray-600">Searching properties...</div></div>';
        }
        
        // Search properties using API
        let properties = [];
        
        if (apiService.isAuthenticated()) {
            try {
                const response = await apiService.getProperties(searchParams);
                properties = response.data || response;
            } catch (error) {
                console.warn('Authenticated search failed, trying public search:', error);
                const response = await apiService.searchProperties(searchParams);
                properties = response.data || response.properties || [];
            }
        } else {
            const response = await apiService.searchProperties(searchParams);
            properties = response.data || response.properties || [];
        }
        
        // Update property grid with search results
        displaySearchResults(properties);
        
    } catch (error) {
        console.error('Property search failed:', error);
        const grid = document.getElementById('property-grid');
        if (grid) {
            grid.innerHTML = '<div class="col-span-full text-center py-8"><div class="text-red-600">Search failed. Please try again.</div></div>';
        }
    }
}

// Collect search parameters from form
function collectSearchParameters() {
    const params = {};
    
    // Location
    const locationSelect = document.querySelector('select');
    if (locationSelect && locationSelect.value !== 'All Cities') {
        params.city = locationSelect.value;
    }
    
    // Property Type
    const typeSelects = document.querySelectorAll('select');
    if (typeSelects.length > 1) {
        const typeSelect = typeSelects[1];
        if (typeSelect.value !== 'All Types') {
            params.propertyType = typeSelect.value.toLowerCase();
        }
    }
    
    // Price Range
    const priceInputs = document.querySelectorAll('input[type="number"]');
    if (priceInputs.length >= 2) {
        const minPrice = priceInputs[0].value;
        const maxPrice = priceInputs[1].value;
        
        if (minPrice) params.minPrice = parseInt(minPrice);
        if (maxPrice) params.maxPrice = parseInt(maxPrice);
    }
    
    // Active filter chips
    const activeChips = document.querySelectorAll('.filter-chip.bg-blue-600');
    const features = [];
    activeChips.forEach(chip => {
        const feature = chip.textContent.toLowerCase().replace(/\s+/g, '');
        features.push(feature);
    });
    
    if (features.length > 0) {
        params.features = features;
    }
    
    // Set default limit for search
    params.limit = 12;
    
    return params;
}

// Display search results
function displaySearchResults(properties) {
    const grid = document.getElementById('property-grid');
    if (!grid) return;
    
    grid.innerHTML = '';
    
    if (!properties || properties.length === 0) {
        grid.innerHTML = '<div class="col-span-full text-center py-8"><div class="text-gray-600">No properties found matching your criteria.</div></div>';
        return;
    }
    
    properties.forEach((property, index) => {
        const propertyCard = createPropertyCard(property, index);
        grid.appendChild(propertyCard);
    });
    
    // Animate new cards
    const cards = grid.querySelectorAll('.property-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        anime({
            targets: card,
            opacity: [0, 1],
            translateY: [30, 0],
            delay: index * 100,
            duration: 600,
            easing: 'easeOutQuart'
        });
    });
}

// Export functions for other pages
window.ZakMakelaarDashboard = {
    mockProperties,
    marketData,
    systemData,
    formatPrice,
    formatDate,
    apiService,
    handlePropertySearch,
    initializePropertySearch
};
