const User = require("../models/User");
const Listing = require("../models/Listing");
const config = require("../config/config");
const aiService = require("./aiService");

// Lazy initialization of external services to avoid errors when credentials are not configured
let sgMail = null;
let twilioClient = null;

// Initialize SendGrid only if API key is provided
const initSendGrid = () => {
  if (!sgMail && config.sendgridApiKey) {
    try {
      sgMail = require("@sendgrid/mail");
      sgMail.setApiKey(config.sendgridApiKey);
      console.log(`✅ [Alert Service] SendGrid initialized successfully`);
    } catch (error) {
      console.error(
        `❌ [Alert Service] SendGrid initialization failed: ${error.message}`
      );
      return null;
    }
  } else if (!config.sendgridApiKey) {
    console.warn(
      `⚠️ [Alert Service] SendGrid API key not configured - email alerts disabled`
    );
    console.warn(
      `   Set SENDGRID_API_KEY in .env to enable email notifications`
    );
  }
  return sgMail;
};

// Initialize Twilio only if credentials are provided
const initTwilio = () => {
  if (!twilioClient && config.twilioAccountSid && config.twilioAuthToken) {
    const twilio = require("twilio");
    twilioClient = twilio(config.twilioAccountSid, config.twilioAuthToken);
  }
  return twilioClient;
};

const sendAlerts = async (listing) => {
  console.log(
    `📢 [Alert Service] Starting alert processing for listing: ${listing.title}`
  );
  console.log(`   URL: ${listing.url}`);
  console.log(`   Location: ${listing.location} | Price: ${listing.price}`);

  const users = await User.find();
  console.log(`👥 [Alert Service] Found ${users.length} users to process`);

  let successCount = 0;
  let errorCount = 0;
  let aiFailureCount = 0;
  let basicAlertCount = 0;

  for (const user of users) {
    try {
      console.log(`🔍 [Alert Service] Processing user: ${user.email}`);

      // Use AI for smarter matching
      let matchResult;
      try {
        console.log(`🤖 [Alert Service] Running AI matching for ${user.email}`);
        matchResult = await aiService.matchListingToUser(
          listing,
          user.preferences
        );
        console.log(
          `✅ [Alert Service] AI matching completed - Score: ${matchResult.score}/100`
        );
      } catch (aiError) {
        console.error(
          `❌ [Alert Service] AI matching failed for ${user.email}: ${aiError.message}`
        );
        aiFailureCount++;

        // Fallback to basic matching if AI fails
        if (matchesBasicPreferences(listing, user.preferences)) {
          console.log(
            `🔄 [Alert Service] Using basic matching fallback for ${user.email}`
          );
          await sendBasicAlert(user, listing);
          basicAlertCount++;
        } else {
          console.log(
            `❌ [Alert Service] Basic matching also failed for ${user.email}`
          );
        }
        continue;
      }

      // Check if match score meets user's threshold
      const threshold = user.aiSettings?.matchThreshold || 70;
      console.log(
        `🎯 [Alert Service] Match score ${matchResult.score} vs threshold ${threshold}`
      );

      if (matchResult.score >= threshold) {
        console.log(
          `✅ [Alert Service] Match score meets threshold - sending personalized alert`
        );
        await sendPersonalizedAlert(user, listing, matchResult);
        successCount++;
      } else {
        console.log(
          `❌ [Alert Service] Match score below threshold - skipping alert`
        );
      }
    } catch (error) {
      console.error(
        `❌ [Alert Service] Error processing alert for user ${user.email}:`,
        error.message
      );
      console.error(`   Error type: ${error.constructor.name}`);
      errorCount++;

      // Fallback to basic matching if everything else fails
      try {
        if (matchesBasicPreferences(listing, user.preferences)) {
          console.log(
            `🔄 [Alert Service] Final fallback - basic alert for ${user.email}`
          );
          await sendBasicAlert(user, listing);
          basicAlertCount++;
        }
      } catch (fallbackError) {
        console.error(
          `❌ [Alert Service] Even basic alert failed for ${user.email}:`,
          fallbackError.message
        );
      }
    }
  }

  // Summary logging
  console.log(`📈 [Alert Service] Alert processing completed:`);
  console.log(`   ✅ Successful personalized alerts: ${successCount}`);
  console.log(`   🔄 Basic fallback alerts: ${basicAlertCount}`);
  console.log(`   🤖 AI failures: ${aiFailureCount}`);
  console.log(`   ❌ Total errors: ${errorCount}`);
  console.log(`   👥 Total users processed: ${users.length}`);
};

const sendPersonalizedAlert = async (user, listing, matchResult) => {
  const { aiSettings } = user;
  const language = aiSettings?.preferredLanguage || "english";

  console.log(
    `📧 [Alert Service] Generating personalized alert for ${user.email}`
  );
  console.log(`   Listing: ${listing.title} | Language: ${language}`);
  console.log(`   Match Score: ${matchResult.score}/100`);

  try {
    // Generate personalized summary if AI is available
    let summary = null;
    try {
      console.log(`🤖 [Alert Service] Generating AI summary for listing`);
      summary = await aiService.summarizeListing(listing, language);
      console.log(`✅ [Alert Service] AI summary generated successfully`);
    } catch (aiError) {
      console.error(
        `❌ [Alert Service] AI summarization failed: ${aiError.message}`
      );
      console.error(`   Falling back to basic alert for ${user.email}`);
      // Continue without summary rather than failing completely
    }

    // Generate market analysis if enabled
    let marketAnalysis = null;
    if (aiSettings?.includeMarketAnalysis) {
      try {
        console.log(`📈 [Alert Service] Generating market analysis`);
        marketAnalysis = await aiService.analyzeMarketTrends(
          listing.location,
          listing.propertyType,
          {} // historical data would be passed here
        );
        console.log(`✅ [Alert Service] Market analysis completed`);
      } catch (error) {
        console.error(
          `❌ [Alert Service] Market analysis failed: ${error.message}`
        );
        // Continue without market analysis
      }
    }

    // Send email alert
    console.log(`📨 [Alert Service] Sending enhanced email to ${user.email}`);
    await sendEnhancedEmail(
      user.email,
      listing,
      matchResult,
      summary,
      marketAnalysis
    );

    // Send WhatsApp alert
    if (user.profile?.phone) {
      console.log(
        `📱 [Alert Service] Sending WhatsApp alert to ${user.profile.phone}`
      );
      await sendEnhancedWhatsApp(
        user.profile?.phone,
        listing,
        matchResult,
        summary
      );
    }

    console.log(
      `✅ [Alert Service] Personalized alert sent successfully to ${user.email}`
    );
  } catch (error) {
    console.error(
      `❌ [Alert Service] Personalized alert generation failed for ${user.email}:`,
      error.message
    );
    console.error(`   Error type: ${error.constructor.name}`);
    console.error(`   Falling back to basic alert`);

    // Fallback to basic alert
    await sendBasicAlert(user, listing);
  }
};

const sendEnhancedEmail = async (
  to,
  listing,
  matchResult,
  summary,
  marketAnalysis
) => {
  const sgMailClient = initSendGrid();

  if (!sgMailClient) {
    console.log(
      `⚠️ [Alert Service] SendGrid not configured, skipping email notification for ${to}`
    );
    return;
  }

  if (!config.sendgridFromEmail) {
    console.error(`❌ [Alert Service] SendGrid from email not configured`);
    console.error(`   Set SENDGRID_FROM_EMAIL in .env to enable email sending`);
    return;
  }

  console.log(`📧 [Alert Service] Preparing enhanced email for ${to}`);
  console.log(`   From: ${config.sendgridFromEmail}`);
  console.log(`   Subject: Perfect Match Found - ${listing.title}`);

  let emailContent = `
    <h2>🏠 New Listing Match: ${listing.title}</h2>
    <p><strong>Match Score:</strong> ${matchResult.score}/100</p>
    <p><strong>Recommendation:</strong> ${matchResult.recommendation}</p>
    
    <h3>Property Details:</h3>
    <ul>
      <li><strong>Price:</strong> ${listing.price}</li>
      <li><strong>Location:</strong> ${listing.location}</li>
      <li><strong>Size:</strong> ${listing.size}</li>
      <li><strong>Rooms:</strong> ${listing.rooms}</li>
    </ul>
    
    <h3>AI Analysis:</h3>
    <p>${matchResult.matchReasoning}</p>
    
    <h3>Key Highlights:</h3>
    <ul>
      ${matchResult.keyHighlights
        .map((highlight) => `<li>${highlight}</li>`)
        .join("")}
    </ul>
  `;

  if (summary) {
    emailContent += `
      <h3>Smart Summary:</h3>
      <p>${summary.summary}</p>
    `;
  }

  if (marketAnalysis) {
    emailContent += `
      <h3>Market Insights:</h3>
      <p><strong>Trend:</strong> ${marketAnalysis.marketTrend}</p>
      <p><strong>Demand:</strong> ${marketAnalysis.demandLevel}</p>
      <p><strong>Prediction:</strong> ${marketAnalysis.pricePrediction}</p>
    `;
  }

  if (matchResult.potentialConcerns.length > 0) {
    emailContent += `
      <h3>⚠️ Potential Concerns:</h3>
      <ul>
        ${matchResult.potentialConcerns
          .map((concern) => `<li>${concern}</li>`)
          .join("")}
      </ul>
    `;
  }

  emailContent += `
    <p><a href="${listing.url}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Listing</a></p>
  `;

  const msg = {
    to,
    from: config.sendgridFromEmail,
    subject: `🎯 Perfect Match Found: ${listing.title} (${matchResult.score}/100)`,
    html: emailContent,
  };

  try {
    console.log(`🚀 [Alert Service] Sending email via SendGrid`);
    await sgMailClient.send(msg);
    console.log(`✅ [Alert Service] Email sent successfully to ${to}`);
  } catch (error) {
    console.error(
      `❌ [Alert Service] SendGrid error for ${to}:`,
      error.message
    );

    if (error.code === 401) {
      console.error(`   🔑 Authentication failed - check SENDGRID_API_KEY`);
      console.error(
        `   Current API key: ${
          config.sendgridApiKey
            ? config.sendgridApiKey.substring(0, 10) + "..."
            : "Not set"
        }`
      );
    } else if (error.code === 403) {
      console.error(`   🚫 Forbidden - check SendGrid account permissions`);
    } else if (error.code === 400) {
      console.error(`   📝 Bad request - check email content and addresses`);
      console.error(`   To: ${to} | From: ${config.sendgridFromEmail}`);
    } else {
      console.error(`   🔍 Error details:`, {
        code: error.code,
        message: error.message,
        response: error.response?.body,
      });
    }

    // Don't throw - just log the error and continue
  }
};

const sendEnhancedWhatsApp = async (to, listing, matchResult, summary) => {
  if (!to) return;

  const twilioClientInstance = initTwilio();

  if (!twilioClientInstance) {
    console.log("Twilio not configured, skipping WhatsApp notification");
    return;
  }

  let message = `🏠 *New Perfect Match!*\n\n`;
  message += `*${listing.title}*\n`;
  message += `📍 ${listing.location}\n`;
  message += `💰 ${listing.price}\n`;
  message += `📊 Match Score: ${matchResult.score}/100\n`;
  message += `⭐ Recommendation: ${matchResult.recommendation}\n\n`;

  if (summary) {
    message += `📝 *Summary:* ${summary.summary}\n\n`;
  }

  message += `🔗 ${listing.url}`;

  twilioClientInstance.messages
    .create({
      body: message,
      from: config.twilioWhatsAppFrom,
      to: `whatsapp:${to}`,
    })
    .catch((error) => console.error("Twilio error:", error));
};

const sendBasicAlert = async (user, listing) => {
  await sendEmail(user.email, listing);
  await sendWhatsApp(user.profile?.phone, listing);
};

// Legacy basic preference matching (fallback)
const matchesBasicPreferences = (listing, preferences) => {
  if (!preferences) return false;

  const { location, budget, rooms } = preferences;

  if (
    location &&
    !listing.location.toLowerCase().includes(location.toLowerCase())
  ) {
    return false;
  }

  if (budget && listing.price > budget) {
    return false;
  }

  if (rooms && listing.size && !listing.size.includes(rooms)) {
    return false;
  }

  return true;
};

// Legacy email function (kept for fallback)
const sendEmail = (to, listing) => {
  const sgMailClient = initSendGrid();

  if (!sgMailClient) {
    console.log("SendGrid not configured, skipping email notification");
    return;
  }

  const msg = {
    to,
    from: config.sendgridFromEmail,
    subject: `New Listing: ${listing.title}`,
    html: `<p>A new listing matching your preferences is available:</p>
           <p><strong>Title:</strong> ${listing.title}</p>
           <p><strong>Price:</strong> ${listing.price}</p>
           <p><strong>Location:</strong> ${listing.location}</p>
           <p><a href="${listing.url}">View Listing</a></p>`,
  };

  sgMailClient
    .send(msg)
    .catch((error) => console.error("SendGrid error:", error));
};

// Legacy WhatsApp function (kept for fallback)
const sendWhatsApp = (to, listing) => {
  if (!to) return;

  const twilioClientInstance = initTwilio();

  if (!twilioClientInstance) {
    console.log("Twilio not configured, skipping WhatsApp notification");
    return;
  }

  const message = `New Listing: ${listing.title}\nPrice: ${listing.price}\nLocation: ${listing.location}\n${listing.url}`;

  twilioClientInstance.messages
    .create({
      body: message,
      from: config.twilioWhatsAppFrom,
      to: `whatsapp:${to}`,
    })
    .catch((error) => console.error("Twilio error:", error));
};

module.exports = { sendAlerts };
