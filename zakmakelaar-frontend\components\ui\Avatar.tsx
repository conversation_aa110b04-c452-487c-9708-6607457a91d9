import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Theme } from '../../constants/Theme';

export interface AvatarProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  source?: { uri: string } | number;
  name?: string;
  backgroundColor?: string;
  textColor?: string;
  onPress?: () => void;
  showBadge?: boolean;
  badgeColor?: string;
  placeholder?: keyof typeof Ionicons.glyphMap;
}

const sizeMap = {
  sm: { size: 32, fontSize: 14 },
  md: { size: 40, fontSize: 16 },
  lg: { size: 56, fontSize: 20 },
  xl: { size: 80, fontSize: 28 },
};

export const Avatar: React.FC<AvatarProps> = ({
  size = 'md',
  source,
  name,
  backgroundColor = Theme.colors.primary,
  textColor = Theme.colors.textInverse,
  onPress,
  showBadge = false,
  badgeColor = Theme.colors.success,
  placeholder = 'person',
}) => {
  const { size: avatarSize, fontSize } = sizeMap[size];
  
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const renderContent = () => {
    if (source) {
      return (
        <Image
          source={source}
          style={[
            styles.image,
            {
              width: avatarSize,
              height: avatarSize,
              borderRadius: avatarSize / 2,
            },
          ]}
          resizeMode="cover"
        />
      );
    }
    
    if (name) {
      return (
        <View
          style={[
            styles.container,
            {
              width: avatarSize,
              height: avatarSize,
              borderRadius: avatarSize / 2,
              backgroundColor,
            },
          ]}
        >
          <Text
            style={[
              styles.initials,
              {
                fontSize,
                color: textColor,
              },
            ]}
          >
            {getInitials(name)}
          </Text>
        </View>
      );
    }

    return (
      <View
        style={[
          styles.container,
          {
            width: avatarSize,
            height: avatarSize,
            borderRadius: avatarSize / 2,
            backgroundColor: Theme.colors.neutral[200],
          },
        ]}
      >
        <Ionicons
          name={placeholder}
          size={avatarSize * 0.5}
          color={Theme.colors.neutral[500]}
        />
      </View>
    );
  };

  const AvatarWrapper = onPress ? TouchableOpacity : View;

  return (
    <AvatarWrapper
      onPress={onPress}
      activeOpacity={0.8}
      style={styles.wrapper}
    >
      {renderContent()}
      
      {showBadge && (
        <View
          style={[
            styles.badge,
            {
              backgroundColor: badgeColor,
              width: avatarSize * 0.3,
              height: avatarSize * 0.3,
              borderRadius: (avatarSize * 0.3) / 2,
              borderWidth: size === 'sm' ? 1 : 2,
            },
          ]}
        />
      )}
    </AvatarWrapper>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    position: 'relative',
  },
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  image: {
    backgroundColor: Theme.colors.neutral[200],
  },
  initials: {
    fontWeight: Theme.typography.fontWeight.semiBold,
    textAlign: 'center',
  },
  badge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    borderColor: Theme.colors.surface,
  },
});

export default Avatar;