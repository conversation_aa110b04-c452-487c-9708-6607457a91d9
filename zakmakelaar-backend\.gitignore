# Dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Environment
.env
.env.local
.env.development
.env.test
.env.production

# Build files
dist/
build/

# Logs
logs/
*.log

# OS files
.DS_Store
Thumbs.db

# IDE files
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# Generated HTML snapshots
funda_page.html
pararius_page.html

# Debug and development files
debug_*.html
src/debug-*.js
public/test-*.html
public/simple-*.html

# Test files
test-*.js
check-*.js
cleanup-*.js
fix-*.js
*-test.js
*-debug.js
create-sample-*.js
force-clean-*.js
run-*.js
make-admin.js
migrate-*.js
setup-env.js
add-complete-*.js
debug-db-*.js
run-performance-*.js
run-unified-*.js
funda_*.html
src/tests/
debug-price-filtering.js
debug-*
*.md
screenshots/
uploads/
artifacts/
test-results/
reset-queue-for-retry.js
Dockerfile.puppeteer-test
docker-compose.test.yml
scripts/
tests/
