import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  <PERSON><PERSON>,
  Switch,
} from "react-native";
import { useRouter } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import Animated, { FadeInUp, SlideInRight } from "react-native-reanimated";
import * as Haptics from "expo-haptics";
import { useAuthStore } from "../store/authStore";

// Define theme colors to match the rest of the app
const THEME = {
  primary: "#4361ee",
  secondary: "#7209b7",
  accent: "#f72585",
  dark: "#0a0a18",
  light: "#ffffff",
  gray: "#6b7280",
  lightGray: "#f3f4f6",
  success: "#10b981",
  warning: "#f59e0b",
  danger: "#ef4444",
};

// Enhanced Header Component to match dashboard
const Header = ({
  showBackButton = false,
  onBack,
}: {
  showBackButton?: boolean;
  onBack?: () => void;
}) => {
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      colors={[THEME.primary, THEME.secondary]}
      style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Animated.View
        style={styles.headerContent}
        entering={FadeInUp.duration(600)}
      >
        {showBackButton && (
          <TouchableOpacity
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              onBack?.();
            }}
            style={styles.backButton}
            activeOpacity={0.8}
          >
            <View style={styles.backButtonInner}>
              <Ionicons name="chevron-back" size={24} color={THEME.primary} />
            </View>
          </TouchableOpacity>
        )}

        <View style={styles.headerCenter}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>ZM</Text>
          </View>
          <View style={styles.headerTextContainer}>
            <Text style={styles.headerTitle}>Profile</Text>
            <Text style={styles.headerSubtitle}>Manage your account</Text>
          </View>
        </View>

        <View style={styles.headerSpacer} />
      </Animated.View>
    </LinearGradient>
  );
};

export default function ProfileScreen() {
  const router = useRouter();
  const { user, logout } = useAuthStore();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [emailUpdates, setEmailUpdates] = useState(true);

  const handleLogout = () => {
    Alert.alert("Logout", "Are you sure you want to logout?", [
      { text: "Cancel", style: "cancel" },
      {
        text: "Logout",
        style: "destructive",
        onPress: async () => {
          // The logout function now handles navigation automatically
          await logout();
        },
      },
    ]);
  };

  const handleEditProfile = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.push("/edit-profile");
  };

  const handleChangePassword = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.push("/change-password");
  };

  const handleSupport = () => {
    Alert.alert("Support", "Contact <NAME_EMAIL>");
  };

  const handleAbout = () => {
    Alert.alert(
      "About ZakMakelaar",
      "ZakMakelaar v1.0.0\n\nYour trusted partner in finding the perfect home in the Netherlands."
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header showBackButton={true} onBack={() => router.back()} />

      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Profile Section */}
        <Animated.View
          style={styles.profileCard}
          entering={FadeInUp.duration(600).delay(200)}
        >
          <LinearGradient
            colors={["rgba(67, 97, 238, 0.1)", "rgba(114, 9, 183, 0.1)"]}
            style={styles.profileGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={styles.avatarContainer}>
              <LinearGradient
                colors={[THEME.accent, THEME.secondary]}
                style={styles.avatarGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <Ionicons name="person" size={32} color={THEME.light} />
              </LinearGradient>
            </View>
            <Text style={styles.userName}>
              {user?.firstName && user?.lastName
                ? `${user.firstName} ${user.lastName}`
                : user?.firstName || user?.lastName || "User"}
            </Text>
            <Text style={styles.userEmail}>
              {user?.email || "<EMAIL>"}
            </Text>
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                handleEditProfile();
              }}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={[THEME.primary, THEME.secondary]}
                style={styles.editButtonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <Ionicons name="create-outline" size={16} color={THEME.light} />
                <Text style={styles.editButtonText}>Edit Profile</Text>
              </LinearGradient>
            </TouchableOpacity>
          </LinearGradient>
        </Animated.View>

        {/* Settings Section */}
        <Animated.View
          style={styles.section}
          entering={FadeInUp.duration(600).delay(400)}
        >
          <Text style={styles.sectionTitle}>Settings</Text>

          <Animated.View entering={SlideInRight.duration(600).delay(500)}>
            <View style={styles.settingItem}>
              <View style={styles.settingLeft}>
                <View
                  style={[
                    styles.settingIcon,
                    { backgroundColor: "rgba(67, 97, 238, 0.1)" },
                  ]}
                >
                  <Ionicons
                    name="notifications-outline"
                    size={20}
                    color={THEME.primary}
                  />
                </View>
                <Text style={styles.settingText}>Push Notifications</Text>
              </View>
              <Switch
                value={notificationsEnabled}
                onValueChange={(value) => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  setNotificationsEnabled(value);
                }}
                trackColor={{ false: "#e5e7eb", true: THEME.accent }}
                thumbColor={THEME.light}
              />
            </View>
          </Animated.View>

          <Animated.View entering={SlideInRight.duration(600).delay(600)}>
            <View style={styles.settingItem}>
              <View style={styles.settingLeft}>
                <View
                  style={[
                    styles.settingIcon,
                    { backgroundColor: "rgba(114, 9, 183, 0.1)" },
                  ]}
                >
                  <Ionicons
                    name="mail-outline"
                    size={20}
                    color={THEME.secondary}
                  />
                </View>
                <Text style={styles.settingText}>Email Updates</Text>
              </View>
              <Switch
                value={emailUpdates}
                onValueChange={(value) => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  setEmailUpdates(value);
                }}
                trackColor={{ false: "#e5e7eb", true: THEME.accent }}
                thumbColor={THEME.light}
              />
            </View>
          </Animated.View>

          <Animated.View entering={SlideInRight.duration(600).delay(700)}>
            <TouchableOpacity
              style={styles.settingItem}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                router.push("/auto-application-settings");
              }}
              activeOpacity={0.8}
            >
              <View style={styles.settingLeft}>
                <View
                  style={[
                    styles.settingIcon,
                    { backgroundColor: "rgba(16, 185, 129, 0.1)" },
                  ]}
                >
                  <Ionicons
                    name="flash-outline"
                    size={20}
                    color={THEME.success}
                  />
                </View>
                <Text style={styles.settingText}>Smart Automation</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={THEME.gray} />
            </TouchableOpacity>
          </Animated.View>

          <Animated.View entering={SlideInRight.duration(600).delay(800)}>
            <TouchableOpacity
              style={styles.settingItem}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                handleChangePassword();
              }}
              activeOpacity={0.8}
            >
              <View style={styles.settingLeft}>
                <View
                  style={[
                    styles.settingIcon,
                    { backgroundColor: "rgba(247, 37, 133, 0.1)" },
                  ]}
                >
                  <Ionicons
                    name="lock-closed-outline"
                    size={20}
                    color={THEME.accent}
                  />
                </View>
                <Text style={styles.settingText}>Change Password</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={THEME.gray} />
            </TouchableOpacity>
          </Animated.View>
        </Animated.View>

        {/* Support Section */}
        <Animated.View
          style={styles.section}
          entering={FadeInUp.duration(600).delay(900)}
        >
          <Text style={styles.sectionTitle}>Support</Text>

          <Animated.View entering={SlideInRight.duration(600).delay(1000)}>
            <TouchableOpacity
              style={styles.settingItem}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                handleSupport();
              }}
              activeOpacity={0.8}
            >
              <View style={styles.settingLeft}>
                <View
                  style={[
                    styles.settingIcon,
                    { backgroundColor: "rgba(245, 158, 11, 0.1)" },
                  ]}
                >
                  <Ionicons
                    name="help-circle-outline"
                    size={20}
                    color={THEME.warning}
                  />
                </View>
                <Text style={styles.settingText}>Help & Support</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={THEME.gray} />
            </TouchableOpacity>
          </Animated.View>

          <Animated.View entering={SlideInRight.duration(600).delay(1100)}>
            <TouchableOpacity
              style={styles.settingItem}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                handleAbout();
              }}
              activeOpacity={0.8}
            >
              <View style={styles.settingLeft}>
                <View
                  style={[
                    styles.settingIcon,
                    { backgroundColor: "rgba(107, 114, 128, 0.1)" },
                  ]}
                >
                  <Ionicons
                    name="information-circle-outline"
                    size={20}
                    color={THEME.gray}
                  />
                </View>
                <Text style={styles.settingText}>About</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={THEME.gray} />
            </TouchableOpacity>
          </Animated.View>
        </Animated.View>

        {/* Logout Section */}
        <Animated.View
          style={styles.logoutSection}
          entering={FadeInUp.duration(600).delay(1200)}
        >
          <TouchableOpacity
            style={styles.logoutButton}
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              handleLogout();
            }}
            activeOpacity={0.8}
          >
            <View style={styles.logoutIconContainer}>
              <Ionicons name="log-out-outline" size={20} color={THEME.danger} />
            </View>
            <Text style={styles.logoutText}>Logout</Text>
          </TouchableOpacity>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  backButton: {
    marginRight: 16,
  },
  backButtonInner: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerCenter: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  logoContainer: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoText: {
    fontSize: 18,
    fontWeight: "bold",
    color: THEME.primary,
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: THEME.light,
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.8)",
  },
  headerSpacer: {
    width: 40,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 40,
  },
  profileCard: {
    backgroundColor: THEME.light,
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
    overflow: "hidden",
  },
  profileGradient: {
    padding: 32,
    alignItems: "center",
  },
  avatarContainer: {
    marginBottom: 20,
  },
  avatarGradient: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  userName: {
    fontSize: 28,
    fontWeight: "bold",
    color: THEME.dark,
    marginBottom: 8,
    textAlign: "center",
  },
  userEmail: {
    fontSize: 16,
    color: THEME.gray,
    marginBottom: 24,
    textAlign: "center",
  },
  editButton: {
    borderRadius: 25,
    overflow: "hidden",
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  editButtonGradient: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  editButtonText: {
    color: THEME.light,
    fontWeight: "bold",
    fontSize: 16,
    marginLeft: 8,
  },
  section: {
    backgroundColor: THEME.light,
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
    overflow: "hidden",
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: THEME.dark,
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.05)",
  },
  settingLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  settingIcon: {
    width: 36,
    height: 36,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  settingText: {
    fontSize: 16,
    color: THEME.dark,
    fontWeight: "500",
  },
  logoutSection: {
    backgroundColor: THEME.light,
    borderRadius: 20,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
    overflow: "hidden",
  },
  logoutButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  logoutIconContainer: {
    width: 36,
    height: 36,
    backgroundColor: "rgba(239, 68, 68, 0.1)",
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: "bold",
    color: THEME.danger,
  },
});
