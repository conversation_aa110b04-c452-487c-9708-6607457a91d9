import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  Image,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { PropertyMatch } from '../../store/aiStore';
import { EnhancedPropertyMatch } from '../../services/enhancedAIMatchingService';
import { listingsService } from '../../services/listingsService';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
};

const { width } = Dimensions.get('window');

interface PropertyMatchCardProps {
  match: EnhancedPropertyMatch;
  onViewDetails: () => void;
  onQuickApply: () => void;
  onSave: (isSaved: boolean) => void;
  onInteraction?: (type: 'like' | 'dislike' | 'skip') => void;
}

export const PropertyMatchCard: React.FC<PropertyMatchCardProps> = ({
  match,
  onViewDetails,
  onQuickApply,
  onSave,
  onInteraction,
}) => {
  // State
  const [imageLoading, setImageLoading] = useState(true);
  const [showFullSummary, setShowFullSummary] = useState(false);
  const [expandDetails, setExpandDetails] = useState(false);
  
  // Animation values
  const expandAnimation = useSharedValue(0);
  const pressAnimation = useSharedValue(1);
  
  // Handle expand toggle
  const toggleExpand = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setExpandDetails(!expandDetails);
    expandAnimation.value = withTiming(expandDetails ? 0 : 1, {
      duration: 300,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  };
  
  // Handle press in
  const handlePressIn = () => {
    pressAnimation.value = withTiming(0.98, {
      duration: 100,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  };
  
  // Handle press out
  const handlePressOut = () => {
    pressAnimation.value = withTiming(1, {
      duration: 200,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  };
  
  // Handle save toggle
  const handleSaveToggle = () => {
    onSave(match.saved);
  };
  
  // Format location
  const formatLocation = () => {
    if (typeof match.listing.location === 'string') {
      return match.listing.location;
    } else if (typeof match.listing.location === 'object') {
      return match.listing.location.city || match.listing.location.address || 'Unknown location';
    }
    return 'Unknown location';
  };
  
  // Get match score color
  const getScoreColor = (score: number) => {
    if (score >= 80) return '#10b981'; // Green
    if (score >= 60) return '#f59e0b'; // Yellow
    return '#ef4444'; // Red
  };
  
  // Animated styles
  const cardAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: pressAnimation.value },
      ],
    };
  });
  
  const expandAnimatedStyle = useAnimatedStyle(() => {
    return {
      height: interpolate(
        expandAnimation.value,
        [0, 1],
        [0, 200],
        Extrapolate.CLAMP
      ),
      opacity: expandAnimation.value,
    };
  });
  
  const rotateAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { 
          rotate: `${interpolate(
            expandAnimation.value,
            [0, 1],
            [0, 180],
            Extrapolate.CLAMP
          )}deg` 
        },
      ],
    };
  });
  
  // Get default image or placeholder
  const isValidImageUrl = (url: string) => {
    if (!url || typeof url !== 'string') return false;
    const lower = url.toLowerCase();
    if (lower.endsWith('.svg')) return false;
    if (lower.includes('funda-logo')) return false;
    if (lower.includes('/maps/map_')) return false;
    return lower.startsWith('http');
  };

  const getImage = () => {
    const imgs: string[] = Array.isArray(match.listing.images) ? match.listing.images : [];
    const firstValid = imgs.find(isValidImageUrl);
    if (firstValid) return { uri: firstValid };
    return require('../../assets/images/property-placeholder.png');
  };
  
  return (
    <Animated.View style={[styles.container, cardAnimatedStyle]}>
      <TouchableOpacity
        activeOpacity={0.9}
        onPress={onViewDetails}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
      >
        {/* Card Header with Image */}
        <View style={styles.imageContainer}>
          <Image
            source={getImage()}
            style={styles.image}
            onLoadStart={() => setImageLoading(true)}
            onLoadEnd={() => setImageLoading(false)}
          />
          
          {imageLoading && (
            <View style={styles.imagePlaceholder}>
              <ActivityIndicator color={THEME.light} />
            </View>
          )}
          
          {/* Match Score Badge */}
          <View style={styles.scoreBadge}>
            <Text style={styles.scoreText}>{Math.round(match.score)}%</Text>
            <Text style={styles.scoreLabel}>Match</Text>
          </View>
          
          {/* Save Button */}
          <TouchableOpacity 
            style={styles.saveButton}
            onPress={handleSaveToggle}
          >
            <Ionicons 
              name={match.saved ? "heart" : "heart-outline"} 
              size={24} 
              color={match.saved ? THEME.accent : THEME.light} 
            />
          </TouchableOpacity>
        </View>
        
        {/* Card Content */}
        <View style={styles.content}>
          <View style={styles.titleRow}>
            <Text style={styles.price}>
              {listingsService.formatPrice(match.listing.price)}
            </Text>
            <View style={[styles.matchIndicator, { backgroundColor: getScoreColor(match.score) }]} />
          </View>
          
          <Text style={styles.title} numberOfLines={1}>
            {match.listing.title}
          </Text>
          
          <View style={styles.detailsRow}>
            <View style={styles.detailItem}>
              <Ionicons name="location" size={16} color={THEME.gray} />
              <Text style={styles.detailText} numberOfLines={1}>
                {formatLocation()}
              </Text>
            </View>
            
            {match.listing.rooms && (
              <View style={styles.detailItem}>
                <Ionicons name="bed-outline" size={16} color={THEME.gray} />
                <Text style={styles.detailText}>
                  {match.listing.rooms} {match.listing.rooms === 1 ? 'Room' : 'Rooms'}
                </Text>
              </View>
            )}
            
            {match.listing.area && (
              <View style={styles.detailItem}>
                <Ionicons name="square-outline" size={16} color={THEME.gray} />
                <Text style={styles.detailText}>
                  {match.listing.area} m²
                </Text>
              </View>
            )}
          </View>
          
          {/* AI Insights Preview */}
          <View style={styles.insightsPreview}>
            {match.pros.slice(0, 1).map((pro, index) => (
              <View key={`pro-${index}`} style={styles.insightItem}>
                <Ionicons name="checkmark-circle" size={16} color="#10b981" />
                <Text style={styles.insightText} numberOfLines={1}>{pro}</Text>
              </View>
            ))}
          </View>
          
          {/* Expand Button */}
          <TouchableOpacity 
            style={styles.expandButton}
            onPress={toggleExpand}
          >
            <Text style={styles.expandText}>
              {expandDetails ? 'Hide details' : 'Show AI insights'}
            </Text>
            <Animated.View style={rotateAnimatedStyle}>
              <Ionicons name="chevron-down" size={16} color={THEME.primary} />
            </Animated.View>
          </TouchableOpacity>
          
          {/* Expandable Content */}
          <Animated.View style={[styles.expandableContent, expandAnimatedStyle]}>
            {/* Pros */}
            <View style={styles.insightsSection}>
              <Text style={styles.insightsSectionTitle}>Highlights</Text>
              {match.pros.map((pro, index) => (
                <View key={`pro-${index}`} style={styles.insightItem}>
                  <Ionicons name="checkmark-circle" size={16} color="#10b981" />
                  <Text style={styles.insightText}>{pro}</Text>
                </View>
              ))}
            </View>
            
            {/* Cons */}
            <View style={styles.insightsSection}>
              <Text style={styles.insightsSectionTitle}>Considerations</Text>
              {match.cons.map((con, index) => (
                <View key={`con-${index}`} style={styles.insightItem}>
                  <Ionicons name="alert-circle" size={16} color="#f59e0b" />
                  <Text style={styles.insightText}>{con}</Text>
                </View>
              ))}
            </View>
          </Animated.View>
        </View>
      </TouchableOpacity>
      
      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={onViewDetails}
        >
          <Ionicons name="eye-outline" size={20} color={THEME.dark} />
          <Text style={styles.actionButtonText}>View Details</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.actionButton, styles.applyButton]}
          onPress={onQuickApply}
        >
          <Ionicons name="paper-plane-outline" size={20} color={THEME.light} />
          <Text style={styles.applyButtonText}>Quick Apply</Text>
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
    overflow: 'hidden',
  },
  imageContainer: {
    height: 180,
    width: '100%',
    position: 'relative',
  },
  image: {
    height: '100%',
    width: '100%',
    resizeMode: 'cover',
  },
  imagePlaceholder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scoreBadge: {
    position: 'absolute',
    top: 16,
    left: 16,
    backgroundColor: 'rgba(10, 10, 24, 0.8)',
    borderRadius: 12,
    paddingVertical: 6,
    paddingHorizontal: 12,
    alignItems: 'center',
  },
  scoreText: {
    color: THEME.light,
    fontSize: 16,
    fontWeight: '700',
  },
  scoreLabel: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
  },
  saveButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    backgroundColor: 'rgba(10, 10, 24, 0.5)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    padding: 16,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  price: {
    fontSize: 20,
    fontWeight: '700',
    color: THEME.dark,
  },
  matchIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 8,
  },
  detailsRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  detailText: {
    fontSize: 14,
    color: THEME.gray,
    marginLeft: 4,
  },
  insightsPreview: {
    marginBottom: 8,
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  insightText: {
    fontSize: 14,
    color: THEME.dark,
    marginLeft: 8,
    flex: 1,
  },
  expandButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  expandText: {
    fontSize: 14,
    color: THEME.primary,
    fontWeight: '500',
    marginRight: 4,
  },
  expandableContent: {
    overflow: 'hidden',
  },
  insightsSection: {
    marginBottom: 12,
  },
  insightsSectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.dark,
    marginLeft: 8,
  },
  applyButton: {
    backgroundColor: THEME.accent,
    borderBottomRightRadius: 16,
  },
  applyButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.light,
    marginLeft: 8,
  },
});