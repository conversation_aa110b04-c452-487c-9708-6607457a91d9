/**
 * Transformation Optimization Configuration
 * 
 * This module provides optimized configuration settings for the transformation pipeline
 * to reduce memory usage and improve performance.
 */

/**
 * Memory-optimized cache configuration
 */
const CACHE_CONFIG = {
  // Reduced cache size and TTL to minimize memory usage
  stdTTL: 300, // 5 minutes (reduced from 10)
  checkperiod: 30, // 30 seconds (reduced from 60)
  maxKeys: 500, // Reduced from 1000
  maxMemoryMB: 50 // Maximum 50MB for cache
};

/**
 * Concurrent processing configuration
 */
const CONCURRENT_CONFIG = {
  // Limit worker threads to reduce memory overhead
  maxWorkers: Math.max(1, require('os').cpus().length - 2), // Leave 2 cores free
  minBatchSize: 15 // Increased from 10 to reduce worker creation
};

/**
 * Memory monitoring configuration
 */
const MEMORY_CONFIG = {
  // Alert thresholds
  alertThreshold: 150 * 1024 * 1024, // 150MB
  warningThreshold: 120 * 1024 * 1024, // 120MB
  
  // Garbage collection hints
  gcInterval: 100, // Run GC hint every 100 transformations
  forceGcThreshold: 200 * 1024 * 1024 // Force GC at 200MB
};

/**
 * Performance optimization settings
 */
const PERFORMANCE_CONFIG = {
  // Disable deep cloning for cached results
  useShallowCopy: true,
  
  // Worker cleanup settings
  workerTimeout: 30000, // 30 seconds
  workerCleanupInterval: 60000, // 1 minute
  
  // Batch processing optimizations
  optimalBatchSize: 25,
  maxBatchSize: 100
};

/**
 * Get optimized configuration for transformation pipeline
 * @returns {Object} Optimized configuration
 */
function getOptimizedConfig() {
  return {
    cache: CACHE_CONFIG,
    concurrent: CONCURRENT_CONFIG,
    memory: MEMORY_CONFIG,
    performance: PERFORMANCE_CONFIG
  };
}

/**
 * Apply memory optimizations to the transformation pipeline
 * @param {OptimizedSchemaTransformer} transformer - Transformer instance
 */
function applyMemoryOptimizations(transformer) {
  // Set up periodic garbage collection hints
  let transformationCount = 0;
  
  const originalTransform = transformer.transform.bind(transformer);
  transformer.transform = async function(rawData, source, options = {}) {
    const result = await originalTransform(rawData, source, options);
    
    transformationCount++;
    
    // Suggest garbage collection periodically
    if (transformationCount % MEMORY_CONFIG.gcInterval === 0) {
      if (global.gc) {
        global.gc();
      }
    }
    
    // Force garbage collection if memory usage is too high
    const memoryUsage = process.memoryUsage().heapUsed;
    if (memoryUsage > MEMORY_CONFIG.forceGcThreshold && global.gc) {
      global.gc();
    }
    
    return result;
  };
  
  return transformer;
}

/**
 * Monitor memory usage and provide recommendations
 * @returns {Object} Memory status and recommendations
 */
function getMemoryStatus() {
  const memoryUsage = process.memoryUsage();
  const heapUsed = memoryUsage.heapUsed;
  const heapTotal = memoryUsage.heapTotal;
  const external = memoryUsage.external;
  
  const status = {
    heapUsed: Math.round(heapUsed / 1024 / 1024),
    heapTotal: Math.round(heapTotal / 1024 / 1024),
    external: Math.round(external / 1024 / 1024),
    heapUtilization: Math.round((heapUsed / heapTotal) * 100),
    timestamp: new Date().toISOString()
  };
  
  const recommendations = [];
  
  if (heapUsed > MEMORY_CONFIG.warningThreshold) {
    recommendations.push({
      level: 'warning',
      message: 'Memory usage is approaching threshold',
      action: 'Consider clearing caches or reducing batch sizes'
    });
  }
  
  if (heapUsed > MEMORY_CONFIG.alertThreshold) {
    recommendations.push({
      level: 'critical',
      message: 'Memory usage exceeds alert threshold',
      action: 'Immediate cache clearing and garbage collection recommended'
    });
  }
  
  if (status.heapUtilization > 80) {
    recommendations.push({
      level: 'info',
      message: 'High heap utilization detected',
      action: 'Monitor for potential memory leaks'
    });
  }
  
  return {
    status,
    recommendations
  };
}

module.exports = {
  CACHE_CONFIG,
  CONCURRENT_CONFIG,
  MEMORY_CONFIG,
  PERFORMANCE_CONFIG,
  getOptimizedConfig,
  applyMemoryOptimizations,
  getMemoryStatus
};
