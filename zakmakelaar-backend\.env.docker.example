# Docker Environment Configuration
# Copy this file to .env.docker and update the values as needed

# Server Configuration
PORT=3000
NODE_ENV=production

# Database Configuration (Docker MongoDB)
MONGO_URI=**********************************************************************

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-here-make-it-long-and-random-change-this-in-production
JWT_EXPIRES_IN=7d

# SendGrid Configuration (for email notifications)
SENDGRID_API_KEY=your-sendgrid-api-key-here
SENDGRID_FROM_EMAIL=<EMAIL>

# Twilio Configuration (for WhatsApp notifications)
TWILIO_ACCOUNT_SID=your-twilio-account-sid-here
TWILIO_AUTH_TOKEN=your-twilio-auth-token-here
TWILIO_WHATSAPP_FROM=whatsapp:+***********

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Scraping Configuration
SCRAPING_INTERVAL_MINUTES=5
SCRAPING_TIMEOUT_MS=60000
# Enable/disable automatic scraper agent startup (true/false)
SCRAPER_AGENT_AUTO_START=true

# CORS Configuration
CORS_ORIGIN=http://localhost:3001

# Redis Configuration (Docker Redis)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# Cache Configuration
CACHE_DEFAULT_TTL=3600
CACHE_LISTINGS_TTL=300
CACHE_USER_TTL=1800

# Debug Logging Configuration
# Enable debug logs for auto-application service
AUTO_APPLICATION_DEBUG=true
# Enable console logs for auto-application service
AUTO_APPLICATION_CONSOLE_LOGS=true

# Chrome/Chromium Configuration for Docker
CHROME_EXECUTABLE_PATH=/usr/bin/chromium-browser
PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Demo Configuration Variables
# Enable demo mode for more verbose logging and visible browser automation
DEMO_MODE=true
DEMO_BROWSER_HEADLESS=false
DEMO_SHOW_BROWSER=true
DEMO_SLOW_MOTION=100
DEMO_SCREENSHOT_ENABLED=true
FUNDA_AUTO_APPLICATION_HEADLESS=false
FUNDA_DEMO_EMAIL=<EMAIL>
FUNDA_DEMO_FIRST_NAME=YourFirstName
FUNDA_DEMO_LAST_NAME=YourLastName
