const { FundaScraper } = require("./FundaScraperV2");
const { ParariusScraper } = require("./ParariusScraperV2");
const { HuurwoningenScraper } = require("./HuurwoningenScraperV2");
const {
  getScraperConfig,
  getAvailableScrapers,
} = require("./configs/scraperConfigs");
const { errorMetrics } = require("./errors/ScrapingErrors");
const { rateLimiter } = require("./utils/RateLimiter");
const { performanceMonitor } = require("./monitoring/PerformanceMonitor");
const { cleanup } = require("../scraperUtils");

/**
 * Enhanced scraper service with improved architecture
 */
class ScraperService {
  constructor() {
    this.scrapers = new Map();
    this.activeSessions = new Map();
    this.config = {
      maxConcurrentScrapers: 2,
      healthCheckInterval: 300000, // 5 minutes
      cleanupInterval: 3600000, // 1 hour
    };

    this.initializeScrapers();
    this.startHealthCheck();
    this.startCleanup();
  }

  /**
   * Initialize all available scrapers
   */
  initializeScrapers() {
    this.scrapers.set("funda", () => new FundaScraper());
    this.scrapers.set("pararius", () => new ParariusScraper());
    this.scrapers.set("huurwoningen", () => new HuurwoningenScraper());

    loggers.scraper.info("Scrapers initialized", {
      count: this.scrapers.size,
      scrapers: Array.from(this.scrapers.keys()),
    });
  }

  /**
   * Start scraping for a specific site
   */
  async scrapeSite(siteName, options = {}) {
    if (!this.scrapers.has(siteName)) {
      throw new Error(`Unknown scraper: ${siteName}`);
    }

    // Check if scraper is already running
    if (this.activeSessions.has(siteName)) {
      throw new Error(`Scraper ${siteName} is already running`);
    }

    const sessionId = performanceMonitor.startSession(siteName);
    this.activeSessions.set(siteName, sessionId);

    try {
      loggers.scraper.info("Starting scraper", { siteName });

      const scraperFactory = this.scrapers.get(siteName);
      const scraper = scraperFactory();

      // Apply any custom options
      if (options.maxPages) {
        scraper.config.maxPages = options.maxPages;
      }
      if (options.timeout) {
        scraper.config.timeout = options.timeout;
      }

      const result = await scraper.scrape();

      // Record successful completion
      performanceMonitor.endSession(sessionId, "completed");

      loggers.scraper.info("Scraper completed successfully", { siteName });
      return {
        success: true,
        siteName,
        sessionId,
        stats: result.stats,
        metrics: performanceMonitor.getSessionMetrics(sessionId),
      };
    } catch (error) {
      // Record failed completion
      performanceMonitor.recordError(sessionId, error);
      performanceMonitor.endSession(sessionId, "failed");

      loggers.scraper.error("Scraper failed", {
        siteName,
        error: error.message,
      });
      throw error;
    } finally {
      this.activeSessions.delete(siteName);
    }
  }

  /**
   * Start scraping for all sites
   */
  async scrapeAll(options = {}) {
    const availableScrapers = getAvailableScrapers();
    const results = [];
    const errors = [];

    loggers.scraper.info("Starting all site scraping", {
      sites: availableScrapers,
    });

    // Scrape sites with controlled concurrency
    const concurrency = Math.min(
      this.config.maxConcurrentScrapers,
      availableScrapers.length
    );

    for (let i = 0; i < availableScrapers.length; i += concurrency) {
      const batch = availableScrapers.slice(i, i + concurrency);

      const batchPromises = batch.map(async (siteName) => {
        try {
          const result = await this.scrapeSite(siteName, options);
          results.push(result);
          return result;
        } catch (error) {
          const errorResult = {
            success: false,
            siteName,
            error: error.message,
            type: error.type || "UNKNOWN",
          };
          errors.push(errorResult);
          return errorResult;
        }
      });

      await Promise.all(batchPromises);

      // Delay between batches to avoid overwhelming the system
      if (i + concurrency < availableScrapers.length) {
        await new Promise((resolve) => setTimeout(resolve, 10000)); // 10 second delay
      }
    }

    const summary = {
      totalScrapers: availableScrapers.length,
      successful: results.filter((r) => r.success).length,
      failed: errors.length,
      results,
      errors,
      performance: performanceMonitor.getPerformanceSummary(),
    };

    loggers.scraper.info("Scraping summary", {
      successful: summary.successful,
      total: summary.totalScrapers,
    });
    return summary;
  }

  /**
   * Get scraper status
   */
  getScraperStatus(siteName = null) {
    if (siteName) {
      return {
        siteName,
        isRunning: this.activeSessions.has(siteName),
        sessionId: this.activeSessions.get(siteName),
        config: getScraperConfig(siteName),
        rateLimitStats: rateLimiter.getStats(siteName),
        errorStats: errorMetrics.getErrorStats(siteName),
        recentMetrics: performanceMonitor.getSiteMetrics(siteName, 5),
      };
    }

    // Return status for all scrapers
    const allScrapers = getAvailableScrapers();
    const status = {};

    allScrapers.forEach((name) => {
      status[name] = this.getScraperStatus(name);
    });

    return status;
  }

  /**
   * Stop a running scraper
   */
  async stopScraper(siteName) {
    if (!this.activeSessions.has(siteName)) {
      return { message: `Scraper ${siteName} is not running` };
    }

    const sessionId = this.activeSessions.get(siteName);

    // Mark session as stopped
    performanceMonitor.endSession(sessionId, "stopped");
    this.activeSessions.delete(siteName);

    loggers.scraper.info("Scraper stopped", { siteName });
    return { message: `Scraper ${siteName} stopped successfully` };
  }

  /**
   * Stop all running scrapers
   */
  async stopAllScrapers() {
    const runningSites = Array.from(this.activeSessions.keys());

    for (const siteName of runningSites) {
      await this.stopScraper(siteName);
    }

    return { message: `Stopped ${runningSites.length} scrapers` };
  }

  /**
   * Get comprehensive system health
   */
  getSystemHealth() {
    const performance = performanceMonitor.getPerformanceSummary();
    const rateLimitStats = rateLimiter.getStats();
    const errorStats = errorMetrics.getErrorStats();
    const alerts = performanceMonitor.getAlerts(10);

    return {
      timestamp: Date.now(),
      activeScrapers: this.activeSessions.size,
      activeSessions: Array.from(this.activeSessions.entries()).map(
        ([site, sessionId]) => ({
          site,
          sessionId,
          metrics: performanceMonitor.getSessionMetrics(sessionId),
        })
      ),
      performance,
      rateLimiting: rateLimitStats,
      errors: errorStats,
      alerts,
      systemStatus: this.determineSystemStatus(performance, alerts),
    };
  }

  /**
   * Determine overall system status
   */
  determineSystemStatus(performance, alerts) {
    const recentAlerts = alerts.filter(
      (alert) => Date.now() - alert.timestamp < 3600000 // Last hour
    );

    const criticalAlerts = recentAlerts.filter((alert) =>
      ["HIGH_ERROR_RATE", "LOW_SUCCESS_RATE"].includes(alert.type)
    );

    if (criticalAlerts.length > 0) {
      return "CRITICAL";
    }

    if (recentAlerts.length > 5) {
      return "WARNING";
    }

    if (performance.avgSuccessRate < 0.8) {
      return "DEGRADED";
    }

    return "HEALTHY";
  }

  /**
   * Update scraper configuration
   */
  updateScraperConfig(siteName, newConfig) {
    const currentConfig = getScraperConfig(siteName);
    const updatedConfig = { ...currentConfig, ...newConfig };

    // Validate the updated configuration
    const required = ["baseUrl", "searchPath", "selectors"];
    const missing = required.filter((key) => !updatedConfig[key]);

    if (missing.length > 0) {
      throw new Error(`Missing required config keys: ${missing.join(", ")}`);
    }

    // Update the configuration (in a real system, this would persist to database/file)
    loggers.scraper.info("Configuration updated", { siteName });
    return updatedConfig;
  }

  /**
   * Reset rate limiting for a site
   */
  resetRateLimit(siteName) {
    rateLimiter.resetBackoff(siteName);
    return { message: `Rate limit reset for ${siteName}` };
  }

  /**
   * Get performance metrics export
   */
  exportMetrics(format = "json") {
    return performanceMonitor.exportMetrics(format);
  }

  /**
   * Start periodic health checks
   */
  startHealthCheck() {
    setInterval(() => {
      const health = this.getSystemHealth();

      if (health.systemStatus === "CRITICAL") {
        loggers.scraper.error("System health critical");
        // In production, this would trigger alerts/notifications
      } else if (health.systemStatus === "WARNING") {
        loggers.scraper.warn("System health degraded");
      }

      // Log health summary
      loggers.scraper.info("Health check completed", {
        status: health.systemStatus,
        activeScrapers: health.activeScrapers,
        successRate: (health.performance.avgSuccessRate * 100).toFixed(1) + "%",
      });
    }, this.config.healthCheckInterval);
  }

  /**
   * Start periodic cleanup
   */
  startCleanup() {
    setInterval(async () => {
      try {
        // Clean up old monitoring data
        performanceMonitor.cleanup();

        // Clean up browser resources
        await cleanup();

        loggers.scraper.debug("Periodic cleanup completed");
      } catch (error) {
        loggers.scraper.error("Error during cleanup", { error: error.message });
      }
    }, this.config.cleanupInterval);
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    loggers.scraper.info("Shutting down scraper service");

    // Stop all running scrapers
    await this.stopAllScrapers();

    // Clean up resources
    await cleanup();

    loggers.scraper.info("Scraper service shutdown complete");
  }
}

// Create singleton instance
const scraperService = new ScraperService();

// Graceful shutdown handling
process.on("SIGINT", async () => {
  await scraperService.shutdown();
  process.exit(0);
});

process.on("SIGTERM", async () => {
  await scraperService.shutdown();
  process.exit(0);
});

module.exports = {
  ScraperService,
  scraperService,
  // Export individual scraper functions for backward compatibility
  scrapeFunda: () => scraperService.scrapeSite("funda"),
  scrapePararius: () => scraperService.scrapeSite("pararius"),
  scrapeHuurwoningen: () => scraperService.scrapeSite("huurwoningen"),
};
