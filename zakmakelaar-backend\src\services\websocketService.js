const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const config = require('../config/config');
const securityConfig = require('../config/security');
const { loggers } = require('./logger');

class WebSocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> socket mapping
    this.rateLimitState = new Map(); // socketId -> { windowStart, count }
    this.heartbeatTimers = new Map(); // socketId -> interval id

    this.rateLimitWindowMs =
      parseInt(process.env.WS_RATE_LIMIT_WINDOW_MS, 10) || 10_000;
    this.rateLimitMaxEvents =
      parseInt(process.env.WS_RATE_LIMIT_MAX_EVENTS, 10) || 50;
    this.heartbeatIntervalMs =
      parseInt(process.env.WS_HEARTBEAT_INTERVAL_MS, 10) || 30_000;
    this.heartbeatTimeoutMs =
      parseInt(process.env.WS_HEARTBEAT_TIMEOUT_MS, 10) || 90_000;
  }

  /**
   * Initialize WebSocket server
   * @param {http.Server} server - HTTP server instance
   */
  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: securityConfig.getAllowedOrigins(),
        credentials: true,
      },
      transports: ['websocket', 'polling'],
    });

    // Authentication middleware
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = jwt.verify(token, config.jwtSecret);
        const user = await this.getUserById(decoded.id);
        
        if (!user) {
          return next(new Error('User not found'));
        }

        socket.userId = user._id.toString();
        socket.user = user;
        next();
      } catch (error) {
        loggers.app.error('WebSocket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });

    // Connection handling
    this.io.on('connection', (socket) => {
      this.handleConnection(socket);
    });

    loggers.app.info('WebSocket service initialized');
  }

  /**
   * Handle new socket connection
   * @param {Socket} socket - Socket.io socket instance
   */
  handleConnection(socket) {
    const userId = socket.userId;
    const socketId = socket.id;
    
    // Store user connection
    this.connectedUsers.set(userId, socket);
    
    loggers.app.info(`User ${userId} connected via WebSocket`);

    // Prime heartbeat tracking
    socket.wsLastHeartbeat = Date.now();
    this.startHeartbeat(socket);

    // Apply simple rate limiting before handling subscription events
    socket.use((packet, next) => {
      const [eventName] = packet;
      if (!eventName || typeof eventName !== 'string') {
        return next();
      }

      if (!this.consumeRateLimit(socketId)) {
        loggers.app.warn(`WebSocket rate limit exceeded for user ${userId}`, {
          socketId,
          event: eventName,
        });
        socket.emit('rate-limit', {
          message: 'Too many requests. Please slow down.',
          retryAfterMs: this.rateLimitWindowMs,
        });
        return;
      }

      next();
    });

    // Join user-specific room
    socket.join(`user:${userId}`);

    // Register subscription handlers
    this.registerChannel(socket, {
      channel: 'auto-application',
      room: `auto-application:${userId}`,
      permission: () => true,
    });

    this.registerChannel(socket, {
      channel: 'queue-status',
      room: `queue:${userId}`,
      permission: () => true,
    });

    this.registerChannel(socket, {
      channel: 'application-monitor',
      room: `monitor:${userId}`,
      permission: (user) => user?.role === 'admin',
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      this.connectedUsers.delete(userId);
      this.rateLimitState.delete(socketId);
      this.stopHeartbeat(socket);
      loggers.app.info(`User ${userId} disconnected: ${reason}`);
    });

    // Send initial connection confirmation
    socket.emit('connected', {
      message: 'WebSocket connection established',
      userId: userId,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Send auto-application status update to user
   * @param {string} userId - User ID
   * @param {Object} statusUpdate - Status update data
   */
  sendAutoApplicationUpdate(userId, statusUpdate) {
    if (!this.io) return;

    const data = {
      type: 'auto-application-status',
      timestamp: new Date().toISOString(),
      ...statusUpdate
    };

    this.io.to(`auto-application:${userId}`).emit('auto-application:status-update', data);
    loggers.app.debug(`Sent auto-application update to user ${userId}:`, data);
  }

  /**
   * Send queue status update to user
   * @param {string} userId - User ID
   * @param {Object} queueUpdate - Queue update data
   */
  sendQueueUpdate(userId, queueUpdate) {
    if (!this.io) return;

    const data = {
      type: 'queue-status',
      timestamp: new Date().toISOString(),
      ...queueUpdate
    };

    this.io.to(`queue:${userId}`).emit('queue:status-update', data);
    loggers.app.debug(`Sent queue update to user ${userId}:`, data);
  }

  /**
   * Send application result update to user
   * @param {string} userId - User ID
   * @param {Object} applicationResult - Application result data
   */
  sendApplicationResult(userId, applicationResult) {
    if (!this.io) return;

    const data = {
      type: 'application-result',
      timestamp: new Date().toISOString(),
      ...applicationResult
    };

    this.io.to(`user:${userId}`).emit('application:result', data);
    this.io.to(`monitor:${userId}`).emit('monitor:application-result', data);
    loggers.app.debug(`Sent application result to user ${userId}:`, data);
  }

  /**
   * Send application progress update to user
   * @param {string} userId - User ID
   * @param {Object} progressUpdate - Progress update data
   */
  sendApplicationProgress(userId, progressUpdate) {
    if (!this.io) return;

    const data = {
      type: 'application-progress',
      timestamp: new Date().toISOString(),
      ...progressUpdate
    };

    this.io.to(`user:${userId}`).emit('application:progress', data);
    loggers.app.debug(`Sent application progress to user ${userId}:`, data);
  }

  /**
   * Send system alert to user
   * @param {string} userId - User ID
   * @param {Object} alert - Alert data
   */
  sendSystemAlert(userId, alert) {
    if (!this.io) return;

    const data = {
      type: 'system-alert',
      timestamp: new Date().toISOString(),
      ...alert
    };

    this.io.to(`user:${userId}`).emit('system:alert', data);
    loggers.app.debug(`Sent system alert to user ${userId}:`, data);
  }

  /**
   * Send document upload confirmation to user
   * @param {string} userId - User ID
   * @param {Object} documentUpdate - Document update data
   */
  sendDocumentUpdate(userId, documentUpdate) {
    if (!this.io) return;

    const data = {
      type: 'document-update',
      timestamp: new Date().toISOString(),
      ...documentUpdate
    };

    this.io.to(`user:${userId}`).emit('document:update', data);
    loggers.app.debug(`Sent document update to user ${userId}:`, data);
  }

  /**
   * Send monitoring data update to user
   * @param {string} userId - User ID
   * @param {Object} monitoringData - Monitoring data
   */
  sendMonitoringUpdate(userId, monitoringData) {
    if (!this.io) return;

    const data = {
      type: 'monitoring-update',
      timestamp: new Date().toISOString(),
      ...monitoringData
    };

    this.io.to(`monitor:${userId}`).emit('monitor:update', data);
    loggers.app.debug(`Sent monitoring update to user ${userId}:`, data);
  }

  /**
   * Broadcast system-wide notification
   * @param {Object} notification - Notification data
   */
  broadcastSystemNotification(notification) {
    if (!this.io) return;

    const data = {
      type: 'system-notification',
      timestamp: new Date().toISOString(),
      ...notification
    };

    this.io.emit('system:notification', data);
    loggers.app.info('Broadcasted system notification:', data);
  }

  /**
   * Get connected user count
   * @returns {number} Number of connected users
   */
  getConnectedUserCount() {
    return this.connectedUsers.size;
  }

  /**
   * Check if user is connected
   * @param {string} userId - User ID
   * @returns {boolean} True if user is connected
   */
  isUserConnected(userId) {
    return this.connectedUsers.has(userId);
  }

  /**
   * Get user by ID (placeholder - should be implemented based on your user service)
   * @param {string} userId - User ID
   * @returns {Object|null} User object or null
   */
  async getUserById(userId) {
    try {
      // This should be replaced with actual user service call
      const User = require('../models/User');
      return await User.findById(userId);
    } catch (error) {
      loggers.app.error('Error fetching user:', error);
      return null;
    }
  }

  /**
   * Close WebSocket server
   */
  close() {
    if (this.io) {
      this.io.close();
      this.connectedUsers.clear();
      this.rateLimitState.clear();
      this.heartbeatTimers.forEach((timer) => clearInterval(timer));
      this.heartbeatTimers.clear();
      loggers.app.info('WebSocket service closed');
    }
  }

  /**
   * Consume rate limit tokens for a socket.
   * Returns true if the event is allowed to proceed.
   */
  consumeRateLimit(socketId) {
    const now = Date.now();
    const entry =
      this.rateLimitState.get(socketId) || {
        windowStart: now,
        count: 0,
      };

    if (now - entry.windowStart > this.rateLimitWindowMs) {
      entry.windowStart = now;
      entry.count = 0;
    }

    if (entry.count >= this.rateLimitMaxEvents) {
      this.rateLimitState.set(socketId, entry);
      return false;
    }

    entry.count += 1;
    this.rateLimitState.set(socketId, entry);
    return true;
  }

  registerChannel(socket, { channel, room, permission }) {
    const userId = socket.userId;
    const eventSubscribe = `subscribe:${channel}`;
    const eventUnsubscribe = `unsubscribe:${channel}`;

    const hasPermission = () => {
      try {
        return typeof permission === 'function'
          ? permission(socket.user)
          : true;
      } catch (error) {
        loggers.app.error(
          `Error evaluating permission for channel ${channel}`,
          error
        );
        return false;
      }
    };

    socket.on(eventSubscribe, () => {
      if (!hasPermission()) {
        loggers.app.warn(
          `User ${userId} denied subscription to channel ${channel}`
        );
        socket.emit('subscription:denied', {
          channel,
          reason: 'Insufficient permissions',
        });
        return;
      }

      socket.join(room);
      loggers.app.debug(
        `User ${userId} subscribed to ${channel} updates`
      );
      socket.emit('subscription:confirmed', {
        channel,
        timestamp: new Date().toISOString(),
      });
    });

    socket.on(eventUnsubscribe, () => {
      socket.leave(room);
      loggers.app.debug(
        `User ${userId} unsubscribed from ${channel} updates`
      );
      socket.emit('subscription:removed', {
        channel,
        timestamp: new Date().toISOString(),
      });
    });
  }

  startHeartbeat(socket) {
    if (this.heartbeatIntervalMs <= 0) {
      return;
    }

    const socketId = socket.id;

    if (this.heartbeatTimers.has(socketId)) {
      clearInterval(this.heartbeatTimers.get(socketId));
    }

    const packetListener = (packet) => {
      if (packet.type === 'pong') {
        socket.wsLastHeartbeat = Date.now();
      }
    };
    socket.wsPacketListener = packetListener;
    socket.conn.on('packet', packetListener);

    const intervalId = setInterval(() => {
      const elapsed = Date.now() - (socket.wsLastHeartbeat || 0);
      if (elapsed > this.heartbeatTimeoutMs) {
        loggers.app.warn(
          `Heartbeat timeout for user ${socket.userId}, disconnecting`
        );
        socket.disconnect(true);
        this.stopHeartbeat(socket);
        return;
      }

      socket.emit('ws:heartbeat', {
        timestamp: Date.now(),
        timeoutMs: this.heartbeatTimeoutMs,
      });
    }, this.heartbeatIntervalMs);

    this.heartbeatTimers.set(socketId, intervalId);
  }

  stopHeartbeat(socket) {
    const socketId = socket.id;
    const timer = this.heartbeatTimers.get(socketId);
    if (timer) {
      clearInterval(timer);
      this.heartbeatTimers.delete(socketId);
    }
    if (socket.wsPacketListener) {
      const remove = socket.conn.off
        ? socket.conn.off.bind(socket.conn)
        : socket.conn.removeListener.bind(socket.conn);
      remove('packet', socket.wsPacketListener);
      delete socket.wsPacketListener;
    }
  }
}

// Export singleton instance
module.exports = new WebSocketService();
