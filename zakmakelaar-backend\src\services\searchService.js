const Listing = require("../models/Listing");
const { logHelpers } = require("./logger");
const cacheService = require("./cacheService");
const { dbErrorHandler } = require("../utils/errorHandler");

class SearchService {
  constructor() {
    this.defaultLimit = 20;
    this.maxLimit = 100;
  }

  // Build MongoDB aggregation pipeline for advanced search
  buildSearchPipeline(searchParams) {
    const pipeline = [];
    const matchStage = {};

    // Text search (using regex for title, description, location)
    if (searchParams.q || searchParams.search) {
      const searchTerm = searchParams.q || searchParams.search;
      matchStage.$or = [
        { title: { $regex: searchTerm, $options: "i" } },
        { description: { $regex: searchTerm, $options: "i" } },
        { location: { $regex: searchTerm, $options: "i" } },
        { propertyType: { $regex: searchTerm, $options: "i" } },
      ];
    }

    // Location search (case-insensitive partial match)
    if (searchParams.location) {
      matchStage.location = {
        $regex: searchParams.location,
        $options: "i",
      };
    }

    // Support multiple cities (comma-separated string or array)
    if (searchParams.cities && !searchParams.location) {
      const cities = Array.isArray(searchParams.cities)
        ? searchParams.cities
        : String(searchParams.cities)
            .split(",")
            .map((c) => c.trim())
            .filter(Boolean);

      if (cities.length > 0) {
        // Use $or with regex for partial, case-insensitive matches
        matchStage.$or = (matchStage.$or || []).concat(
          cities.map((city) => ({
            location: { $regex: city, $options: "i" },
          }))
        );
      }
    }

    // Price range
    if (searchParams.minPrice || searchParams.maxPrice) {
      matchStage.priceNumeric = {};
      if (searchParams.minPrice) {
        matchStage.priceNumeric.$gte = parseFloat(searchParams.minPrice);
      }
      if (searchParams.maxPrice) {
        matchStage.priceNumeric.$lte = parseFloat(searchParams.maxPrice);
      }
    }

    // Number of rooms
    if (searchParams.minRooms || searchParams.maxRooms) {
      matchStage.roomsNumeric = {};
      if (searchParams.minRooms) {
        matchStage.roomsNumeric.$gte = parseFloat(searchParams.minRooms);
      }
      if (searchParams.maxRooms) {
        matchStage.roomsNumeric.$lte = parseFloat(searchParams.maxRooms);
      }
    }

    // Size range
    if (searchParams.minSize || searchParams.maxSize) {
      matchStage.sizeNumeric = {};
      if (searchParams.minSize) {
        matchStage.sizeNumeric.$gte = parseFloat(searchParams.minSize);
      }
      if (searchParams.maxSize) {
        matchStage.sizeNumeric.$lte = parseFloat(searchParams.maxSize);
      }
    }
    // Property type
    if (searchParams.propertyType) {
      matchStage.propertyType = {
        $regex: searchParams.propertyType,
        $options: "i",
      };
    }

    // Support multiple property types (comma-separated string or array)
    if (searchParams.propertyTypes && !searchParams.propertyType) {
      const propertyTypes = Array.isArray(searchParams.propertyTypes)
        ? searchParams.propertyTypes
        : String(searchParams.propertyTypes)
            .split(",")
            .map((p) => p.trim())
            .filter(Boolean);

      if (propertyTypes.length > 0) {
        // Use $or with regex to be tolerant of capitalization/variations
        matchStage.$or = (matchStage.$or || []).concat(
          propertyTypes.map((ptype) => ({
            propertyType: { $regex: ptype, $options: "i" },
          }))
        );
      }
    }

    // Date range
    if (searchParams.dateFrom || searchParams.dateTo) {
      matchStage.dateAdded = {};
      if (searchParams.dateFrom) {
        matchStage.dateAdded.$gte = new Date(searchParams.dateFrom);
      }
      if (searchParams.dateTo) {
        matchStage.dateTo.$lte = new Date(searchParams.dateTo);
      }
    }

    // Add match stage if we have conditions
    if (Object.keys(matchStage).length > 0) {
      pipeline.push({ $match: matchStage });
    }

    // Add text score for text search
    if (searchParams.q) {
      pipeline.push({
        $addFields: {
          score: { $meta: "textScore" },
        },
      });
    }

    // Sorting
    const sortStage = {};
    if (searchParams.q) {
      // Sort by text score first for text search
      sortStage.score = { $meta: "textScore" };
    }

    const sortFieldMap = {
      price: "priceNumeric",
      rooms: "roomsNumeric",
      size: "sizeNumeric",
      timestamp: "timestamp",
      dateAdded: "dateAdded",
    };

    const sortBy = searchParams.sortBy || "dateAdded";
    const resolvedSortField = sortFieldMap[sortBy] || sortBy;
    const sortOrder = searchParams.sortOrder === "asc" ? 1 : -1;

    if (!searchParams.sortBy) {
      sortStage.dateAdded = 1; // Ascending - older first
    } else {
      sortStage[resolvedSortField] = sortOrder;
    }

    pipeline.push({ $sort: sortStage });

    return pipeline;
  }

  // Advanced search with aggregation
  async search(searchParams) {
    const startTime = Date.now();

    try {
      // Pagination
      const page = parseInt(searchParams.page) || 1;
      const limit = Math.min(
        parseInt(searchParams.limit) || this.defaultLimit,
        this.maxLimit
      );
      const skip = (page - 1) * limit;

      // Build aggregation pipeline
      const pipeline = this.buildSearchPipeline(searchParams);

      // Add pagination
      const paginationPipeline = [
        ...pipeline,
        {
          $facet: {
            data: [{ $skip: skip }, { $limit: limit }],
            count: [{ $count: "total" }],
          },
        },
      ];

      // Execute aggregation
      const [result] = await Listing.aggregate(paginationPipeline);
      const listings = result.data;
      const totalCount = result.count[0]?.total || 0;

      // Calculate pagination info
      const totalPages = Math.ceil(totalCount / limit);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      const duration = Date.now() - startTime;
      logHelpers.logDbOperation("search", "listings", duration);

      return {
        listings,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          hasNextPage,
          hasPrevPage,
          limit,
        },
        searchParams,
        performance: {
          duration: `${duration}ms`,
          resultsFound: totalCount,
        },
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      logHelpers.logDbOperation("search", "listings", duration, error);
      throw error;
    }
  }

  // Get search suggestions based on existing data
  async getSearchSuggestions(query, type = "location") {
    const startTime = Date.now();

    try {
      let pipeline = [];

      switch (type) {
        case "location":
          pipeline = [
            {
              $match: {
                location: { $regex: query, $options: "i" },
              },
            },
            {
              $group: {
                _id: "$location",
                count: { $sum: 1 },
              },
            },
            {
              $sort: { count: -1 },
            },
            {
              $limit: 10,
            },
            {
              $project: {
                _id: 0,
                suggestion: "$_id",
                count: 1,
              },
            },
          ];
          break;

        case "propertyType":
          pipeline = [
            {
              $match: {
                propertyType: { $regex: query, $options: "i" },
              },
            },
            {
              $group: {
                _id: "$propertyType",
                count: { $sum: 1 },
              },
            },
            {
              $sort: { count: -1 },
            },
            {
              $limit: 10,
            },
            {
              $project: {
                _id: 0,
                suggestion: "$_id",
                count: 1,
              },
            },
          ];
          break;

        default:
          throw new Error("Invalid suggestion type");
      }

      const suggestions = await Listing.aggregate(pipeline);

      const duration = Date.now() - startTime;
      logHelpers.logDbOperation("suggestions", "listings", duration);

      return suggestions;
    } catch (error) {
      const duration = Date.now() - startTime;
      logHelpers.logDbOperation("suggestions", "listings", duration, error);
      throw error;
    }
  }

  // Get search statistics
  async getSearchStats() {
    const startTime = Date.now();

    try {
      const stats = await Listing.aggregate([
        {
          $group: {
            _id: null,
            totalListings: { $sum: 1 },
            avgPrice: { $avg: "$priceNumeric" },
            minPrice: { $min: "$priceNumeric" },
            maxPrice: { $max: "$priceNumeric" },
            locations: { $addToSet: "$location" },
            propertyTypes: { $addToSet: "$propertyType" },
          },
        },
      ]);

      const rawStats =
        stats[0] || {
          totalListings: 0,
          avgPrice: 0,
          minPrice: 0,
          maxPrice: 0,
          locations: [],
          propertyTypes: [],
        };

      const formatPrice = (value) => {
        if (typeof value !== "number" || !Number.isFinite(value)) return null;
        return `? ${Math.round(value).toLocaleString("nl-NL")} per maand`;
      };

      const cleanedLocations = (rawStats.locations || []).filter(Boolean);
      const cleanedPropertyTypes = (rawStats.propertyTypes || []).filter(Boolean);

      const response = {
        totalListings: rawStats.totalListings || 0,
        avgPrice: rawStats.avgPrice
          ? parseFloat(rawStats.avgPrice.toFixed(2))
          : 0,
        minPrice: rawStats.minPrice || 0,
        maxPrice: rawStats.maxPrice || 0,
        minPriceString: formatPrice(rawStats.minPrice),
        maxPriceString: formatPrice(rawStats.maxPrice),
        avgPriceFormatted: formatPrice(rawStats.avgPrice),
        uniqueLocations: cleanedLocations.length,
        uniquePropertyTypes: cleanedPropertyTypes.length,
        locations: cleanedLocations.slice(0, 20),
        propertyTypes: cleanedPropertyTypes.slice(0, 20),
      };

      const duration = Date.now() - startTime;
      logHelpers.logDbOperation("stats", "listings", duration);

      return response;
    } catch (error) {
      const duration = Date.now() - startTime;
      logHelpers.logDbOperation("stats", "listings", duration, error);
      throw error;
    }
  }
  // Get quick statistics for dashboard
  async getQuickStats() {
    return dbErrorHandler.executeWithFallback(
      "quick-stats",
      async () => {
        const startTime = Date.now();
        const cacheKey = "quick-stats";
        let partialData = {};
        let errors = [];

        // Check cache first
        const cached = await cacheService.get(cacheKey);
        if (cached) {
          const isStaleAverage =
            cached.totalListings > 0 &&
            (!cached.averagePrice || cached.averagePrice <= 0);

          if (!isStaleAverage) {
            const duration = Date.now() - startTime;
            logHelpers.logDbOperation(
              "quick-stats",
              "listings",
              duration,
              null,
              "cache-hit"
            );
            logHelpers.logPerformance("quick-stats-cache-hit", duration, {
              cached: true,
            });
            return { ...cached, cached: true };
          }

          errors.push(
            "Cached quick stats missing average price, recalculating live data"
          );
        }

        logHelpers.logCache("get", cacheKey, false);

        // Calculate start of today in local timezone
        const now = new Date();
        const startOfToday = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate()
        );

        // Try to get stats with graceful degradation
        const stats = await this._calculateStatsWithFallback(
          startOfToday,
          partialData,
          errors
        );

        // Attempt to cache results if we have valid data
        if (
          stats.totalListings > 0 ||
          stats.averagePrice > 0 ||
          stats.newToday > 0
        ) {
          try {
            await cacheService.set(cacheKey, stats, 300);
            logHelpers.logCache("set", cacheKey);
          } catch (cacheError) {
            logHelpers.logCache("set", cacheKey, null, cacheError);
            errors.push(`Cache set failed: ${cacheError.message}`);
          }
        }

        const duration = Date.now() - startTime;

        // Log performance metrics
        logHelpers.logDbOperation("quick-stats", "listings", duration);
        logHelpers.logPerformance("quick-stats-calculation", duration, {
          cached: false,
          totalListings: stats.totalListings,
          averagePrice: stats.averagePrice,
          newToday: stats.newToday,
          hasErrors: errors.length > 0,
          errorCount: errors.length,
        });

        // Log warning if performance is slow
        if (duration > 500) {
          logHelpers.logPerformance("quick-stats-slow", duration, {
            warning: "Quick stats calculation exceeded 500ms threshold",
            threshold: "500ms",
          });
        }

        const result = {
          ...stats,
          cached: false,
          performance: {
            duration: `${duration}ms`,
            timestamp: new Date().toISOString(),
          },
        };

        // Add error information if there were issues
        if (errors.length > 0) {
          result.warnings = errors;
          result.degraded = true;
        }

        return result;
      },
      {
        maxRetries: 2,
        retryDelay: 500,
        circuitBreakerThreshold: 3,
        circuitBreakerTimeout: 30000,
        metadata: { operation: "quick-stats" },
      }
    );
  }

  // Helper method to calculate stats with graceful degradation
  async _calculateStatsWithFallback(startOfToday, partialData, errors) {
    const stats = {
      totalListings: 0,
      averagePrice: 0,
      newToday: 0,
    };

    // Try to get total count first (simplest query)
    try {
      const totalCount = await Listing.countDocuments();
      stats.totalListings = totalCount;
      partialData.totalListings = totalCount;
      logHelpers.logDbOperation("quick-stats-total-count", "listings", 0);
    } catch (error) {
      errors.push(`Total count calculation failed: ${error.message}`);
      logHelpers.logDbOperation(
        "quick-stats-total-count",
        "listings",
        0,
        error
      );
    }

    // Try to get new today count
    try {
      const newTodayCount = await Listing.countDocuments({
        dateAdded: { $gte: startOfToday },
      });
      stats.newToday = newTodayCount;
      partialData.newToday = newTodayCount;
      logHelpers.logDbOperation("quick-stats-new-today", "listings", 0);
    } catch (error) {
      errors.push(`New today count calculation failed: ${error.message}`);
      logHelpers.logDbOperation("quick-stats-new-today", "listings", 0, error);
    }

    // Try to calculate average price (most complex query)
    try {
      const avgPricePipeline = [
        {
          $match: {
            priceNumeric: { $gt: 0 },
          },
        },
        {
          $group: {
            _id: null,
            totalPrice: { $sum: "$priceNumeric" },
            validPriceCount: { $sum: 1 },
          },
        },
      ];

      const [numericPriceStats] = await Listing.aggregate(avgPricePipeline);

      let totalPrice = numericPriceStats?.totalPrice || 0;
      let totalCount = numericPriceStats?.validPriceCount || 0;

      // Backfill average calculation for listings without priceNumeric persisted
      const missingPriceListings = await Listing.find(
        {
          price: { $exists: true, $ne: null },
          $or: [
            { priceNumeric: { $exists: false } },
            { priceNumeric: { $eq: null } },
            { priceNumeric: { $lte: 0 } },
          ],
        },
        { price: 1 }
      )
        .lean()
        .exec();

      let parsedFallbackCount = 0;

      if (Array.isArray(missingPriceListings) && missingPriceListings.length) {
        for (const listing of missingPriceListings) {
          const parsed = Listing.parsePrice
            ? Listing.parsePrice(listing.price)
            : null;
          if (typeof parsed === "number" && Number.isFinite(parsed) && parsed > 0) {
            totalPrice += parsed;
            totalCount += 1;
            parsedFallbackCount += 1;
          }
        }
      }

      if (totalCount > 0) {
        stats.averagePrice = Math.round(totalPrice / totalCount);
        partialData.averagePrice = stats.averagePrice;
        logHelpers.logDbOperation(
          "quick-stats-avg-price",
          "listings",
          0,
          null,
          null,
          {
            validPriceCount: totalCount,
            averagePrice: stats.averagePrice,
            parsedFallbackCount,
          }
        );
      } else {
        errors.push("Average price calculation returned no results");
      }
    } catch (error) {
      errors.push(`Average price calculation failed: ${error.message}`);
      logHelpers.logDbOperation("quick-stats-avg-price", "listings", 0, error);
    }

    return stats;
  }
}

module.exports = new SearchService();













