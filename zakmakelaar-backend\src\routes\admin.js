const express = require("express");
const router = express.Router();
const autoApplicationService = require("../services/autoApplicationService");
const { auth, requireAdmin } = require("../middleware/auth");
const { loggers } = require("../services/logger");

/**
 * @swagger
 * components:
 *   schemas:
 *     AdminResetDailyLimitRequest:
 *       type: object
 *       required:
 *         - userId
 *       properties:
 *         userId:
 *           type: string
 *           description: User ID to reset daily limit for
 *         reason:
 *           type: string
 *           description: Reason for reset
 *           default: "Admin reset"
 *         adminId:
 *           type: string
 *           description: ID of admin performing the action
 *
 *     AdminRestartFailedApplicationsRequest:
 *       type: object
 *       required:
 *         - userId
 *       properties:
 *         userId:
 *           type: string
 *           description: User ID to restart failed applications for
 *         reason:
 *           type: string
 *           description: Reason for restart
 *           default: "Admin restart"
 *         adminId:
 *           type: string
 *           description: ID of admin performing the action
 *         maxAge:
 *           type: number
 *           description: Maximum age of failed applications to restart (in hours)
 *           default: 24
 *         resetAttempts:
 *           type: boolean
 *           description: Whether to reset attempt counter
 *           default: true
 */

/**
 * @swagger
 * /api/admin/reset-daily-limit:
 *   post:
 *     summary: Reset daily application limit for a user
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/AdminResetDailyLimitRequest'
 *     responses:
 *       200:
 *         description: Daily limit reset successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                     previousCount:
 *                       type: number
 *                     newCount:
 *                       type: number
 *                     resetDate:
 *                       type: string
 *                       format: date-time
 *                     reason:
 *                       type: string
 *                     adminId:
 *                       type: string
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User or settings not found
 *       500:
 *         description: Internal server error
 */
router.post("/reset-daily-limit", auth, requireAdmin, async (req, res) => {
  try {
    const { userId, reason, adminId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    // Add current user as adminId if not provided
    const effectiveAdminId = adminId || req.user._id;

    loggers.app.info(
      `Admin ${effectiveAdminId} resetting daily limit for user ${userId}`,
      {
        adminId: effectiveAdminId,
        targetUserId: userId,
        reason,
        ip: req.ip,
      }
    );

    const result = await autoApplicationService.resetDailyLimit(userId, {
      reason,
      adminId: effectiveAdminId,
    });

    res.json(result);
  } catch (error) {
    loggers.app.error("Error in reset daily limit admin endpoint:", error);

    if (error.message.includes("not found")) {
      return res.status(404).json({
        success: false,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      message: "Failed to reset daily limit",
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /api/admin/restart-failed-applications:
 *   post:
 *     summary: Restart all failed applications for a user
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/AdminRestartFailedApplicationsRequest'
 *     responses:
 *       200:
 *         description: Failed applications restarted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                     totalFound:
 *                       type: number
 *                     restartedCount:
 *                       type: number
 *                     failedToRestart:
 *                       type: number
 *                     restartResults:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           applicationId:
 *                             type: string
 *                           listingUrl:
 *                             type: string
 *                           previousStatus:
 *                             type: string
 *                           previousAttempts:
 *                             type: number
 *                           newStatus:
 *                             type: string
 *                           newAttempts:
 *                             type: number
 *                     options:
 *                       type: object
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User or settings not found
 *       500:
 *         description: Internal server error
 */
router.post(
  "/restart-failed-applications",
  auth,
  requireAdmin,
  async (req, res) => {
    try {
      const { userId, reason, adminId, maxAge, resetAttempts } = req.body;

      if (!userId) {
        return res.status(400).json({
          success: false,
          message: "User ID is required",
        });
      }

      // Add current user as adminId if not provided
      const effectiveAdminId = adminId || req.user._id;

      loggers.app.info(
        `Admin ${effectiveAdminId} restarting failed applications for user ${userId}`,
        {
          adminId: effectiveAdminId,
          targetUserId: userId,
          reason,
          maxAge,
          resetAttempts,
          ip: req.ip,
        }
      );

      const result = await autoApplicationService.restartFailedApplications(
        userId,
        {
          reason,
          adminId: effectiveAdminId,
          maxAge,
          resetAttempts,
        }
      );

      res.json(result);
    } catch (error) {
      loggers.app.error(
        "Error in restart failed applications admin endpoint:",
        error
      );

      if (error.message.includes("not found")) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: "Failed to restart failed applications",
        error: error.message,
      });
    }
  }
);

/**
 * @swagger
 * /api/admin/user-status/{userId}:
 *   get:
 *     summary: Get comprehensive user auto-application status
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID to get status for
 *     responses:
 *       200:
 *         description: User status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         email:
 *                           type: string
 *                         name:
 *                           type: string
 *                         createdAt:
 *                           type: string
 *                           format: date-time
 *                     autoApplication:
 *                       type: object
 *                       properties:
 *                         enabled:
 *                           type: boolean
 *                         isActive:
 *                           type: boolean
 *                         dailyCount:
 *                           type: number
 *                         dailyLimit:
 *                           type: number
 *                         lastApplication:
 *                           type: string
 *                           format: date-time
 *                         lastDailyReset:
 *                           type: object
 *                         pausedReason:
 *                           type: string
 *                         errorCount:
 *                           type: number
 *                     queueStatistics:
 *                       type: object
 *                     recentApplications:
 *                       type: array
 *                       items:
 *                         type: object
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */
router.get("/user-status/:userId", auth, requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    loggers.app.info(
      `Admin ${req.user._id} requesting status for user ${userId}`,
      {
        adminId: req.user._id,
        targetUserId: userId,
        ip: req.ip,
      }
    );

    const result = await autoApplicationService.getAdminUserStatus(userId);

    res.json(result);
  } catch (error) {
    loggers.app.error("Error in get user status admin endpoint:", error);

    if (error.message.includes("not found")) {
      return res.status(404).json({
        success: false,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      message: "Failed to get user status",
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /api/admin/bulk-reset-daily-limit:
 *   post:
 *     summary: Reset daily application limit for multiple users
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userIds
 *             properties:
 *               userIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of user IDs to reset daily limit for
 *               reason:
 *                 type: string
 *                 description: Reason for reset
 *                 default: "Bulk admin reset"
 *               adminId:
 *                 type: string
 *                 description: ID of admin performing the action
 *     responses:
 *       200:
 *         description: Bulk daily limit reset completed
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post("/bulk-reset-daily-limit", auth, requireAdmin, async (req, res) => {
  try {
    const { userIds, reason = "Bulk admin reset", adminId } = req.body;

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: "User IDs array is required",
      });
    }

    if (userIds.length > 100) {
      return res.status(400).json({
        success: false,
        message: "Maximum 100 users allowed per bulk operation",
      });
    }

    const effectiveAdminId = adminId || req.user._id;

    loggers.app.info(
      `Admin ${effectiveAdminId} performing bulk daily limit reset`,
      {
        adminId: effectiveAdminId,
        userCount: userIds.length,
        reason,
        ip: req.ip,
      }
    );

    const results = [];
    let successCount = 0;
    let failureCount = 0;

    for (const userId of userIds) {
      try {
        const result = await autoApplicationService.resetDailyLimit(userId, {
          reason,
          adminId: effectiveAdminId,
        });
        results.push({
          userId,
          success: true,
          data: result.data,
        });
        successCount++;
      } catch (error) {
        results.push({
          userId,
          success: false,
          error: error.message,
        });
        failureCount++;
        loggers.app.warn(
          `Failed to reset daily limit for user ${userId}:`,
          error
        );
      }
    }

    loggers.app.info(`Bulk daily limit reset completed`, {
      adminId: effectiveAdminId,
      totalUsers: userIds.length,
      successCount,
      failureCount,
    });

    res.json({
      success: true,
      message: `Bulk reset completed: ${successCount} successful, ${failureCount} failed`,
      data: {
        totalUsers: userIds.length,
        successCount,
        failureCount,
        results,
        adminId: effectiveAdminId,
        reason,
      },
    });
  } catch (error) {
    loggers.app.error("Error in bulk reset daily limit admin endpoint:", error);
    res.status(500).json({
      success: false,
      message: "Failed to perform bulk daily limit reset",
      error: error.message,
    });
  }
});

module.exports = router;
