import React, { useEffect, useState } from 'react';
import { View, ActivityIndicator, StyleSheet, Text } from 'react-native';
import { useAuthStore } from '../store/authStore';
import { apiService } from '../services/api';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Animated, {
  FadeIn,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSequence,
  withTiming,
  Easing
} from 'react-native-reanimated';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff'
};

interface AppInitializerProps {
  children: React.ReactNode;
}

const APP_INITIALIZED_KEY = 'app_initialized';

// Debug function to reset initialization status (for testing)
export const resetAppInitialization = async () => {
  try {
    await AsyncStorage.removeItem(APP_INITIALIZED_KEY);
    console.log('🔄 DEBUG: App initialization status reset');
  } catch (error) {
    console.error('❌ DEBUG: Error resetting app initialization:', error);
  }
};

export default function AppInitializer({ children }: AppInitializerProps) {
  const [isInitializing, setIsInitializing] = useState(true);
  const [initError, setInitError] = useState<string | null>(null);
  const { checkAuthStatus, isAuthenticated } = useAuthStore();

  // Animation values
  const logoScale = useSharedValue(1);
  const logoRotate = useSharedValue(0);

  // Start animations
  useEffect(() => {
    // Logo pulse animation
    logoScale.value = withRepeat(
      withSequence(
        withTiming(1.08, { duration: 1500, easing: Easing.inOut(Easing.ease) }),
        withTiming(1, { duration: 1500, easing: Easing.inOut(Easing.ease) })
      ),
      -1, // Infinite repeat
      true // Reverse
    );

    // Logo subtle rotation
    logoRotate.value = withRepeat(
      withSequence(
        withTiming(8, { duration: 3000, easing: Easing.inOut(Easing.ease) }),
        withTiming(-8, { duration: 3000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
  }, []);

  // Animated styles
  const logoAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: logoScale.value },
        { rotate: `${logoRotate.value}deg` }
      ]
    };
  });

  // Initialize app
  useEffect(() => {
    // Global guard to prevent multiple initializations across component remounts
    if ((globalThis as any).__APP_INITIALIZING__ || (globalThis as any).__APP_INITIALIZED__) {
      console.log('✅ AppInitializer: Already initialized or initializing, skipping...');
      setIsInitializing(false);
      return;
    }
    
    // Set global flag to prevent concurrent initializations
    (globalThis as any).__APP_INITIALIZING__ = true;
    
    const initializeApp = async () => {
      try {
        console.log('🔄 AppInitializer: Starting initialization check...');

        // Check if app has been initialized before
        const hasBeenInitialized = await AsyncStorage.getItem(APP_INITIALIZED_KEY);
        const isFirstLaunch = hasBeenInitialized !== 'true';

        console.log('🔍 AppInitializer: First launch?', isFirstLaunch);

        // Check current auth status directly from store instead of hook state
        const currentAuthStatus = useAuthStore.getState().isAuthenticated;
        
        // If not first launch and user is authenticated, skip initialization screen
        if (!isFirstLaunch && currentAuthStatus) {
          console.log('✅ AppInitializer: Skipping - app already initialized and user authenticated');
          setIsInitializing(false);
          return;
        }

        // Show initialization screen for first launch or when not authenticated
        const showInitScreen = isFirstLaunch || !currentAuthStatus;
        const delay = showInitScreen ? 1500 : 100; // Shorter delay for returning users

        console.log('🔄 AppInitializer: Will show init screen:', showInitScreen, 'delay:', delay);

        const timer = setTimeout(async () => {
          try {
            // Check API health (only on first launch or when needed)
            if (isFirstLaunch) {
              try {
                console.log('🔄 AppInitializer: Checking API health (first launch)...');
                await apiService.healthCheck();
                console.log('✅ AppInitializer: API health check passed');
              } catch (error) {
                console.warn('⚠️ AppInitializer: API health check failed:', error);
                // Don't fail initialization if health check fails
              }
            }

            // Always check authentication status
            console.log('🔄 AppInitializer: Checking authentication status...');
            await checkAuthStatus();
            console.log('✅ AppInitializer: Authentication status checked');

            // Mark app as initialized on first launch
            if (isFirstLaunch) {
              await AsyncStorage.setItem(APP_INITIALIZED_KEY, 'true');
              console.log('✅ AppInitializer: App marked as initialized');
            }

            // Decide initial navigation target to avoid route flicker
            const [{ welcomeService }, { authService }, { PreferencesValidationService }] = await Promise.all([
              import('../services/welcomeService'),
              import('../services/authService'),
              import('../services/preferencesValidationService'),
            ]);

            const authed = await authService.isAuthenticated();
            let target: string | null = null;

            if (authed) {
              const user = await authService.getCachedUser();
              const isOwner = user && (user.role === 'owner' || user.propertyOwner?.isPropertyOwner);
              if (isOwner) {
                target = '/property-owner/dashboard';
              } else if (user && PreferencesValidationService.hasMinimumRequiredPreferences(user.preferences)) {
                target = '/dashboard';
              } else {
                target = '/preferences';
              }
            } else {
              const seen = await welcomeService.hasSeenWelcome();
              target = seen ? '/login' : '/';
            }

            // Perform a single navigation replacement and keep splash until done
            try {
              const { router } = await import('expo-router');
              if (target) {
                (globalThis as any).__BOOT_NAV_DONE__ = true;
                router.replace(target as any);
                // Small delay to let navigation settle before rendering children
                await new Promise((res) => setTimeout(res, 50));
              }
            } catch (navErr) {
              console.error('❌ AppInitializer: Navigation error during boot:', navErr);
            }

            // Complete initialization (children will render on the correct route)
            console.log('✅ AppInitializer: Initialization complete (navigated to target)');
            (globalThis as any).__APP_INITIALIZED__ = true;
            (globalThis as any).__APP_INITIALIZING__ = false;
            setIsInitializing(false);
          } catch (error: any) {
            console.error('❌ AppInitializer: Initialization failed:', error);
            (globalThis as any).__APP_INITIALIZED__ = true;
            (globalThis as any).__APP_INITIALIZING__ = false;
            setInitError(error.message || 'Failed to initialize app');
            setIsInitializing(false);
          }
        }, delay);

        return () => {
          clearTimeout(timer);
          (globalThis as any).__APP_INITIALIZING__ = false;
        };
      } catch (error: any) {
        console.error('❌ AppInitializer: Error in initialization check:', error);
        (globalThis as any).__APP_INITIALIZED__ = true;
        (globalThis as any).__APP_INITIALIZING__ = false;
        setIsInitializing(false);
      }
    };

    initializeApp();
    
    // Cleanup function
    return () => {
      (globalThis as any).__APP_INITIALIZING__ = false;
    };
  }, []); // Only run once on mount - remove dependencies to prevent infinite loop

  if (isInitializing) {
    return (
      <View style={styles.container}>
        <Animated.View
          style={[styles.logoWrapper, logoAnimatedStyle]}
        >
          {/* Glow effect behind logo */}
          <View style={styles.logoGlow}>
            <LinearGradient
              colors={[THEME.accent, THEME.primary]}
              style={styles.logoGlowGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            />
          </View>

          {/* Logo container with gradient */}
          <View style={styles.logoContainer}>
            <LinearGradient
              colors={[THEME.primary, THEME.secondary]}
              style={styles.logoGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Text style={styles.logoText}>ZM</Text>
            </LinearGradient>
          </View>
        </Animated.View>

        <Animated.Text
          style={styles.appName}
          entering={FadeIn.delay(300).duration(600)}
        >
          ZakMakelaar
        </Animated.Text>

        <ActivityIndicator
          size="large"
          color={THEME.accent}
          style={styles.loader}
        />

        <Animated.Text
          style={styles.loadingText}
          entering={FadeIn.delay(600).duration(600)}
        >
          Initializing your AI rental assistant...
        </Animated.Text>
      </View>
    );
  }

  if (initError) {
    return (
      <View style={styles.container}>
        <View style={styles.logoWrapper}>
          <View style={styles.logoGlow}>
            <LinearGradient
              colors={['#ef4444', '#f97316']}
              style={styles.logoGlowGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            />
          </View>

          <View style={styles.logoContainer}>
            <LinearGradient
              colors={['#ef4444', '#b91c1c']}
              style={styles.logoGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Text style={styles.logoText}>ZM</Text>
            </LinearGradient>
          </View>
        </View>

        <Text style={styles.appName}>ZakMakelaar</Text>

        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            Failed to initialize app: {initError}
          </Text>
          <Text style={styles.retryText}>
            Please check your internet connection and restart the app.
          </Text>
        </View>
      </View>
    );
  }

  return <>{children}</>;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: THEME.dark,
    padding: 20,
  },
  logoWrapper: {
    position: 'relative',
    width: 120,
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  logoGlow: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    borderRadius: 30,
    overflow: 'hidden',
  },
  logoGlowGradient: {
    width: '100%',
    height: '100%',
  },
  logoContainer: {
    width: '85%',
    height: '85%',
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
  },
  logoGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 40,
    fontWeight: "bold",
    color: THEME.light,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: THEME.light,
    marginBottom: 32,
    textAlign: 'center',
  },
  loader: {
    marginBottom: 24,
  },
  loadingText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  errorContainer: {
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginTop: 24,
    borderWidth: 1,
    borderColor: 'rgba(239, 68, 68, 0.3)',
    maxWidth: '90%',
  },
  errorText: {
    fontSize: 16,
    color: '#ef4444',
    textAlign: 'center',
    marginBottom: 12,
    lineHeight: 24,
  },
  retryText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    lineHeight: 20,
  },
});