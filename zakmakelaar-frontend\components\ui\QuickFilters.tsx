import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Theme } from '../../constants/Theme';

export interface FilterOption {
  id: string;
  label: string;
  count?: number;
  icon?: keyof typeof Ionicons.glyphMap;
  color?: string;
}

export interface QuickFiltersProps {
  options: FilterOption[];
  activeFilter: string;
  onFilterChange: (filterId: string) => void;
  showCounts?: boolean;
}

export const QuickFilters: React.FC<QuickFiltersProps> = ({
  options,
  activeFilter,
  onFilterChange,
  showCounts = true,
}) => {
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.container}
      style={styles.scrollView}
    >
      {options.map((option, index) => {
        const isActive = option.id === activeFilter;
        
        return (
          <TouchableOpacity
            key={option.id}
            style={[
              styles.filterButton,
              isActive && styles.activeFilterButton,
              { marginRight: index < options.length - 1 ? Theme.spacing.sm : 0 },
            ]}
            onPress={() => onFilterChange(option.id)}
            activeOpacity={0.7}
          >
            {/* Icon */}
            {option.icon && (
              <Ionicons
                name={option.icon}
                size={18}
                color={
                  isActive
                    ? Theme.colors.textInverse
                    : option.color || Theme.colors.primary
                }
                style={styles.icon}
              />
            )}

            {/* Label */}
            <Text
              style={[
                styles.filterText,
                isActive && styles.activeFilterText,
                !isActive && option.color && { color: option.color },
              ]}
            >
              {option.label}
            </Text>

            {/* Count Badge */}
            {showCounts && option.count !== undefined && (
              <View
                style={[
                  styles.countBadge,
                  isActive ? styles.activeCountBadge : styles.inactiveCountBadge,
                ]}
              >
                <Text
                  style={[
                    styles.countText,
                    isActive ? styles.activeCountText : styles.inactiveCountText,
                  ]}
                >
                  {option.count}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        );
      })}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flexGrow: 0,
  },
  container: {
    paddingHorizontal: Theme.spacing.base,
    paddingVertical: Theme.spacing.sm,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing.base,
    paddingVertical: Theme.spacing.sm,
    backgroundColor: Theme.colors.surface,
    borderRadius: Theme.borderRadius.full,
    borderWidth: 1,
    borderColor: Theme.colors.border,
    minHeight: 40,
  },
  activeFilterButton: {
    backgroundColor: Theme.colors.primary,
    borderColor: Theme.colors.primary,
    ...Theme.shadows.sm,
  },
  icon: {
    marginRight: Theme.spacing.xs,
  },
  filterText: {
    fontSize: Theme.typography.fontSize.sm,
    fontWeight: Theme.typography.fontWeight.medium,
    color: Theme.colors.textSecondary,
  },
  activeFilterText: {
    color: Theme.colors.textInverse,
    fontWeight: Theme.typography.fontWeight.semiBold,
  },
  countBadge: {
    marginLeft: Theme.spacing.xs,
    paddingHorizontal: Theme.spacing.xs,
    paddingVertical: 2,
    borderRadius: Theme.borderRadius.full,
    minWidth: 20,
    alignItems: 'center',
  },
  activeCountBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  inactiveCountBadge: {
    backgroundColor: Theme.colors.neutral[100],
  },
  countText: {
    fontSize: Theme.typography.fontSize.xs,
    fontWeight: Theme.typography.fontWeight.semiBold,
  },
  activeCountText: {
    color: Theme.colors.textInverse,
  },
  inactiveCountText: {
    color: Theme.colors.textSecondary,
  },
});

export default QuickFilters;