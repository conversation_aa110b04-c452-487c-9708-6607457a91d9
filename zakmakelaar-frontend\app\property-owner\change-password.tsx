import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  TextInput,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

import { authService } from '../../services/authService';

// Hide the default navigation header
export const options = {
  headerShown: false,
};

// Theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
};

// Header Component
const Header = () => {
  const router = useRouter();
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      colors={[THEME.primary, THEME.secondary]}
      style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Animated.View
        style={styles.headerContent}
        entering={FadeInUp.duration(600)}
      >
        <TouchableOpacity
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            router.back();
          }}
          style={styles.backButton}
          activeOpacity={0.8}
        >
          <View style={styles.backButtonInner}>
            <Ionicons name="chevron-back" size={24} color={THEME.primary} />
          </View>
        </TouchableOpacity>

        <View style={styles.headerCenter}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>ZM</Text>
          </View>
          <Text style={styles.headerTitle}>Change Password</Text>
        </View>

        <View style={styles.headerSpacer} />
      </Animated.View>
    </LinearGradient>
  );
};

// Password Input Component
const PasswordInput = ({
  label,
  value,
  onChangeText,
  placeholder,
  showPassword,
  onTogglePassword,
}: {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  showPassword: boolean;
  onTogglePassword: () => void;
}) => (
  <View style={styles.inputContainer}>
    <Text style={styles.inputLabel}>{label}</Text>
    <View style={styles.passwordInputContainer}>
      <TextInput
        style={styles.passwordInput}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={THEME.gray}
        secureTextEntry={!showPassword}
        autoCapitalize="none"
        autoCorrect={false}
      />
      <TouchableOpacity
        onPress={onTogglePassword}
        style={styles.passwordToggle}
        activeOpacity={0.7}
      >
        <Ionicons
          name={showPassword ? 'eye-off' : 'eye'}
          size={20}
          color={THEME.gray}
        />
      </TouchableOpacity>
    </View>
  </View>
);

export default function PropertyOwnerChangePasswordScreen() {
  const router = useRouter();
  
  // Form state
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  // Password validation
  const validatePassword = (password: string) => {
    const errors: string[] = [];
    
    if (password.length < 6) {
      errors.push('Password must be at least 6 characters long');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    return errors;
  };

  const handleChangePassword = async () => {
    try {
      // Validation
      if (!currentPassword) {
        Alert.alert('Error', 'Please enter your current password');
        return;
      }
      
      if (!newPassword) {
        Alert.alert('Error', 'Please enter a new password');
        return;
      }
      
      if (!confirmPassword) {
        Alert.alert('Error', 'Please confirm your new password');
        return;
      }
      
      if (newPassword !== confirmPassword) {
        Alert.alert('Error', 'New passwords do not match');
        return;
      }
      
      if (currentPassword === newPassword) {
        Alert.alert('Error', 'New password must be different from current password');
        return;
      }
      
      // Validate new password strength
      const passwordErrors = validatePassword(newPassword);
      if (passwordErrors.length > 0) {
        Alert.alert('Password Requirements', passwordErrors.join('\n'));
        return;
      }
      
      setLoading(true);
      
      const response = await authService.changePassword(currentPassword, newPassword);
      
      if (response.success) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        Alert.alert(
          'Success',
          'Password changed successfully',
          [
            {
              text: 'OK',
              onPress: () => router.back(),
            },
          ]
        );
      } else {
        throw new Error(response.error || 'Failed to change password');
      }
    } catch (error: any) {
      console.error('Error changing password:', error);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert('Error', error.message || 'Failed to change password');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header />
      
      <KeyboardAvoidingView 
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Instructions */}
          <Animated.View
            style={styles.instructionsContainer}
            entering={FadeInUp.duration(600).delay(200)}
          >
            <View style={styles.instructionsIcon}>
              <Ionicons name="shield-checkmark" size={32} color={THEME.primary} />
            </View>
            <Text style={styles.instructionsTitle}>Secure Password Change</Text>
            <Text style={styles.instructionsText}>
              Enter your current password and choose a new secure password to protect your property owner account.
            </Text>
          </Animated.View>

          {/* Password Form */}
          <Animated.View
            style={styles.formContainer}
            entering={FadeInUp.duration(600).delay(400)}
          >
            <PasswordInput
              label="Current Password"
              value={currentPassword}
              onChangeText={setCurrentPassword}
              placeholder="Enter your current password"
              showPassword={showCurrentPassword}
              onTogglePassword={() => setShowCurrentPassword(!showCurrentPassword)}
            />
            
            <PasswordInput
              label="New Password"
              value={newPassword}
              onChangeText={setNewPassword}
              placeholder="Enter your new password"
              showPassword={showNewPassword}
              onTogglePassword={() => setShowNewPassword(!showNewPassword)}
            />
            
            <PasswordInput
              label="Confirm New Password"
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              placeholder="Confirm your new password"
              showPassword={showConfirmPassword}
              onTogglePassword={() => setShowConfirmPassword(!showConfirmPassword)}
            />
          </Animated.View>

          {/* Password Requirements */}
          <Animated.View
            style={styles.requirementsContainer}
            entering={FadeInUp.duration(600).delay(600)}
          >
            <Text style={styles.requirementsTitle}>Password Requirements:</Text>
            <View style={styles.requirementsList}>
              <View style={styles.requirementItem}>
                <Ionicons 
                  name={newPassword.length >= 6 ? 'checkmark-circle' : 'ellipse-outline'} 
                  size={16} 
                  color={newPassword.length >= 6 ? THEME.success : THEME.gray} 
                />
                <Text style={[
                  styles.requirementText,
                  newPassword.length >= 6 && styles.requirementMet
                ]}>
                  At least 6 characters
                </Text>
              </View>
              <View style={styles.requirementItem}>
                <Ionicons 
                  name={/[A-Z]/.test(newPassword) ? 'checkmark-circle' : 'ellipse-outline'} 
                  size={16} 
                  color={/[A-Z]/.test(newPassword) ? THEME.success : THEME.gray} 
                />
                <Text style={[
                  styles.requirementText,
                  /[A-Z]/.test(newPassword) && styles.requirementMet
                ]}>
                  One uppercase letter
                </Text>
              </View>
              <View style={styles.requirementItem}>
                <Ionicons 
                  name={/[a-z]/.test(newPassword) ? 'checkmark-circle' : 'ellipse-outline'} 
                  size={16} 
                  color={/[a-z]/.test(newPassword) ? THEME.success : THEME.gray} 
                />
                <Text style={[
                  styles.requirementText,
                  /[a-z]/.test(newPassword) && styles.requirementMet
                ]}>
                  One lowercase letter
                </Text>
              </View>
              <View style={styles.requirementItem}>
                <Ionicons 
                  name={/\d/.test(newPassword) ? 'checkmark-circle' : 'ellipse-outline'} 
                  size={16} 
                  color={/\d/.test(newPassword) ? THEME.success : THEME.gray} 
                />
                <Text style={[
                  styles.requirementText,
                  /\d/.test(newPassword) && styles.requirementMet
                ]}>
                  One number
                </Text>
              </View>
            </View>
          </Animated.View>

          {/* Change Password Button */}
          <Animated.View
            style={styles.buttonContainer}
            entering={FadeInDown.duration(600).delay(800)}
          >
            <TouchableOpacity
              style={[styles.changeButton, loading && styles.changeButtonDisabled]}
              onPress={handleChangePassword}
              disabled={loading}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={loading ? [THEME.gray, THEME.gray] : [THEME.primary, THEME.secondary]}
                style={styles.changeButtonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                {loading ? (
                  <ActivityIndicator size="small" color={THEME.light} />
                ) : (
                  <Ionicons name="shield-checkmark" size={20} color={THEME.light} />
                )}
                <Text style={styles.changeButtonText}>
                  {loading ? 'Changing Password...' : 'Change Password'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>

          {/* Bottom Spacing */}
          <View style={styles.bottomSpacing} />
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  keyboardAvoid: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    marginRight: 16,
  },
  backButtonInner: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerCenter: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoContainer: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: THEME.primary,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: THEME.light,
  },
  headerSpacer: {
    width: 40,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
  },
  instructionsContainer: {
    backgroundColor: THEME.light,
    borderRadius: 20,
    padding: 24,
    marginBottom: 20,
    alignItems: 'center',
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  instructionsIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: `${THEME.primary}15`,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  instructionsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: THEME.dark,
    marginBottom: 8,
    textAlign: 'center',
  },
  instructionsText: {
    fontSize: 14,
    color: THEME.gray,
    textAlign: 'center',
    lineHeight: 20,
  },
  formContainer: {
    backgroundColor: THEME.light,
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 8,
  },
  passwordInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: THEME.lightGray,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: THEME.dark,
  },
  passwordToggle: {
    padding: 12,
  },
  requirementsContainer: {
    backgroundColor: THEME.light,
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  requirementsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 12,
  },
  requirementsList: {
    gap: 8,
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  requirementText: {
    fontSize: 14,
    color: THEME.gray,
  },
  requirementMet: {
    color: THEME.success,
    fontWeight: '500',
  },
  buttonContainer: {
    marginTop: 20,
  },
  changeButton: {
    borderRadius: 25,
    overflow: 'hidden',
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  changeButtonDisabled: {
    opacity: 0.6,
  },
  changeButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
    paddingVertical: 16,
    gap: 8,
  },
  changeButtonText: {
    color: THEME.light,
    fontSize: 18,
    fontWeight: 'bold',
  },
  bottomSpacing: {
    height: 40,
  },
});