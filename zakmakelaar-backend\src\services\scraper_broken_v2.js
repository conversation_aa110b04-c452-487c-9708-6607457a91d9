// Original V1 scraper service
const { scrapeFunda } = require('./scrapers/fundaScraper');
const { scrapePararius } = require('./scrapers/parariusScraper');
const { scrapeHuurwoningen } = require('./scrapers/huurwoningenScraper');
const {
  cleanup,
  getScrapingMetrics
} = require('./scraperUtils');

// Agent management
let agentStatus = {
  isRunning: false,
  currentTask: "idle",
  config: {
    scrapeInterval: 15 * 60 * 1000, // 15 minutes
    maxRetries: 3,
    timeout: 30000,
    activeScrapers: ["funda"],
  },
};

const getAgentStatus = () => {
  return { ...agentStatus };
    baseStatus.v2Features = {
      performanceMonitoring: true,
      intelligentRateLimit: true,
      errorRecovery: true,
      realTimeMetrics: true
    };
  } catch (error) {
    console.warn('Could not get V2 system health:', error.message);
  }
  
  return baseStatus;
};

const startAgent = async () => {
  if (agentStatus.isRunning) {
    return { message: "Agent is already running" };
  }

  agentStatus.isRunning = true;
  agentStatus.currentTask = "starting";

  try {
    console.log('🚀 Starting enhanced scraping agent with V2 architecture...');
    
    // Use V2 scraper service for better performance and monitoring
    agentStatus.currentTask = "scraping all sites";
    const results = await scraperService.scrapeAll({
      maxPages: agentStatus.config.maxPages || 50,
      timeout: agentStatus.config.timeout
    });

    agentStatus.currentTask = "idle";
    
    return {
      message: "Agent started successfully and completed scraping with V2 architecture",
      results: results,
      performance: results.performance,
      v2Features: {
        concurrentScraping: true,
        intelligentErrorHandling: true,
        performanceMonitoring: true,
        rateLimiting: true
      }
    };
  } catch (error) {
    agentStatus.isRunning = false;
    agentStatus.currentTask = "error";
    console.error('❌ Agent failed with V2 system:', error.message);
    throw error;
  }
};

const stopAgent = async () => {
  if (!agentStatus.isRunning) {
    return { message: "Agent is not running" };
  }

  agentStatus.isRunning = false;
  agentStatus.currentTask = "stopping";

  try {
    // Stop all V2 scrapers
    await scraperService.stopAllScrapers();
    
    // Clean up any remaining resources
    await cleanup();
    
    agentStatus.currentTask = "idle";
    return { 
      message: "Agent stopped successfully",
      v2Cleanup: true
    };
  } catch (error) {
    console.error('Error stopping V2 scrapers:', error.message);
    // Still try to cleanup
    await cleanup();
    agentStatus.currentTask = "idle";
    return { message: "Agent stopped with some cleanup issues" };
  }
};

const updateAgentConfig = async (newConfig) => {
  // Validate the new configuration
  const validScrapers = ["pararius", "funda", "huurwoningen"];
  const validScrapersInConfig = newConfig.activeScrapers?.filter((scraper) =>
    validScrapers.includes(scraper)
  );

  if (validScrapersInConfig && validScrapersInConfig.length === 0) {
    throw new Error("At least one valid scraper must be active");
  }

  // Update the configuration
  agentStatus.config = {
    ...agentStatus.config,
    ...newConfig,
    useV2: true // Ensure V2 is enabled
  };

  console.log('🔧 Updated agent configuration for V2 system');
  return { 
    message: "Agent configuration updated successfully",
    v2Enhanced: true,
    config: agentStatus.config
  };
};

// New V2 specific functions
const getSystemHealth = () => {
  try {
    return scraperService.getSystemHealth();
  } catch (error) {
    console.error('Error getting system health:', error.message);
    return { status: 'UNKNOWN', error: error.message };
  }
};

const getScraperStatus = (siteName = null) => {
  try {
    return scraperService.getScraperStatus(siteName);
  } catch (error) {
    console.error('Error getting scraper status:', error.message);
    return { error: error.message };
  }
};

const exportMetrics = (format = 'json') => {
  try {
    return scraperService.exportMetrics(format);
  } catch (error) {
    console.error('Error exporting metrics:', error.message);
    return { error: error.message };
  }
};

// Export functions (backward compatible + new V2 features)
module.exports = {
  // Backward compatible functions
  scrapePararius,
  scrapeFunda,
  scrapeHuurwoningen,
  cleanup,
  getScrapingMetrics,
  getAgentStatus,
  startAgent,
  stopAgent,
  updateAgentConfig,
  
  // New V2 enhanced functions
  getSystemHealth,
  getScraperStatus,
  exportMetrics,
  scraperService, // Direct access to V2 service for advanced usage
  
  // Convenience functions
  scrapeAll: () => scraperService.scrapeAll(),
  resetRateLimit: (siteName) => scraperService.resetRateLimit(siteName)
};