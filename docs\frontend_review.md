Zakmakelaar Frontend Review
===========================

Code Quality
------------
- `services/queryClient.ts:139` relies on `localStorage`, which is undefined in React Native. This will throw when background sync tries to queue a mutation; switch to `AsyncStorage` (or guard for web vs native) before enabling mutation queuing on devices.
- `app/auto-application-dashboard.tsx:1` is a 3 646-line monolith that mixes data orchestration, presentation, and UI state. Split the screen into focused components (stats header, queue list, activity feed, modals) and import them into a small container component.
- Domain typing in `services/aiService.ts` and `services/autoApplicationService.ts` keeps AI workflows explicit and is a strength worth preserving.

Architecture & State Management
-------------------------------
- Auth-aware navigation logic lives in three places (`components/AppInitializer.tsx:87`, `app/_layout.tsx:35`, `services/navigationService.ts:70`), each firing timers and global flags. Consolidate route decisions into a single navigation controller and let other layers subscribe to its result to remove race conditions.
- `components/QueryProvider.tsx:14` calls `backgroundSync.setupBackgroundRefetch()`, which creates an uncaptured `setInterval` in `services/queryClient.ts:256`. Store the interval id and clear it during cleanup; prefer React Query’s `focusManager` / `onlineManager` APIs instead of manual timers.
- `store/aiStore.ts:142` persists large AI-derived arrays (matches, generated applications, activities) via Zustand while React Query caches the same data. Use React Query for server-backed data and limit Zustand to user-configurable settings to avoid duplication and bloated storage.

UI / UX
-------
- The welcome CTA uses bare `TouchableOpacity` without accessibility props (`app/index.tsx:459`). Add `accessibilityRole="button"` and descriptive labels to keep VoiceOver/TalkBack useful.
- When a user has previously skipped onboarding, the welcome screen renders `return null;` (`app/index.tsx:234`), producing a blank frame. Render a lightweight loader until routing completes so the app never flashes black.
- Timed reveals (e.g., extra feature cards after `setTimeout` at `app/index.tsx:176`) cause layout jumps on small screens. Replace them with conditional collapse/expand logic tied to real data readiness.

Performance
-----------
- The hard-coded 1.5 s delay in `components/AppInitializer.tsx:122` slows cold starts. Replace magic timers with actual readiness checks (await `checkAuthStatus`, health checks, then navigate immediately).
- The background refetch interval at `services/queryClient.ts:256` invalidates auth and listing queries every 15 minutes regardless of app state. Gate the timer with `AppState`/`focusManager` and clear the interval on unmount to reduce unnecessary network use.
- Offline detection always returns true (`services/queryClient.ts:127`) because it checks `navigator.onLine`. Hook into `@react-native-community/netinfo` to give React Query accurate network status and pause refetches while offline.

Security & Privacy
------------------
- Full user records are persisted to plain AsyncStorage (`services/api.ts:418`). Move sensitive fields to `SecureStore` (or encrypt before writing) and only cache the minimum required for boot navigation.
- Debug logs leak user details (e.g., property owner registration at `store/authStore.ts:125`, auth bootstrap at `store/authStore.ts:378`). Strip or guard these logs behind an environment flag in production builds.
- Personal auto-application data (income, employer, emergency contacts) is persisted via Zustand (`store/aiStore.ts:142`). Store these sensitive fields securely or reload them per session; provide a “clear local data” action for users.

AI Integration
--------------
- React Query keys include the entire preferences object (`services/queryClient.ts:104`, `hooks/useAIQueries.ts:21`), so harmless object reference changes bust the cache. Normalize keys (sorted JSON or hashed subsets) to keep caching effective.
- `useGenerateApplication` only logs failures (`hooks/useAIQueries.ts:94`) and provides no user feedback. Surface mutation error states to the UI, cache the last successful draft, and offer manual fallback actions if the AI endpoint is down.
- Duplicating AI responses in React Query and Zustand increases drift risk. Decide which layer owns AI data and let the other consume derived selectors instead of copying arrays.

Scalability
-----------
- Large screens (auto application, dashboards, owner flows) combine many concerns, limiting parallel development. Refactor into feature modules with shared UI primitives to keep individual files manageable.
- Navigation rules are hard-coded strings scattered across services (`services/navigationService.ts`, `app/_layout.tsx`, `components/AppInitializer.tsx`). Adopt a declarative route map keyed by role/state to make new personas or flows easier to add.
- Persisting ever-growing AI artifacts (matches, generated letters) in AsyncStorage will slow down devices as the user base grows. Plan for server-side pagination and keep only lightweight metadata locally.

Future Enhancements
-------------------
1. Integrate reliable offline support: feed NetInfo into React Query’s `onlineManager`, queue pending mutations with metadata, and show an offline banner or retry sheet.
2. Provide AI fallbacks: cache the last successful recommendations, allow manual search/application editing, and communicate AI outage states clearly to maintain trust.
3. Introduce role-based module boundaries with lazy-loaded stacks (e.g., a separate property-owner navigator) and a shared design system to support rapid feature growth without regressions.
