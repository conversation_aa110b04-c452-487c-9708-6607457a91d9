/**
 * Improved Browser Manager for Form Automation
 *
 * Addresses browser management issues with better resource cleanup,
 * health monitoring, and connection stability.
 */

const puppeteer = require("puppeteer");
const { loggers } = require("./logger");
const { setupPageStealth } = require("./scraperUtils");

class ImprovedBrowserManager {
  constructor(options = {}) {
    this.browser = null;
    this.isInitialized = false;
    this.healthCheckInterval = null;
    this.lastHealthCheck = null;
    this.connectionAttempts = 0;
    this.maxConnectionAttempts = 3;
    this.healthCheckIntervalMs = 30000; // 30 seconds

    this.config = {
      headless: options.headless !== false,
      devtools: options.devtools || false,
      slowMo: options.slowMo || 0,
      timeout: options.timeout || 30000,
      navigationTimeout: options.navigationTimeout || 30000,
      ...options,
    };

    // Browser launch options
    this.launchOptions = {
      headless: this.config.headless,
      devtools: this.config.devtools,
      slowMo: this.config.slowMo,
      timeout: this.config.timeout,
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--disable-accelerated-2d-canvas",
        "--no-first-run",
        "--no-zygote",
        "--single-process",
        "--disable-gpu",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-renderer-backgrounding",
        "--disable-ipc-flooding-protection",
        ...(this.config.headless
          ? []
          : ["--window-position=200,200", "--window-size=1200,800"]),
      ],
    };
  }

  /**
   * Initialize browser with improved error handling and health monitoring
   */
  async initialize() {
    if (this.isInitialized && (await this.isHealthy())) {
      return;
    }

    await this.cleanup();

    try {
      await this._launchBrowser();
      this._setupHealthMonitoring();
      this.isInitialized = true;
      this.connectionAttempts = 0;

      loggers.app.info("Browser manager initialized successfully");
    } catch (error) {
      loggers.app.error("Failed to initialize browser manager:", error);
      throw error;
    }
  }

  /**
   * Launch browser with retry mechanism and better error handling
   */
  async _launchBrowser() {
    const executablePath = this._findChromePath();

    if (!executablePath) {
      throw new Error(
        "Chrome executable not found. Install Chrome or set PUPPETEER_EXECUTABLE_PATH"
      );
    }

    const launchOptions = {
      ...this.launchOptions,
      executablePath,
    };

    let lastError;

    for (let attempt = 1; attempt <= this.maxConnectionAttempts; attempt++) {
      try {
        loggers.app.debug(
          `Browser launch attempt ${attempt}/${this.maxConnectionAttempts}`
        );

        this.browser = await puppeteer.launch(launchOptions);

        // Test browser immediately
        await this._testBrowserConnection();

        // Setup disconnect handler
        this.browser.on("disconnected", () => {
          loggers.app.warn("Browser disconnected unexpectedly");
          this._handleBrowserDisconnect();
        });

        loggers.app.info(`Browser launched successfully on attempt ${attempt}`);
        return;
      } catch (error) {
        lastError = error;
        loggers.app.warn(
          `Browser launch attempt ${attempt} failed:`,
          error.message
        );

        if (this.browser) {
          try {
            await this.browser.close();
          } catch (closeError) {
            // Ignore close errors
          }
          this.browser = null;
        }

        if (attempt < this.maxConnectionAttempts) {
          await this._delay(1000 * attempt);
        }
      }
    }

    throw new Error(
      `Failed to launch browser after ${this.maxConnectionAttempts} attempts. Last error: ${lastError.message}`
    );
  }

  /**
   * Test browser connection
   */
  async _testBrowserConnection() {
    const pages = await this.browser.pages();

    if (pages.length === 0) {
      const testPage = await this.browser.newPage();
      await testPage.close();
    } else {
      // Test existing page
      const testPage = pages[0];
      await testPage.evaluate(() => document.title);
    }
  }

  /**
   * Find Chrome executable path
   */
  _findChromePath() {
    if (process.env.PUPPETEER_EXECUTABLE_PATH) {
      return process.env.PUPPETEER_EXECUTABLE_PATH;
    }

    const fs = require("fs");
    const possiblePaths = [
      puppeteer.executablePath(),
      "/usr/bin/google-chrome-stable",
      "/usr/bin/google-chrome",
      "/usr/bin/chromium-browser",
      "/usr/bin/chromium",
      "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
      "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
    ];

    for (const path of possiblePaths) {
      try {
        if (fs.existsSync(path)) {
          return path;
        }
      } catch (e) {
        continue;
      }
    }

    return null;
  }

  /**
   * Setup health monitoring
   */
  _setupHealthMonitoring() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(async () => {
      try {
        const healthy = await this.isHealthy();
        this.lastHealthCheck = new Date();

        if (!healthy) {
          loggers.app.warn("Browser health check failed, reinitializing...");
          await this.initialize();
        }
      } catch (error) {
        loggers.app.error("Health check error:", error);
      }
    }, this.healthCheckIntervalMs);
  }

  /**
   * Check if browser is healthy
   */
  async isHealthy() {
    try {
      if (!this.browser || !this.browser.isConnected()) {
        return false;
      }

      // Test browser responsiveness
      const pages = await this.browser.pages();
      return true;
    } catch (error) {
      loggers.app.debug("Browser health check failed:", error.message);
      return false;
    }
  }

  /**
   * Handle browser disconnect
   */
  _handleBrowserDisconnect() {
    this.browser = null;
    this.isInitialized = false;

    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
  }

  /**
   * Create a new page with stealth setup
   */
  async createPage() {
    if (!this.isInitialized || !(await this.isHealthy())) {
      await this.initialize();
    }

    const page = await this.browser.newPage();

    // Setup stealth mode
    await setupPageStealth(page);

    // Set timeouts
    page.setDefaultTimeout(this.config.timeout);
    page.setDefaultNavigationTimeout(this.config.navigationTimeout);

    return page;
  }

  /**
   * Get browser instance (initializes if needed)
   */
  async getBrowser() {
    if (!this.browser || !this.isConnected()) {
      await this.initialize();
    }
    return this.browser;
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    if (this.browser) {
      try {
        const pages = await this.browser.pages();
        await Promise.all(pages.map((page) => page.close().catch(() => {})));
        await this.browser.close();
      } catch (error) {
        loggers.app.debug("Error during browser cleanup:", error.message);
      }
      this.browser = null;
    }

    this.isInitialized = false;
    this.lastHealthCheck = null;
  }

  /**
   * Get browser status
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      connected: this.browser?.isConnected() || false,
      lastHealthCheck: this.lastHealthCheck,
      connectionAttempts: this.connectionAttempts,
    };
  }

  /**
   * Utility delay function
   */
  _delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

module.exports = ImprovedBrowserManager;
