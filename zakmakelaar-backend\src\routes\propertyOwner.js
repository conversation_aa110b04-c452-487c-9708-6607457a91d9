const express = require('express');
const router = express.Router();
const propertyOwnerController = require('../controllers/propertyOwnerController');
const { auth } = require('../middleware/auth');
const rateLimiter = require('../middleware/rateLimiter');
const validation = require('../middleware/validation');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;

// Import new services
const applicantFilteringService = require('../services/applicantFilteringService');
const { ViewingSchedulerService } = require('../services/viewingSchedulerService');
const { ApplicantChecklistService } = require('../services/applicantChecklistService');
const Property = require('../models/Property');
const Application = require('../models/Application');
const { body, param, query } = require('express-validator');
const { loggers } = require('../services/logger');
const logger = loggers.propertyOwnerLogger;

/**
 * Property Owner Management Routes
 * 
 * All routes require authentication and are rate-limited
 * Requirements: 10.1, 10.2, 10.3, 10.4, 10.5, 10.6
 */

// Apply authentication middleware to all routes
router.use(auth);

// Apply rate limiting - property owners get higher limits
router.use(rateLimiter.propertyOwner);

/**
 * Property Owner Registration and Verification Routes
 * Requirements: 10.1, 10.2
 */

/**
 * @swagger
 * /api/property-owner/register:
 *   post:
 *     summary: Register as property owner
 *     description: Register the authenticated user as a property owner with business details
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - businessRegistration
 *             properties:
 *               businessRegistration:
 *                 type: string
 *                 pattern: '^\\d{8}$'
 *                 description: Dutch KvK business registration number (8 digits)
 *                 example: "********"
 *               companyName:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 100
 *                 description: Company or business name
 *                 example: "Amsterdam Properties BV"
 *               address:
 *                 type: string
 *                 minLength: 5
 *                 maxLength: 200
 *                 description: Business address
 *                 example: "Damrak 123, 1012 LP Amsterdam"
 *               phone:
 *                 type: string
 *                 pattern: '^(\\+31|0)[1-9]\\d{8}$'
 *                 description: Dutch phone number
 *                 example: "+***********"
 *               website:
 *                 type: string
 *                 format: uri
 *                 description: Company website URL
 *                 example: "https://amsterdamproperties.nl"
 *               description:
 *                 type: string
 *                 maxLength: 1000
 *                 description: Company description
 *                 example: "Professional property management company in Amsterdam"
 *     responses:
 *       201:
 *         description: Property owner registration completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Property owner registration completed successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     isPropertyOwner:
 *                       type: boolean
 *                       example: true
 *                     verificationStatus:
 *                       type: string
 *                       enum: [pending, verified, rejected]
 *                       example: "pending"
 *                     businessRegistration:
 *                       type: string
 *                       example: "********"
 *       400:
 *         description: Bad request - Validation errors or user already registered
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /api/property-owner/verify:
 *   post:
 *     summary: Verify property owner business registration
 *     description: Submit documents and information for business verification
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               documents:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of document URLs or IDs
 *                 example: ["doc1.pdf", "doc2.pdf"]
 *               businessLicense:
 *                 type: string
 *                 description: Business license document URL
 *                 example: "license.pdf"
 *               taxId:
 *                 type: string
 *                 description: Tax identification number
 *                 example: "NL********9B01"
 *     responses:
 *       200:
 *         description: Property owner verification completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Property owner verification completed"
 *                 data:
 *                   type: object
 *                   properties:
 *                     verificationStatus:
 *                       type: string
 *                       enum: [pending, verified, rejected]
 *                       example: "pending"
 *                     submittedAt:
 *                       type: string
 *                       format: date-time
 *                       example: "2024-01-20T14:30:00Z"
 *                     estimatedProcessingTime:
 *                       type: string
 *                       example: "2-3 business days"
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - User not registered as property owner
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /api/property-owner/verification-status:
 *   get:
 *     summary: Get verification status
 *     description: Get the current verification status of the property owner
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Verification status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: object
 *                   properties:
 *                     isPropertyOwner:
 *                       type: boolean
 *                       example: true
 *                     verificationStatus:
 *                       type: string
 *                       enum: [pending, verified, rejected]
 *                       example: "verified"
 *                     businessRegistration:
 *                       type: string
 *                       example: "********"
 *                     taxNumber:
 *                       type: string
 *                       example: "***B01"
 *                       description: Masked tax number for security
 *                     bankAccount:
 *                       type: string
 *                       example: "***1234"
 *                       description: Masked bank account for security
 *                     properties:
 *                       type: integer
 *                       example: 5
 *                       description: Number of properties owned
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User is not registered as a property owner
 *       500:
 *         description: Internal server error
 */

// Register as property owner
router.post('/register', 
  validation.validatePropertyOwnerRegistration,
  propertyOwnerController.registerPropertyOwner
);

// Verify property owner business registration
router.post('/verify', 
  validation.validatePropertyOwnerVerification,
  propertyOwnerController.verifyPropertyOwner
);

// Get verification status
router.get('/verification-status', 
  propertyOwnerController.getVerificationStatus
);

/**
 * @swagger
 * components:
 *   schemas:
 *     PropertyOwnerProfile:
 *       type: object
 *       properties:
 *         businessRegistration:
 *           type: string
 *           description: Dutch KvK business registration number (8 digits)
 *           example: "********"
 *         companyName:
 *           type: string
 *           description: Company or business name
 *           example: "Amsterdam Properties BV"
 *         address:
 *           type: string
 *           description: Business address
 *           example: "Damrak 123, 1012 LP Amsterdam"
 *         phone:
 *           type: string
 *           description: Business phone number
 *           example: "+***********"
 *         website:
 *           type: string
 *           description: Company website URL
 *           example: "https://amsterdamproperties.nl"
 *         description:
 *           type: string
 *           description: Company description
 *           example: "Professional property management company in Amsterdam"
 *         verificationStatus:
 *           type: string
 *           enum: [pending, verified, rejected]
 *           description: Business verification status
 *           example: "pending"
 *         notificationSettings:
 *           type: object
 *           properties:
 *             email:
 *               type: boolean
 *               description: Email notifications enabled
 *               example: true
 *             push:
 *               type: boolean
 *               description: Push notifications enabled
 *               example: true
 *             sms:
 *               type: boolean
 *               description: SMS notifications enabled
 *               example: false
 */

/**
 * @swagger
 * /api/property-owner/profile:
 *   get:
 *     summary: Get property owner profile
 *     description: Retrieve the profile information for the authenticated property owner
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Property owner profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   $ref: '#/components/schemas/PropertyOwnerProfile'
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *       403:
 *         description: Forbidden - User is not a property owner
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /api/property-owner/profile:
 *   put:
 *     summary: Update property owner profile
 *     description: Update the profile information for the authenticated property owner
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               companyName:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 100
 *                 description: Company or business name
 *                 example: "Amsterdam Properties BV"
 *               businessRegistration:
 *                 type: string
 *                 pattern: '^\\d{8}$'
 *                 description: Dutch KvK business registration number (8 digits)
 *                 example: "********"
 *               address:
 *                 type: string
 *                 minLength: 5
 *                 maxLength: 200
 *                 description: Business address
 *                 example: "Damrak 123, 1012 LP Amsterdam"
 *               phone:
 *                 type: string
 *                 pattern: '^(\\+31|0)[1-9]\\d{8}$'
 *                 description: Dutch phone number
 *                 example: "+***********"
 *               website:
 *                 type: string
 *                 format: uri
 *                 description: Company website URL
 *                 example: "https://amsterdamproperties.nl"
 *               description:
 *                 type: string
 *                 maxLength: 1000
 *                 description: Company description
 *                 example: "Professional property management company in Amsterdam"
 *               notificationSettings:
 *                 type: object
 *                 properties:
 *                   email:
 *                     type: boolean
 *                     description: Email notifications enabled
 *                     example: true
 *                   push:
 *                     type: boolean
 *                     description: Push notifications enabled
 *                     example: true
 *                   sms:
 *                     type: boolean
 *                     description: SMS notifications enabled
 *                     example: false
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Profile updated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/PropertyOwnerProfile'
 *       400:
 *         description: Bad request - Validation errors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Validation failed"
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       field:
 *                         type: string
 *                         example: "businessRegistration"
 *                       message:
 *                         type: string
 *                         example: "Business registration must be 8 digits (KvK number)"
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *       403:
 *         description: Forbidden - User is not a property owner
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */

/**
 * Property Owner Profile Routes
 * Requirements: 10.1, 10.2
 */

// Get property owner profile
router.get('/profile', 
  propertyOwnerController.getOwnerProfile
);

// Update property owner profile
router.put('/profile', 
  validation.validatePropertyOwnerProfile,
  propertyOwnerController.updateOwnerProfile
);

/**
 * @swagger
 * components:
 *   schemas:
 *     Property:
 *       type: object
 *       required:
 *         - title
 *         - address
 *         - propertyType
 *         - rent
 *       properties:
 *         _id:
 *           type: string
 *           description: Property unique identifier
 *           example: "507f1f77bcf86cd799439011"
 *         title:
 *           type: string
 *           minLength: 5
 *           maxLength: 200
 *           description: Property title
 *           example: "Modern Apartment in Amsterdam Center"
 *         description:
 *           type: string
 *           maxLength: 2000
 *           description: Property description
 *           example: "Beautiful modern apartment with great city views"
 *         address:
 *           type: object
 *           required:
 *             - street
 *             - houseNumber
 *             - postalCode
 *             - city
 *           properties:
 *             street:
 *               type: string
 *               example: "Damrak"
 *             houseNumber:
 *               type: string
 *               example: "123"
 *             postalCode:
 *               type: string
 *               pattern: '^\\d{4}\\s?[A-Z]{2}$'
 *               example: "1012AB"
 *             city:
 *               type: string
 *               example: "Amsterdam"
 *             province:
 *               type: string
 *               example: "Noord-Holland"
 *         propertyType:
 *           type: string
 *           enum: [apartment, house, studio, room, commercial]
 *           example: "apartment"
 *         size:
 *           type: number
 *           minimum: 10
 *           maximum: 10000
 *           description: Size in square meters
 *           example: 75
 *         rooms:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           example: 3
 *         bedrooms:
 *           type: integer
 *           minimum: 0
 *           maximum: 20
 *           example: 2
 *         bathrooms:
 *           type: integer
 *           minimum: 1
 *           maximum: 10
 *           example: 1
 *         rent:
 *           type: object
 *           required:
 *             - amount
 *           properties:
 *             amount:
 *               type: number
 *               minimum: 100
 *               maximum: 50000
 *               description: Monthly rent in euros
 *               example: 1500
 *             currency:
 *               type: string
 *               default: "EUR"
 *               example: "EUR"
 *             deposit:
 *               type: number
 *               description: Security deposit in euros
 *               example: 3000
 *             additionalCosts:
 *               type: object
 *               properties:
 *                 utilities:
 *                   type: number
 *                   example: 150
 *                 serviceCharges:
 *                   type: number
 *                   example: 50
 *                 parking:
 *                   type: number
 *                   example: 75
 *                 other:
 *                   type: number
 *                   example: 0
 *         features:
 *           type: object
 *           properties:
 *             furnished:
 *               type: boolean
 *               example: false
 *             interior:
 *               type: string
 *               enum: [kaal, gestoffeerd, gemeubileerd]
 *               example: "gestoffeerd"
 *             parking:
 *               type: boolean
 *               example: true
 *             balcony:
 *               type: boolean
 *               example: true
 *             garden:
 *               type: boolean
 *               example: false
 *             elevator:
 *               type: boolean
 *               example: true
 *             energyLabel:
 *               type: string
 *               enum: [A+++, A++, A+, A, B, C, D, E, F, G]
 *               example: "B"
 *         policies:
 *           type: object
 *           properties:
 *             petsAllowed:
 *               type: boolean
 *               example: false
 *             smokingAllowed:
 *               type: boolean
 *               example: false
 *             studentsAllowed:
 *               type: boolean
 *               example: true
 *             expatFriendly:
 *               type: boolean
 *               example: true
 *             minimumIncome:
 *               type: number
 *               description: Minimum monthly income required
 *               example: 4500
 *             maximumOccupants:
 *               type: integer
 *               minimum: 1
 *               maximum: 20
 *               example: 2
 *         status:
 *           type: string
 *           enum: [draft, active, rented, maintenance, inactive]
 *           example: "active"
 *         images:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               url:
 *                 type: string
 *                 format: uri
 *                 example: "https://example.com/image.jpg"
 *               caption:
 *                 type: string
 *                 example: "Living room"
 *               isPrimary:
 *                 type: boolean
 *                 example: true
 *         availabilityDate:
 *           type: string
 *           format: date
 *           example: "2024-03-01"
 *         createdAt:
 *           type: string
 *           format: date-time
 *           example: "2024-01-15T10:30:00Z"
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           example: "2024-01-20T14:45:00Z"
 *     PropertyApplication:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           example: "app123"
 *         propertyId:
 *           type: string
 *           example: "507f1f77bcf86cd799439011"
 *         applicantName:
 *           type: string
 *           example: "John Doe"
 *         applicantEmail:
 *           type: string
 *           format: email
 *           example: "<EMAIL>"
 *         tenantScore:
 *           type: number
 *           minimum: 0
 *           maximum: 100
 *           example: 85
 *         status:
 *           type: string
 *           enum: [submitted, under_review, screening, shortlisted, interview, approved, conditional, rejected]
 *           example: "submitted"
 *         appliedAt:
 *           type: string
 *           format: date-time
 *           example: "2024-01-15T10:30:00Z"
 *         moveInDate:
 *           type: string
 *           format: date
 *           example: "2024-03-01"
 *     PropertyStatistics:
 *       type: object
 *       properties:
 *         period:
 *           type: string
 *           example: "30d"
 *         properties:
 *           type: object
 *           properties:
 *             total:
 *               type: integer
 *               example: 5
 *             active:
 *               type: integer
 *               example: 4
 *             rented:
 *               type: integer
 *               example: 3
 *             vacant:
 *               type: integer
 *               example: 1
 *         applications:
 *           type: object
 *           properties:
 *             total:
 *               type: integer
 *               example: 45
 *             pending:
 *               type: integer
 *               example: 8
 *             approved:
 *               type: integer
 *               example: 12
 *             rejected:
 *               type: integer
 *               example: 15
 *         screening:
 *           type: object
 *           properties:
 *             averageScore:
 *               type: number
 *               example: 73.5
 *             totalScreened:
 *               type: integer
 *               example: 35
 *             approvalRate:
 *               type: number
 *               example: 0.34
 */

/**
 * @swagger
 * /api/property-owner/dashboard:
 *   get:
 *     summary: Get property owner dashboard
 *     description: Retrieve dashboard data including properties overview, recent applications, and key metrics
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: object
 *                   properties:
 *                     properties:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                           example: 5
 *                         active:
 *                           type: integer
 *                           example: 4
 *                         rented:
 *                           type: integer
 *                           example: 3
 *                     recentApplications:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/PropertyApplication'
 *                     screeningStats:
 *                       type: object
 *                       properties:
 *                         totalScreened:
 *                           type: integer
 *                           example: 45
 *                         averageScore:
 *                           type: number
 *                           example: 73.5
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *       403:
 *         description: Forbidden - User is not a property owner
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /api/property-owner/statistics:
 *   get:
 *     summary: Get property owner statistics
 *     description: Retrieve detailed statistics for the property owner's portfolio
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [7d, 30d, 90d, 1y]
 *           default: "30d"
 *         description: Time period for statistics
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   $ref: '#/components/schemas/PropertyStatistics'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /api/property-owner/properties:
 *   get:
 *     summary: List all properties for the owner
 *     description: Retrieve a list of all properties owned by the authenticated property owner
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [draft, active, rented, maintenance, inactive]
 *         description: Filter properties by status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of properties per page
 *     responses:
 *       200:
 *         description: Properties retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Properties retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Property'
 *                 total:
 *                   type: integer
 *                   example: 5
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 *   post:
 *     summary: Add new property
 *     description: Create a new property listing for the authenticated property owner
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Property'
 *     responses:
 *       201:
 *         description: Property created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Property add completed successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                       example: true
 *                     propertyId:
 *                       type: string
 *                       example: "507f1f77bcf86cd799439011"
 *       400:
 *         description: Bad request - Validation errors
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /api/property-owner/properties/{propertyId}:
 *   get:
 *     summary: Get single property details
 *     description: Retrieve detailed information about a specific property owned by the authenticated property owner
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *         description: Property ID
 *     responses:
 *       200:
 *         description: Property details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Property details retrieved successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Property'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Property not owned by user
 *       404:
 *         description: Property not found
 *       500:
 *         description: Internal server error
 *   put:
 *     summary: Update property
 *     description: Update an existing property owned by the authenticated property owner. Supports partial updates.
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *         description: Property ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 minLength: 5
 *                 maxLength: 200
 *                 example: "Updated Modern Apartment"
 *               description:
 *                 type: string
 *                 maxLength: 2000
 *                 example: "Updated description with new features"
 *               address:
 *                 type: object
 *                 properties:
 *                   street:
 *                     type: string
 *                     example: "Updated Street"
 *                   houseNumber:
 *                     type: string
 *                     example: "456"
 *                   postalCode:
 *                     type: string
 *                     example: "1234CD"
 *                   city:
 *                     type: string
 *                     example: "Amsterdam"
 *               rent:
 *                 type: object
 *                 properties:
 *                   amount:
 *                     type: number
 *                     example: 1600
 *                   deposit:
 *                     type: number
 *                     example: 3200
 *               features:
 *                 type: object
 *                 properties:
 *                   furnished:
 *                     type: boolean
 *                     example: true
 *                   parking:
 *                     type: boolean
 *                     example: true
 *               policies:
 *                 type: object
 *                 properties:
 *                   petsAllowed:
 *                     type: boolean
 *                     example: false
 *                   studentsAllowed:
 *                     type: boolean
 *                     example: true
 *             description: "Partial update object - only include fields you want to update"
 *     responses:
 *       200:
 *         description: Property updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Property update completed successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                       example: true
 *                     propertyId:
 *                       type: string
 *                       example: "507f1f77bcf86cd799439011"
 *                     data:
 *                       $ref: '#/components/schemas/Property'
 *       400:
 *         description: Bad request - Validation errors
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Property not owned by user
 *       404:
 *         description: Property not found
 *       500:
 *         description: Internal server error
 *   delete:
 *     summary: Remove property
 *     description: Delete a property owned by the authenticated property owner
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *         description: Property ID
 *     responses:
 *       200:
 *         description: Property removed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Property remove completed successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                       example: true
 *                     propertyId:
 *                       type: string
 *                       example: "507f1f77bcf86cd799439011"
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Property not owned by user
 *       404:
 *         description: Property not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /api/property-owner/properties/{propertyId}/activate:
 *   put:
 *     summary: Activate property listing
 *     description: Activate a property listing to make it visible to potential tenants
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *         description: Property ID
 *     responses:
 *       200:
 *         description: Property activated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Property activated successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                       example: true
 *                     propertyId:
 *                       type: string
 *                       example: "507f1f77bcf86cd799439011"
 *                     status:
 *                       type: string
 *                       example: "active"
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Property not owned by user
 *       404:
 *         description: Property not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /api/property-owner/properties/{propertyId}/deactivate:
 *   put:
 *     summary: Deactivate property listing
 *     description: Deactivate a property listing to hide it from potential tenants
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *         description: Property ID
 *     responses:
 *       200:
 *         description: Property deactivated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Property deactivated successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                       example: true
 *                     propertyId:
 *                       type: string
 *                       example: "507f1f77bcf86cd799439011"
 *                     status:
 *                       type: string
 *                       example: "inactive"
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Property not owned by user
 *       404:
 *         description: Property not found
 *       500:
 *         description: Internal server error
 */

/**
 * Property Owner Dashboard Routes
 * Requirements: 10.4, 10.5
 */

// Get property owner dashboard
router.get('/dashboard', 
  propertyOwnerController.getDashboard
);

// Get property owner statistics
router.get('/statistics', 
  propertyOwnerController.getStatistics
);

/**
 * Property Management Routes
 * Requirements: 10.3, 10.4
 */

// List all properties for the owner
router.get('/properties', 
  propertyOwnerController.getProperties
);

// Get single property details
router.get('/properties/:propertyId', 
  validation.validatePropertyId,
  propertyOwnerController.getPropertyDetails
);

// Add new property
router.post('/properties', 
  validation.validatePropertyData,
  propertyOwnerController.manageProperties
);

// Update property
router.put('/properties/:propertyId', 
  validation.validatePropertyId,
  validation.validatePropertyUpdateData,
  propertyOwnerController.manageProperties
);

// Remove property
router.delete('/properties/:propertyId', 
  validation.validatePropertyId,
  propertyOwnerController.manageProperties
);

// Activate property listing
router.put('/properties/:propertyId/activate', 
  validation.validatePropertyId,
  propertyOwnerController.activateProperty
);

// Deactivate property listing
router.put('/properties/:propertyId/deactivate', 
  validation.validatePropertyId,
  propertyOwnerController.deactivateProperty
);

// Property validation route
router.get('/properties/:propertyId/validate',
  validation.validatePropertyId,
  propertyOwnerController.validateProperty
);

/**
 * @swagger
 * /api/property-owner/applications:
 *   get:
 *     summary: Get all applications for property owner
 *     description: Retrieve all tenant applications for all properties owned by the authenticated property owner
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, approved, rejected, submitted, under_review, screening, shortlisted, interview, conditional]
 *         description: Filter applications by status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: Number of applications per page
 *     responses:
 *       200:
 *         description: Applications retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         example: "app1"
 *                       applicantName:
 *                         type: string
 *                         example: "John Doe"
 *                       applicantEmail:
 *                         type: string
 *                         example: "<EMAIL>"
 *                       applicantPhone:
 *                         type: string
 *                         example: "+31 6 ********"
 *                       propertyId:
 *                         type: string
 *                         example: "prop1"
 *                       propertyAddress:
 *                         type: string
 *                         example: "123 Main Street, Utrecht"
 *                       applicationDate:
 *                         type: string
 *                         format: date
 *                         example: "2025-01-20"
 *                       status:
 *                         type: string
 *                         enum: [pending, approved, rejected]
 *                         example: "pending"
 *                       creditScore:
 *                         type: number
 *                         example: 720
 *                       incomeVerified:
 *                         type: boolean
 *                         example: true
 *                       backgroundCheckPassed:
 *                         type: boolean
 *                         example: true
 *                       documents:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                               example: "d1"
 *                             name:
 *                               type: string
 *                               example: "ID Document"
 *                             type:
 *                               type: string
 *                               example: "identity"
 *                             url:
 *                               type: string
 *                               example: "/documents/id1.pdf"
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     limit:
 *                       type: integer
 *                       example: 50
 *                     total:
 *                       type: integer
 *                       example: 3
 *                     pages:
 *                       type: integer
 *                       example: 1
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - User is not a property owner
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /api/property-owner/properties/{propertyId}/applications:
 *   get:
 *     summary: Get applications for a property
 *     description: Retrieve all applications submitted for a specific property
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *         description: Property ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [submitted, under_review, screening, shortlisted, interview, approved, conditional, rejected]
 *         description: Filter applications by status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of applications per page
 *     responses:
 *       200:
 *         description: Applications retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: object
 *                   properties:
 *                     applications:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/PropertyApplication'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                           example: 1
 *                         limit:
 *                           type: integer
 *                           example: 20
 *                         total:
 *                           type: integer
 *                           example: 45
 *                         pages:
 *                           type: integer
 *                           example: 3
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Property not owned by user
 *       404:
 *         description: Property not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /api/property-owner/screen-tenants/{propertyId}:
 *   post:
 *     summary: Screen tenants for a property
 *     description: Run AI-powered screening on tenant applications for a specific property
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *         description: Property ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               applicationIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of application IDs to screen (empty array screens all)
 *                 example: ["app1", "app2"]
 *     responses:
 *       200:
 *         description: Tenant screening completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Tenant screening completed"
 *                 data:
 *                   type: object
 *                   properties:
 *                     screenedApplications:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           applicationId:
 *                             type: string
 *                             example: "app1"
 *                           tenantScore:
 *                             type: number
 *                             example: 85
 *                           recommendation:
 *                             type: string
 *                             enum: [approve, conditional, reject]
 *                             example: "approve"
 *                           aiAnalysis:
 *                             type: string
 *                             example: "Strong candidate with stable income and good rental history"
 *                     summary:
 *                       type: object
 *                       properties:
 *                         totalScreened:
 *                           type: integer
 *                           example: 5
 *                         averageScore:
 *                           type: number
 *                           example: 73.2
 *                         recommended:
 *                           type: integer
 *                           example: 3
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Property not owned by user
 *       404:
 *         description: Property not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /api/property-owner/rank-applicants/{propertyId}:
 *   post:
 *     summary: Rank applicants for a property
 *     description: Use AI to rank and prioritize applicants based on specified criteria
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *         description: Property ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               priorityFactors:
 *                 type: object
 *                 properties:
 *                   tenantScore:
 *                     type: number
 *                     minimum: 0
 *                     maximum: 1
 *                     example: 0.4
 *                     description: Weight for tenant score (0-1)
 *                   income:
 *                     type: number
 *                     minimum: 0
 *                     maximum: 1
 *                     example: 0.3
 *                     description: Weight for income level (0-1)
 *                   stability:
 *                     type: number
 *                     minimum: 0
 *                     maximum: 1
 *                     example: 0.2
 *                     description: Weight for employment stability (0-1)
 *                   compatibility:
 *                     type: number
 *                     minimum: 0
 *                     maximum: 1
 *                     example: 0.1
 *                     description: Weight for property compatibility (0-1)
 *               preferences:
 *                 type: object
 *                 properties:
 *                   preferStudents:
 *                     type: boolean
 *                     example: true
 *                   preferExpats:
 *                     type: boolean
 *                     example: false
 *                   minimumIncome:
 *                     type: number
 *                     example: 4500
 *     responses:
 *       200:
 *         description: Applicant ranking completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Applicant ranking completed"
 *                 data:
 *                   type: object
 *                   properties:
 *                     rankedApplicants:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           rank:
 *                             type: integer
 *                             example: 1
 *                           applicationId:
 *                             type: string
 *                             example: "app1"
 *                           applicantName:
 *                             type: string
 *                             example: "John Doe"
 *                           overallScore:
 *                             type: number
 *                             example: 87.5
 *                           tenantScore:
 *                             type: number
 *                             example: 85
 *                           compatibility:
 *                             type: number
 *                             example: 90
 *                           aiReasoning:
 *                             type: string
 *                             example: "High income, stable employment, excellent rental history"
 *                     criteria:
 *                       type: object
 *                       description: The criteria used for ranking
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Property not owned by user
 *       404:
 *         description: Property not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /api/property-owner/applications/{applicationId}/status:
 *   put:
 *     summary: Update application status
 *     description: Update the status of a tenant application
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: applicationId
 *         required: true
 *         schema:
 *           type: string
 *         description: Application ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [under_review, screening, shortlisted, interview, approved, conditional, rejected]
 *                 example: "approved"
 *                 description: New status for the application
 *               reason:
 *                 type: string
 *                 example: "Excellent candidate with strong financial background"
 *                 description: Reason for the status change
 *               notes:
 *                 type: string
 *                 example: "Schedule lease signing for next week"
 *                 description: Additional notes about the status change
 *     responses:
 *       200:
 *         description: Application status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Application status updated successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     applicationId:
 *                       type: string
 *                       example: "app1"
 *                     previousStatus:
 *                       type: string
 *                       example: "submitted"
 *                     newStatus:
 *                       type: string
 *                       example: "approved"
 *                     updatedBy:
 *                       type: string
 *                       example: "507f1f77bcf86cd799439011"
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: "2024-01-20T14:30:00Z"
 *       400:
 *         description: Bad request - Invalid status or validation errors
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Application not accessible by user
 *       404:
 *         description: Application not found
 *       500:
 *         description: Internal server error
 */

/**
 * Tenant Screening and Application Management Routes
 * Requirements: 10.4, 10.5, 10.6
 */

// Screen tenants for a property
router.post('/screen-tenants/:propertyId', 
  validation.validatePropertyId,
  validation.validateScreeningRequest,
  propertyOwnerController.screenTenants
);

// Rank applicants for a property
router.post('/rank-applicants/:propertyId', 
  validation.validatePropertyId,
  validation.validateRankingCriteria,
  propertyOwnerController.rankApplicants
);

// Get all applications for property owner
router.get('/applications', 
  propertyOwnerController.getAllApplications
);

// Get applications for a property
router.get('/properties/:propertyId/applications', 
  validation.validatePropertyId,
  propertyOwnerController.getPropertyApplications
);

// Update application status
router.put('/applications/:applicationId/status', 
  validation.validateApplicationId,
  validation.validateApplicationStatusUpdate,
  propertyOwnerController.updateApplicationStatus
);

/**
 * @swagger
 * /api/property-owner/properties/{propertyId}/report:
 *   get:
 *     summary: Generate property report
 *     description: Generate comprehensive reports for property performance, screening results, or market analysis
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *         description: Property ID
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [comprehensive, screening, performance, market]
 *           default: "comprehensive"
 *         description: Type of report to generate
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [7d, 30d, 90d, 1y]
 *           default: "30d"
 *         description: Time period for the report
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [json, pdf]
 *           default: "json"
 *         description: Report format
 *     responses:
 *       200:
 *         description: Property report generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Property report generated successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     reportType:
 *                       type: string
 *                       example: "comprehensive"
 *                     propertyId:
 *                       type: string
 *                       example: "507f1f77bcf86cd799439011"
 *                     generatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: "2024-01-20T14:30:00Z"
 *                     period:
 *                       type: string
 *                       example: "30d"
 *                     summary:
 *                       type: object
 *                       properties:
 *                         totalApplications:
 *                           type: integer
 *                           example: 25
 *                         averageTenantScore:
 *                           type: number
 *                           example: 73.5
 *                         occupancyRate:
 *                           type: number
 *                           example: 0.95
 *                         averageTimeToRent:
 *                           type: integer
 *                           example: 14
 *                     applications:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                           example: 25
 *                         approved:
 *                           type: integer
 *                           example: 8
 *                         rejected:
 *                           type: integer
 *                           example: 12
 *                         pending:
 *                           type: integer
 *                           example: 5
 *                     screening:
 *                       type: object
 *                       properties:
 *                         totalScreened:
 *                           type: integer
 *                           example: 20
 *                         averageScore:
 *                           type: number
 *                           example: 73.5
 *                         highQualityCandidates:
 *                           type: integer
 *                           example: 12
 *                     performance:
 *                       type: object
 *                       properties:
 *                         viewingToApplicationRate:
 *                           type: number
 *                           example: 0.45
 *                         applicationToApprovalRate:
 *                           type: number
 *                           example: 0.32
 *                         averageResponseTime:
 *                           type: number
 *                           example: 2.5
 *                     recommendations:
 *                       type: array
 *                       items:
 *                         type: string
 *                       example: ["Consider lowering rent by 5% to attract more applications", "Property photos could be improved"]
 *           application/pdf:
 *             schema:
 *               type: string
 *               format: binary
 *               description: PDF report file
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Property not owned by user
 *       404:
 *         description: Property not found
 *       500:
 *         description: Internal server error
 */

/**
 * Reporting Routes
 * Requirements: 10.6
 */

// Generate property report
router.get('/properties/:propertyId/report', 
  validation.validatePropertyId,
  validation.validateReportType,
  propertyOwnerController.generatePropertyReport
);

/**
 * Image Upload Configuration
 */

// Configure multer for property image uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/property-images');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    // Generate unique filename: propertyId_timestamp_originalname
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const propertyId = req.params.propertyId || 'new';
    cb(null, `${propertyId}_${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const imageFileFilter = (req, file, cb) => {
  // Accept only image files
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed'), false);
  }
};

const imageUpload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 10 // Maximum 10 images per upload
  },
  fileFilter: imageFileFilter
});

/**
 * @swagger
 * /api/property-owner/properties/{propertyId}/images:
 *   post:
 *     summary: Upload property images
 *     description: Upload one or more images for a property
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *         description: Property ID (use 'new' for new properties)
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Image files to upload
 *     responses:
 *       200:
 *         description: Images uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Images uploaded successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     images:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           url:
 *                             type: string
 *                             example: "/uploads/property-images/property_123_image.jpg"
 *                           filename:
 *                             type: string
 *                             example: "property_123_1640995200000_image.jpg"
 *                           originalName:
 *                             type: string
 *                             example: "living_room.jpg"
 *                           size:
 *                             type: integer
 *                             example: 2048576
 *       400:
 *         description: Bad request - Invalid files or validation errors
 *       401:
 *         description: Unauthorized
 *       413:
 *         description: File too large
 *       500:
 *         description: Internal server error
 */

// Upload property images
router.post('/properties/:propertyId/images',
  imageUpload.array('images', 10),
  propertyOwnerController.uploadPropertyImages
);

// === AI-BASED APPLICANT FILTERING ROUTES ===

// Filter and rank applicants with AI
router.post('/properties/:propertyId/filter-applicants',
  validation.validatePropertyId,
  propertyOwnerController.filterAndRankApplicants
);

// Get AI insights for specific application
router.get('/applications/:applicationId/insights',
  validation.validateApplicationId,
  propertyOwnerController.getApplicationInsights
);

// === VIEWING SCHEDULER ROUTES ===

// Initialize viewing schedule
router.post('/properties/:propertyId/viewing-schedule',
  validation.validatePropertyId,
  propertyOwnerController.initializeViewingSchedule
);

// Get viewing schedule
router.get('/properties/:propertyId/viewing-schedule',
  validation.validatePropertyId,
  propertyOwnerController.getViewingSchedule
);

// Update owner availability
router.put('/properties/:propertyId/availability',
  validation.validatePropertyId,
  propertyOwnerController.updateOwnerAvailability
);

// Approve viewing request
router.post('/viewing-schedule/:scheduleId/approve/:slotId',
  param('scheduleId').isMongoId().withMessage('Invalid schedule ID'),
  param('slotId').isMongoId().withMessage('Invalid slot ID'),
  body('applicationId').isMongoId().withMessage('Valid application ID required'),
  propertyOwnerController.approveViewingRequest
);

// Get viewing suggestions
router.get('/properties/:propertyId/viewing-suggestions',
  validation.validatePropertyId,
  query('daysAhead').optional().isInt({ min: 1, max: 30 }),
  propertyOwnerController.getViewingSuggestions
);

// Get viewing statistics
router.get('/properties/:propertyId/viewing-stats',
  validation.validatePropertyId,
  propertyOwnerController.getViewingStatistics
);

// === APPLICANT CHECKLIST ROUTES ===

// Create applicant checklist
router.post('/applications/:applicationId/checklist',
  validation.validateApplicationId,
  propertyOwnerController.createApplicantChecklist
);

// Get applicant checklist
router.get('/checklists/:checklistId',
  param('checklistId').isMongoId().withMessage('Invalid checklist ID'),
  propertyOwnerController.getApplicantChecklist
);

// Update checklist item
router.put('/checklists/:checklistId/:category/:itemId',
  param('checklistId').isMongoId().withMessage('Invalid checklist ID'),
  param('category').isIn(['documents', 'backgroundCheck', 'interview', 'financial', 'references']).withMessage('Invalid category'),
  param('itemId').optional().isMongoId().withMessage('Invalid item ID'),
  propertyOwnerController.updateChecklistItem
);

// Schedule interview
router.post('/checklists/:checklistId/interview',
  param('checklistId').isMongoId().withMessage('Invalid checklist ID'),
  body('scheduledDate').isISO8601().withMessage('Valid scheduled date required'),
  body('type').optional().isIn(['phone', 'video', 'in_person', 'property_viewing']),
  body('duration').optional().isInt({ min: 15, max: 180 }),
  propertyOwnerController.scheduleInterview
);

// Complete interview
router.post('/checklists/:checklistId/interview/complete',
  param('checklistId').isMongoId().withMessage('Invalid checklist ID'),
  body('assessment.overall').isInt({ min: 1, max: 5 }).withMessage('Overall rating required (1-5)'),
  body('assessment.communication').optional().isInt({ min: 1, max: 5 }),
  body('assessment.professionalism').optional().isInt({ min: 1, max: 5 }),
  body('assessment.reliability').optional().isInt({ min: 1, max: 5 }),
  body('assessment.compatibility').optional().isInt({ min: 1, max: 5 }),
  propertyOwnerController.completeInterview
);

// Add reference
router.post('/checklists/:checklistId/references',
  param('checklistId').isMongoId().withMessage('Invalid checklist ID'),
  body('type').isIn(['landlord', 'employer', 'personal']).withMessage('Valid reference type required'),
  body('name').notEmpty().withMessage('Reference name required'),
  body('contactInfo.email').optional().isEmail(),
  body('contactInfo.phone').optional().isMobilePhone(),
  propertyOwnerController.addReference
);

// Get all checklists
router.get('/checklists',
  query('stage').optional().isIn(['initial_review', 'document_collection', 'background_checks', 'interview_scheduled', 'interview_completed', 'final_review', 'decision_pending', 'completed']),
  query('priority').optional().isIn(['low', 'medium', 'high', 'urgent']),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  propertyOwnerController.getAllChecklists
);

// Enhanced dashboard data
router.get('/dashboard-data',
  propertyOwnerController.getDashboardData
);

module.exports = router;
