# Backend Improvement TODOs

- [ ] Harden document encryption in `zakmakelaar-backend/src/services/documentVaultService.js`
  - [x] Replace deprecated cipher helpers with `crypto.createCipheriv` / `createDecipheriv`
  - [x] Store IV and auth tag alongside ciphertext; introduce envelope-encrypted data keys instead of raw storage
  - [ ] Add regression tests covering upload/download/denied access paths
- [x] Reinstate personal info encryption for auto-application settings (`src/models/AutoApplicationSettings.js`) and remove dead references
- [x] Persist numeric price/room/size columns during ingestion to avoid runtime conversion in search pipelines; add supporting indexes
- [x] Tighten security middleware (CSP, rate limits, 2FA/session flows) and centralise environment-specific CORS configuration
  - [x] Introduce request ID middleware, stricter helmet/CSP policies, and environment-aware CORS defaults
- [x] Move scraping/auto-application work to a resilient job queue with retry schedules and puppeteer stealth hardening
  - [x] Harden BullMQ worker with extended locks, stall detection, and clean shutdown hooks
- [x] Extend observability with Prometheus metrics, request IDs, and `/metrics` endpoint; wire alerts for scraper/AI degradation
  - [x] Propagate `X-Request-Id` through middleware and structured logs
  - [x] Instrument Prometheus metrics producers (HTTP, queues, AI/scraper subsystems)
  - [x] Expose authenticated `/metrics` endpoint
  - [x] Add alert hooks for scraper/AI degradation signals
- [ ] Expand automated tests (auth, listings search, quick-stats fallbacks, documents vault, AI fallbacks) and integrate CI
- [ ] Finish unified property schema rollout by migrating controllers/services away from legacy branches; publish OpenAPI schemas
- [x] Add WebSocket throttling, heartbeats, and per-channel permissions in `src/services/websocketService.js`
  - [x] Enforce socket rate limits, heartbeat supervision, and role-gated channel subscriptions
