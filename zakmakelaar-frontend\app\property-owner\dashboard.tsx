import { Ionicons } from "@expo/vector-icons";
import type { NavigationProp } from "@react-navigation/native";
import { LinearGradient } from "expo-linear-gradient";
import { useNavigation, useRouter } from "expo-router";
import { StatusBar } from "expo-status-bar";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Animated,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { PropertyCard } from "../../components/property-owner/PropertyCard";
import { FilterOption, QuickFilters } from "../../components/ui/QuickFilters";
import { Theme } from "../../constants/Theme";
import { propertyOwnerService } from "../../services/propertyOwnerService";
import { useAuthStore } from "../../store/authStore";

// Define theme colors matching user dashboard
const THEME = {
  primary: "#4361ee",
  secondary: "#7209b7",
  accent: "#f72585",
  dark: "#0a0a18",
  light: "#ffffff",
  gray: "#6b7280",
  lightGray: "#f3f4f6",
  success: "#10b981",
  warning: "#f59e0b",
  danger: "#ef4444",
};

// Global type declaration for __DEV__
declare const __DEV__: boolean;

// Type guard function to check if data is BackendProperty array
const isBackendPropertyArray = (data: unknown): data is BackendProperty[] => {
  return (
    Array.isArray(data) &&
    data.every(
      (item) =>
        typeof item === "object" &&
        item !== null &&
        "_id" in item &&
        "title" in item &&
        "address" in item
    )
  );
};

// Define types for backend property data
interface BackendProperty {
  _id: string;
  title: string;
  address: {
    street: string;
    houseNumber: string;
    postalCode: string;
    city: string;
    province?: string;
    country: string;
  };
  propertyType: string;
  bedrooms: number;
  bathrooms: number;
  rent: {
    amount: number;
    currency: string;
  };
  size: number;
  images: {
    url: string;
    caption?: string;
    isPrimary: boolean;
  }[];
  status: "draft" | "active" | "rented" | "maintenance" | "inactive";
  metrics: {
    views: number;
    applications: number;
    viewings: number;
  };
}

// Define types for frontend property data (after transformation)
interface Property {
  _id: string;
  id: string;
  title: string;
  address: string; // Transformed to string
  city: string;
  type: string;
  bedrooms: number;
  bathrooms: number;
  price: number;
  size: number;
  images: string[]; // Transformed to string array
  status: "draft" | "active" | "rented" | "maintenance" | "inactive";
  occupancyStatus: string;
  applicationsCount: number;
  imageUrl: string;
}

export default function PropertyOwnerDashboard() {
  const router = useRouter();
  const navigation = useNavigation<NavigationProp<any>>();
  const insets = useSafeAreaInsets();
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [properties, setProperties] = useState<Property[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [activeFilter, setActiveFilter] = useState("all");

  // Animations
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const slideAnim = React.useRef(new Animated.Value(50)).current;

  // Configure navigation options
  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation]);

  // Initialize animations
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: Theme.animation.slow,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: Theme.animation.slow,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  useEffect(() => {
    fetchProperties();
  }, []);

  // Filter properties based on active filter
  const filteredProperties = React.useMemo(() => {
    if (activeFilter === "all") return properties;
    return properties.filter((property) => property.status === activeFilter);
  }, [properties, activeFilter]);

  // Generate filter options with counts
  const filterOptions: FilterOption[] = React.useMemo(
    () => [
      {
        id: "all",
        label: "All",
        count: properties.length,
        icon: "grid-outline",
      },
      {
        id: "active",
        label: "Active",
        count: properties.filter((p) => p.status === "active").length,
        icon: "checkmark-circle-outline",
        color: Theme.colors.success,
      },
      {
        id: "draft",
        label: "Draft",
        count: properties.filter((p) => p.status === "draft").length,
        icon: "create-outline",
        color: Theme.colors.neutral[500],
      },
      {
        id: "rented",
        label: "Rented",
        count: properties.filter((p) => p.status === "rented").length,
        icon: "home",
        color: Theme.colors.primary,
      },
    ],
    [properties]
  );

  const fetchProperties = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log("🔄 Fetching properties from backend...");
      const response = await propertyOwnerService.getProperties();

      console.log("📊 Properties response:", {
        success: response.success,
        dataLength: Array.isArray(response.data) ? response.data.length : 0,
        hasData: !!response.data,
      });

      if (
        response.success &&
        response.data &&
        isBackendPropertyArray(response.data)
      ) {
        // Transform backend data to match frontend expectations
        const transformedProperties: Property[] = response.data.map(
          (backendProperty: BackendProperty) => {
            console.log("🏠 Transforming property:", backendProperty.title);

            // Create address string from address object
            const addressString = backendProperty.address
              ? `${backendProperty.address.street} ${backendProperty.address.houseNumber}, ${backendProperty.address.city}`
              : "Address not available";

            const transformedProperty: Property = {
              _id: backendProperty._id,
              id: backendProperty._id,
              title: backendProperty.title,
              address: addressString, // Convert address object to string
              city: backendProperty.address?.city || "Unknown City",
              type: backendProperty.propertyType
                ? backendProperty.propertyType.charAt(0).toUpperCase() +
                  backendProperty.propertyType.slice(1)
                : "Unknown Type",
              bedrooms: backendProperty.bedrooms,
              bathrooms: backendProperty.bathrooms,
              price: backendProperty.rent?.amount || 0,
              size: backendProperty.size,
              status: backendProperty.status,
              occupancyStatus: getOccupancyStatus(backendProperty.status),
              applicationsCount: backendProperty.metrics?.applications || 0,
              images: backendProperty.images?.map((img) => img.url) || [], // Convert image objects to URL array
              imageUrl:
                backendProperty.images?.find((img) => img.isPrimary)?.url ||
                backendProperty.images?.[0]?.url ||
                "https://via.placeholder.com/300x200?text=No+Image",
            };

            return transformedProperty;
          }
        );

        console.log("✅ Properties transformed:", transformedProperties.length);
        setProperties(transformedProperties);
      } else {
        const errorMessage =
          response.error || response.message || "Failed to fetch properties";
        console.error("❌ Properties fetch failed:", errorMessage);
        setError(errorMessage);
      }
    } catch (err: any) {
      console.error("❌ Error fetching properties:", err);
      setError(err.message || "Failed to fetch properties");
    } finally {
      setLoading(false);
    }
  };

  const getOccupancyStatus = (status: string) => {
    switch (status) {
      case "active":
        return "Vacant";
      case "rented":
        return "Rented";
      case "maintenance":
        return "Under Maintenance";
      case "draft":
        return "Draft";
      case "inactive":
        return "Inactive";
      default:
        return "Unknown";
    }
  };

  const handlePropertyPress = (propertyId: string) => {
    // Navigate to property details screen
    router.push(`/property-owner/property-details?propertyId=${propertyId}`);
  };

  const handleActivateProperty = async (propertyId: string) => {
    setActionLoading(propertyId);

    try {
      console.log("🟢 Activating property:", propertyId);

      const response = await propertyOwnerService.activateProperty(propertyId);

      if (response.status === "success") {
        console.log("✅ Property activated successfully");

        // Update the property status in local state
        setProperties((prevProperties) =>
          prevProperties.map((property) =>
            property.id === propertyId
              ? { ...property, status: "active", occupancyStatus: "Vacant" }
              : property
          )
        );

        // Show success message with more details
        Alert.alert(
          "Property Published! 🎉",
          "Your property is now live and available for applications. Tenants can find and apply to your property.",
          [{ text: "Great!" }]
        );
      } else {
        throw new Error(response.message || "Failed to activate property");
      }
    } catch (error: any) {
      console.error("❌ Error activating property:", error);

      let errorMessage = "Failed to activate property. Please try again.";
      let showEditOption = false;

      if (error.code === "VALIDATION_ERROR") {
        errorMessage =
          error.message ||
          "Property information is incomplete. Please review and update your property details.";
        showEditOption = true;
      } else if (error.code === "UNAUTHORIZED") {
        errorMessage = "Authentication required. Please log in again.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      const buttons: {
        text: string;
        onPress?: () => void;
        style?: "default" | "cancel" | "destructive";
      }[] = [{ text: "OK" }];
      if (showEditOption) {
        buttons.unshift({
          text: "Edit Property",
          onPress: () =>
            router.push(
              `/property-owner/edit-property?propertyId=${propertyId}`
            ),
        });
      }

      Alert.alert("Activation Failed", errorMessage, buttons);
    } finally {
      setActionLoading(null);
    }
  };

  const handleDeactivateProperty = async (propertyId: string) => {
    Alert.alert(
      "Pause Property Listing",
      "This will temporarily remove your property from public listings. You can reactivate it anytime.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Pause Listing",
          style: "destructive",
          onPress: async () => {
            setActionLoading(propertyId);

            try {
              console.log("🟡 Deactivating property:", propertyId);

              const response = await propertyOwnerService.deactivateProperty(
                propertyId
              );

              if (response.status === "success") {
                console.log("✅ Property deactivated successfully");

                // Update the property status in local state
                setProperties((prevProperties) =>
                  prevProperties.map((property) =>
                    property.id === propertyId
                      ? {
                          ...property,
                          status: "inactive",
                          occupancyStatus: "Inactive",
                        }
                      : property
                  )
                );

                // Show success message
                Alert.alert(
                  "Property Paused",
                  "Your property has been removed from public listings. You can reactivate it anytime.",
                  [{ text: "OK" }]
                );
              } else {
                throw new Error(
                  response.message || "Failed to deactivate property"
                );
              }
            } catch (error: any) {
              console.error("❌ Error deactivating property:", error);
              Alert.alert(
                "Deactivation Failed",
                error.message ||
                  "Failed to pause property listing. Please try again.",
                [{ text: "OK" }]
              );
            } finally {
              setActionLoading(null);
            }
          },
        },
      ]
    );
  };

  const onRefresh = async () => {
    setRefreshing(true);
    console.log("🔄 Refreshing properties...");
    await fetchProperties();
    setRefreshing(false);
  };

  const handleRetry = () => {
    console.log("🔄 Retrying property fetch...");
    fetchProperties();
  };

  const handleAddProperty = () => {
    // Navigate to add property tab
    router.push("/property-owner/add-property");
  };

  // Greeting logic (copied from user dashboard)
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour >= 0 && hour < 6) return "Good night";
    if (hour >= 6 && hour < 12) return "Good morning";
    if (hour >= 12 && hour < 17) return "Good afternoon";
    if (hour >= 17 && hour < 22) return "Good evening";
    return "Good night";
  };

  const resolvedName = (() => {
    try {
      const firstName =
        (user as any)?.firstName ||
        (user as any)?.first_name ||
        (user as any)?.name?.first;
      const lastName =
        (user as any)?.lastName ||
        (user as any)?.last_name ||
        (user as any)?.name?.last;
      const fullName =
        (user as any)?.name ||
        (user as any)?.fullName ||
        (user as any)?.displayName;
      const email = (user as any)?.email;
      if (firstName) return `${firstName}${lastName ? ` ${lastName}` : ""}!`;
      if (typeof fullName === "string" && fullName.length > 0)
        return `${fullName}!`;
      if (email) {
        const emailName = String(email).split("@")[0];
        const capitalized =
          emailName.charAt(0).toUpperCase() + emailName.slice(1);
        return `${capitalized}!`;
      }
      return "Welcome!";
    } catch {
      return "Welcome!";
    }
  })();

  const greetingTextValue = getGreeting();

  return (
    <View style={styles.container}>
      <StatusBar style="light" />

      {/* Header with gradient matching user dashboard */}
      <LinearGradient
        colors={[THEME.primary, THEME.secondary]}
        style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <Animated.View
          style={[
            styles.headerContent,
            { opacity: fadeAnim, transform: [{ translateY: slideAnim }] },
          ]}
        >
          <View style={styles.headerLeft}>
            <View style={styles.logoContainer}>
              <Text style={styles.logoText}>ZM</Text>
            </View>
            <View style={styles.headerTextContainer}>
              <Text style={styles.greetingText}>{greetingTextValue}</Text>
              <Text style={styles.userNameText}>{resolvedName}</Text>
            </View>
          </View>

          <TouchableOpacity
            style={styles.profileButton}
            onPress={() => router.push("/property-owner/profile")}
          >
            <View style={styles.profileAvatar}>
              <Ionicons name="person" size={20} color={THEME.primary} />
            </View>
          </TouchableOpacity>
        </Animated.View>
      </LinearGradient>

      {/* Quick Filters - ALWAYS VISIBLE */}
      <QuickFilters
        options={filterOptions}
        activeFilter={activeFilter}
        onFilterChange={setActiveFilter}
      />

      {/* Content area below filters */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading properties...</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color="#FF3B30" />
          <Text style={styles.errorText}>{error}</Text>
          <Text style={styles.errorSubtext}>
            Please check your internet connection and try again
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
            <Ionicons name="refresh" size={20} color="#FFFFFF" />
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      ) : properties.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons
            name="home-outline"
            size={80}
            color={Theme.colors.neutral[300]}
          />
          <Text style={styles.emptyText}>Welcome to your dashboard!</Text>
          <Text style={styles.emptySubtext}>
            Add your first property to get started managing your real estate
            portfolio
          </Text>
          <TouchableOpacity
            style={styles.addPropertyButton}
            onPress={handleAddProperty}
          >
            <Ionicons name="add" size={20} color={Theme.colors.textInverse} />
            <Text style={styles.addPropertyButtonText}>Add First Property</Text>
          </TouchableOpacity>
        </View>
      ) : filteredProperties.length === 0 && activeFilter !== "all" ? (
        <View style={styles.emptyContainer}>
          <Ionicons
            name="filter-outline"
            size={64}
            color={Theme.colors.neutral[300]}
          />
          <Text style={styles.emptyText}>No {activeFilter} properties</Text>
          <Text style={styles.emptySubtext}>
            Try a different filter or add new properties
          </Text>
          {/* Spacer to guarantee separation from the button on all devices */}
          <View style={{ height: Theme.spacing.xl }} />
          <TouchableOpacity
            style={styles.addPropertyButton}
            onPress={handleAddProperty}
          >
            <Ionicons name="add" size={20} color={Theme.colors.textInverse} />
            <Text style={styles.addPropertyButtonText}>Add Property</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <ScrollView
          style={styles.scrollContainer}
          contentContainerStyle={styles.propertiesContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={Theme.colors.primary}
              colors={[Theme.colors.primary]}
            />
          }
          showsVerticalScrollIndicator={false}
        >
          {/* Section Header */}
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>
              {activeFilter === "all"
                ? "All Properties"
                : `${
                    activeFilter.charAt(0).toUpperCase() + activeFilter.slice(1)
                  } Properties`}
            </Text>
            <Text style={styles.sectionSubtitle}>
              {filteredProperties.length}{" "}
              {filteredProperties.length === 1 ? "property" : "properties"}
            </Text>
          </View>

          {/* Properties List */}
          <View style={styles.propertiesList}>
            {filteredProperties.map((property, index) => (
              <Animated.View
                key={property.id}
                style={{
                  opacity: fadeAnim,
                  transform: [
                    {
                      translateY: fadeAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [20 * (index + 1), 0],
                      }),
                    },
                  ],
                }}
              >
                <PropertyCard
                  property={property}
                  onPress={() => handlePropertyPress(property.id)}
                  onEdit={() =>
                    router.push(
                      `/property-owner/edit-property?propertyId=${property.id}`
                    )
                  }
                  onActivate={() => handleActivateProperty(property.id)}
                  onDeactivate={() => handleDeactivateProperty(property.id)}
                  isLoading={actionLoading === property.id}
                />
              </Animated.View>
            ))}
          </View>
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },

  // Header Styles (match user dashboard with gradient)
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  headerLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  logoContainer: {
    width: 48,
    height: 48,
    backgroundColor: THEME.light,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoText: {
    fontSize: 18,
    fontWeight: "800",
    color: THEME.primary,
  },
  headerTextContainer: {
    flex: 1,
  },
  greetingText: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.8)",
    fontWeight: "500",
  },
  userNameText: {
    fontSize: 24,
    fontWeight: "700",
    color: THEME.light,
    marginTop: 2,
  },
  profileButton: {
    padding: 4,
  },
  profileAvatar: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  // Statistics Section
  statsSection: {
    paddingHorizontal: Theme.spacing.base,
    paddingVertical: Theme.spacing.lg,
  },
  statsGrid: {
    gap: Theme.spacing.md,
  },
  statRow: {
    flexDirection: "row",
    gap: Theme.spacing.md,
  },

  // Content Sections
  scrollContainer: {
    flex: 1,
  },
  propertiesContainer: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 100,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
    paddingHorizontal: 4,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: "800",
    color: THEME.dark,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: THEME.gray,
    fontWeight: "500",
  },
  propertiesList: {
    gap: 20,
  },

  // Loading & Error States
  loadingContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 60,
    backgroundColor: THEME.light,
    borderRadius: 20,
    marginVertical: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: THEME.gray,
    fontWeight: "500",
  },
  errorContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 60,
    backgroundColor: THEME.light,
    borderRadius: 20,
    marginVertical: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  errorText: {
    fontSize: 20,
    fontWeight: "700",
    color: THEME.dark,
    marginTop: 16,
    marginBottom: 8,
  },
  errorSubtext: {
    fontSize: 14,
    color: THEME.gray,
    textAlign: "center",
    marginBottom: 20,
    lineHeight: 20,
    fontWeight: "500",
  },
  retryButton: {
    backgroundColor: THEME.accent,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  retryButtonText: {
    color: THEME.light,
    fontSize: 14,
    fontWeight: "600",
  },

  // Empty States
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 60,
    backgroundColor: THEME.light,
    borderRadius: 20,
    marginVertical: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  emptyText: {
    fontSize: 20,
    fontWeight: "700",
    color: THEME.dark,
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: THEME.gray,
    textAlign: "center",
    marginBottom: 20,
    lineHeight: 20,
    fontWeight: "500",
  },
  addPropertyButton: {
    backgroundColor: THEME.accent,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  addPropertyButtonText: {
    color: THEME.light,
    fontSize: 14,
    fontWeight: "600",
  },
});
