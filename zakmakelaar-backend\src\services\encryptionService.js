const crypto = require("crypto");
const bcrypt = require("bcrypt");
const { loggers } = require("./logger");

/**
 * EncryptionService - Handles encryption/decryption of sensitive user data
 *
 * This service provides:
 * - AES-256-GCM encryption for sensitive data
 * - Key derivation and management
 * - Field-level encryption for auto-application data
 * - Secure password hashing
 */
class EncryptionService {
  constructor() {
    this.ALGORITHM = "aes-256-gcm";
    this.KEY_LENGTH = 32; // 256 bits
    this.IV_LENGTH = 12; // 96 bits (recommended for GCM)
    this.TAG_LENGTH = 16; // 128 bits
    this.SALT_ROUNDS = 12;

    // Master key from environment (should be 64 hex characters)
    this.MASTER_KEY =
      process.env.ENCRYPTION_MASTER_KEY || this._generateMasterKey();

    if (!process.env.ENCRYPTION_MASTER_KEY) {
      loggers.app.warn(
        "ENCRYPTION_MASTER_KEY not set in environment. Using generated key."
      );
    }
  }

  /**
   * Generate a master key (for development only)
   * @private
   * @returns {string} Hex-encoded master key
   */
  _generateMasterKey() {
    return crypto.randomBytes(32).toString("hex");
  }

  /**
   * Derive encryption key from master key and user ID
   * @private
   * @param {string} userId - User ID for key derivation
   * @returns {Buffer} Derived encryption key
   */
  _deriveKey(userId) {
    if (!this.MASTER_KEY) {
      throw new Error("MASTER_KEY is not set");
    }

    if (this.MASTER_KEY.length !== 64) {
      throw new Error(
        `MASTER_KEY must be 64 hex characters, got ${this.MASTER_KEY.length}`
      );
    }

    const masterKeyBuffer = Buffer.from(this.MASTER_KEY, "hex");
    const salt = crypto.createHash("sha256").update(String(userId)).digest();
    return crypto.pbkdf2Sync(
      masterKeyBuffer,
      salt,
      100000,
      this.KEY_LENGTH,
      "sha256"
    );
  }

  /**
   * Encrypt sensitive data
   * @param {string} plaintext - Data to encrypt
   * @param {string} userId - User ID for key derivation
   * @returns {string} Encrypted data (base64 encoded)
   */
  encrypt(plaintext, userId) {
    if (plaintext === undefined || plaintext === null) {
      return plaintext;
    }

    if (!userId) {
      throw new Error("User ID is required for encryption");
    }

    try {
      const key = this._deriveKey(userId);
      const iv = crypto.randomBytes(this.IV_LENGTH);
      const cipher = crypto.createCipheriv(this.ALGORITHM, key, iv, {
        authTagLength: this.TAG_LENGTH,
      });

      const encrypted = Buffer.concat([
        cipher.update(String(plaintext), "utf8"),
        cipher.final(),
      ]);
      const authTag = cipher.getAuthTag();

      return [
        iv.toString("base64"),
        authTag.toString("base64"),
        encrypted.toString("base64"),
      ].join(":");
    } catch (error) {
      loggers.app.error("Encryption error:", {
        message: error.message,
        userId,
      });
      throw new Error("Failed to encrypt data");
    }
  }

  /**
   * Decrypt sensitive data
   * @param {string} encryptedData - Base64 encoded encrypted data
   * @param {string} userId - User ID for key derivation
   * @returns {string} Decrypted plaintext
   */
  decrypt(encryptedData, userId) {
    if (encryptedData === undefined || encryptedData === null) {
      return encryptedData;
    }

    if (!userId) {
      throw new Error("User ID is required for decryption");
    }

    if (typeof encryptedData !== "string") {
      return encryptedData;
    }

    const parts = encryptedData.split(":");
    if (parts.length !== 3) {
      // Not an encrypted payload we recognise
      return encryptedData;
    }

    try {
      const [ivB64, tagB64, dataB64] = parts;
      const key = this._deriveKey(userId);
      const iv = Buffer.from(ivB64, "base64");
      const authTag = Buffer.from(tagB64, "base64");
      const encrypted = Buffer.from(dataB64, "base64");

      const decipher = crypto.createDecipheriv(this.ALGORITHM, key, iv, {
        authTagLength: this.TAG_LENGTH,
      });
      decipher.setAuthTag(authTag);

      const decrypted = Buffer.concat([decipher.update(encrypted), decipher.final()]);
      return decrypted.toString("utf8");
    } catch (error) {
      loggers.app.error("Decryption error:", {
        message: error.message,
        userId,
      });
      throw new Error("Failed to decrypt data");
    }
  }

  /**
   * Encrypt an object's sensitive fields
   * @param {Object} obj - Object containing sensitive data
   * @param {Array} sensitiveFields - Array of field names to encrypt
   * @param {string} userId - User ID for key derivation
   * @returns {Object} Object with encrypted sensitive fields
   */
  encryptObject(obj, sensitiveFields, userId) {
    if (!obj || typeof obj !== "object") {
      return obj;
    }

    const encrypted = { ...obj };

    sensitiveFields.forEach((field) => {
      if (obj[field] !== undefined && obj[field] !== null) {
        encrypted[field] = this.encrypt(String(obj[field]), userId);
      }
    });

    return encrypted;
  }

  /**
   * Decrypt an object's sensitive fields
   * @param {Object} obj - Object with encrypted sensitive data
   * @param {Array} sensitiveFields - Array of field names to decrypt
   * @param {string} userId - User ID for key derivation
   * @returns {Object} Object with decrypted sensitive fields
   */
  decryptObject(obj, sensitiveFields, userId) {
    if (!obj || typeof obj !== "object") {
      return obj;
    }

    const decrypted = { ...obj };

    sensitiveFields.forEach((field) => {
      if (obj[field] !== undefined && obj[field] !== null) {
        try {
          decrypted[field] = this.decrypt(obj[field], userId);
        } catch (error) {
          loggers.app.warn(`Failed to decrypt field ${field}:`, error.message);
          // Keep encrypted value if decryption fails
        }
      }
    });

    return decrypted;
  }

  /**
   * Hash password securely
   * @param {string} password - Plain text password
   * @returns {Promise<string>} Hashed password
   */
  async hashPassword(password) {
    try {
      return await bcrypt.hash(password, this.SALT_ROUNDS);
    } catch (error) {
      loggers.app.error("Password hashing error:", error);
      throw new Error("Failed to hash password");
    }
  }

  /**
   * Verify password against hash
   * @param {string} password - Plain text password
   * @param {string} hash - Hashed password
   * @returns {Promise<boolean>} True if password matches
   */
  async verifyPassword(password, hash) {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      loggers.app.error("Password verification error:", error);
      return false;
    }
  }

  /**
   * Generate secure random token
   * @param {number} length - Token length in bytes (default: 32)
   * @returns {string} Hex-encoded random token
   */
  generateSecureToken(length = 32) {
    return crypto.randomBytes(length).toString("hex");
  }

  /**
   * Generate secure random string for IDs
   * @param {number} length - String length (default: 16)
   * @returns {string} URL-safe random string
   */
  generateSecureId(length = 16) {
    return crypto
      .randomBytes(Math.ceil((length * 3) / 4))
      .toString("base64")
      .slice(0, length)
      .replace(/\+/g, "-")
      .replace(/\//g, "_")
      .replace(/=/g, "");
  }

  /**
   * Create HMAC signature for data integrity
   * @param {string} data - Data to sign
   * @param {string} secret - Secret key for signing
   * @returns {string} HMAC signature
   */
  createSignature(data, secret) {
    return crypto.createHmac("sha256", secret).update(data).digest("hex");
  }

  /**
   * Verify HMAC signature
   * @param {string} data - Original data
   * @param {string} signature - HMAC signature to verify
   * @param {string} secret - Secret key for verification
   * @returns {boolean} True if signature is valid
   */
  verifySignature(data, signature, secret) {
    const expectedSignature = this.createSignature(data, secret);
    return crypto.timingSafeEqual(
      Buffer.from(signature, "hex"),
      Buffer.from(expectedSignature, "hex")
    );
  }

  /**
   * Encrypt auto-application personal info
   * @param {Object} personalInfo - Personal information object
   * @param {string} userId - User ID
   * @returns {Object} Encrypted personal info
   */
  encryptPersonalInfo(personalInfo, userId) {
    const sensitiveFields = [
      "fullName",
      "email",
      "phone",
      "dateOfBirth",
      "nationality",
      "occupation",
      "employer",
      "monthlyIncome",
      "guarantorInfo.name",
      "guarantorInfo.email",
      "guarantorInfo.phone",
      "guarantorInfo.monthlyIncome",
      "emergencyContact.name",
      "emergencyContact.email",
      "emergencyContact.phone",
    ];

    return this._encryptNestedObject(personalInfo, sensitiveFields, userId);
  }

  /**
   * Decrypt auto-application personal info
   * @param {Object} encryptedPersonalInfo - Encrypted personal information
   * @param {string} userId - User ID
   * @returns {Object} Decrypted personal info
   */
  decryptPersonalInfo(encryptedPersonalInfo, userId) {
    const sensitiveFields = [
      "fullName",
      "email",
      "phone",
      "dateOfBirth",
      "nationality",
      "occupation",
      "employer",
      "monthlyIncome",
      "guarantorInfo.name",
      "guarantorInfo.email",
      "guarantorInfo.phone",
      "guarantorInfo.monthlyIncome",
      "emergencyContact.name",
      "emergencyContact.email",
      "emergencyContact.phone",
    ];

    const decrypted = this._decryptNestedObject(
      encryptedPersonalInfo,
      sensitiveFields,
      userId
    );

    if (decrypted && decrypted.dateOfBirth) {
      const parsedDate = new Date(decrypted.dateOfBirth);
      if (!Number.isNaN(parsedDate.getTime())) {
        decrypted.dateOfBirth = parsedDate;
      }
    }

    if (decrypted && decrypted.monthlyIncome !== undefined) {
      const parsedIncome = parseFloat(decrypted.monthlyIncome);
      if (!Number.isNaN(parsedIncome)) {
        decrypted.monthlyIncome = parsedIncome;
      }
    }

    if (
      decrypted &&
      decrypted.guarantorInfo &&
      decrypted.guarantorInfo.monthlyIncome !== undefined
    ) {
      const parsedGuarantorIncome = parseFloat(
        decrypted.guarantorInfo.monthlyIncome
      );
      if (!Number.isNaN(parsedGuarantorIncome)) {
        decrypted.guarantorInfo.monthlyIncome = parsedGuarantorIncome;
      }
    }

    return decrypted;
  }

  /**
   * Encrypt nested object fields
   * @private
   * @param {Object} obj - Object to encrypt
   * @param {Array} fields - Dot-notation field paths
   * @param {string} userId - User ID
   * @returns {Object} Object with encrypted fields
   */
  _encryptNestedObject(obj, fields, userId) {
    if (!obj) return obj;

    const result = JSON.parse(JSON.stringify(obj)); // Deep clone

    fields.forEach((fieldPath) => {
      const value = this._getNestedValue(result, fieldPath);
      if (value !== undefined && value !== null) {
        this._setNestedValue(
          result,
          fieldPath,
          this.encrypt(String(value), userId)
        );
      }
    });

    return result;
  }

  /**
   * Decrypt nested object fields
   * @private
   * @param {Object} obj - Object to decrypt
   * @param {Array} fields - Dot-notation field paths
   * @param {string} userId - User ID
   * @returns {Object} Object with decrypted fields
   */
  _decryptNestedObject(obj, fields, userId) {
    if (!obj) return obj;

    const result = JSON.parse(JSON.stringify(obj)); // Deep clone

    fields.forEach((fieldPath) => {
      const value = this._getNestedValue(result, fieldPath);
      if (value !== undefined && value !== null) {
        try {
          this._setNestedValue(result, fieldPath, this.decrypt(value, userId));
        } catch (error) {
          loggers.app.warn(
            `Failed to decrypt nested field ${fieldPath}:`,
            error.message
          );
        }
      }
    });

    return result;
  }

  /**
   * Get nested object value by dot notation
   * @private
   */
  _getNestedValue(obj, path) {
    return path.split(".").reduce((current, key) => current?.[key], obj);
  }

  /**
   * Set nested object value by dot notation
   * @private
   */
  _setNestedValue(obj, path, value) {
    const keys = path.split(".");
    const lastKey = keys.pop();
    const target = keys.reduce((current, key) => {
      if (!current[key]) current[key] = {};
      return current[key];
    }, obj);
    target[lastKey] = value;
  }

  isEncrypted(value) {
    if (typeof value !== "string") {
      return false;
    }
    const parts = value.split(":");
    return parts.length === 3 && parts.every((part) => part.length > 0);
  }
}

module.exports = new EncryptionService();
