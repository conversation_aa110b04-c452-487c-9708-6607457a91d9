// ZakMakelaar API Service
// Centralized API communication layer for dashboard

class ApiService {
    constructor() {
        this.baseUrl = 'http://localhost:3000';
        this.authToken = localStorage.getItem('authToken');
    }

    // Set authentication token
    setAuthToken(token) {
        this.authToken = token;
        localStorage.setItem('authToken', token);
    }

    // Remove authentication token
    clearAuthToken() {
        this.authToken = null;
        localStorage.removeItem('authToken');
    }

    // Get headers for API requests
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json',
        };
        
        if (this.authToken) {
            headers['Authorization'] = `Bearer ${this.authToken}`;
        }
        
        return headers;
    }

    // Generic API request method
    async makeRequest(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        
        const config = {
            headers: this.getHeaders(),
            ...options
        };

        try {
            const response = await fetch(url, config);
            
            // Handle authentication errors
            if (response.status === 401) {
                this.clearAuthToken();
                throw new Error('Authentication required. Please log in.');
            }
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || `HTTP Error: ${response.status}`);
            }
            
            return data;
        } catch (error) {
            console.error(`API Request failed for ${endpoint}:`, error);
            throw error;
        }
    }

    // Authentication methods
    async login(email, password) {
        const response = await this.makeRequest('/api/auth/login', {
            method: 'POST',
            body: JSON.stringify({ email, password })
        });
        
        if (response.data?.token) {
            this.setAuthToken(response.data.token);
        }
        
        return response;
    }

    async register(userData) {
        return this.makeRequest('/api/auth/register', {
            method: 'POST',
            body: JSON.stringify(userData)
        });
    }

    async logout() {
        try {
            await this.makeRequest('/api/auth/logout', {
                method: 'POST'
            });
        } finally {
            this.clearAuthToken();
        }
    }

    // User profile methods
    async getUserProfile() {
        return this.makeRequest('/api/auth/profile');
    }

    // Property Owner methods
    async getPropertyOwnerDashboard() {
        return this.makeRequest('/api/property-owner/dashboard');
    }

    async getPropertyOwnerStatistics(period = '30d') {
        return this.makeRequest(`/api/property-owner/statistics?period=${period}`);
    }

    async getProperties(filters = {}) {
        const queryParams = new URLSearchParams(filters).toString();
        const endpoint = `/api/property-owner/properties${queryParams ? '?' + queryParams : ''}`;
        return this.makeRequest(endpoint);
    }

    async getPropertyDetails(propertyId) {
        return this.makeRequest(`/api/property-owner/properties/${propertyId}`);
    }

    async createProperty(propertyData) {
        return this.makeRequest('/api/property-owner/properties', {
            method: 'POST',
            body: JSON.stringify(propertyData)
        });
    }

    async updateProperty(propertyId, propertyData) {
        return this.makeRequest(`/api/property-owner/properties/${propertyId}`, {
            method: 'PUT',
            body: JSON.stringify(propertyData)
        });
    }

    async deleteProperty(propertyId) {
        return this.makeRequest(`/api/property-owner/properties/${propertyId}`, {
            method: 'DELETE'
        });
    }

    async activateProperty(propertyId) {
        return this.makeRequest(`/api/property-owner/properties/${propertyId}/activate`, {
            method: 'PUT'
        });
    }

    async deactivateProperty(propertyId) {
        return this.makeRequest(`/api/property-owner/properties/${propertyId}/deactivate`, {
            method: 'PUT'
        });
    }

    // Application methods
    async getApplications(filters = {}) {
        const queryParams = new URLSearchParams(filters).toString();
        const endpoint = `/api/property-owner/applications${queryParams ? '?' + queryParams : ''}`;
        return this.makeRequest(endpoint);
    }

    async getPropertyApplications(propertyId, filters = {}) {
        const queryParams = new URLSearchParams(filters).toString();
        const endpoint = `/api/property-owner/properties/${propertyId}/applications${queryParams ? '?' + queryParams : ''}`;
        return this.makeRequest(endpoint);
    }

    async updateApplicationStatus(applicationId, statusData) {
        return this.makeRequest(`/api/property-owner/applications/${applicationId}/status`, {
            method: 'PUT',
            body: JSON.stringify(statusData)
        });
    }

    // Screening and ranking methods
    async screenTenants(propertyId, applicationIds = []) {
        return this.makeRequest(`/api/property-owner/screen-tenants/${propertyId}`, {
            method: 'POST',
            body: JSON.stringify({ applicationIds })
        });
    }

    async rankApplicants(propertyId, criteria) {
        return this.makeRequest(`/api/property-owner/rank-applicants/${propertyId}`, {
            method: 'POST',
            body: JSON.stringify(criteria)
        });
    }

    // Report methods
    async generatePropertyReport(propertyId, type = 'comprehensive', period = '30d', format = 'json') {
        const queryParams = new URLSearchParams({ type, period, format }).toString();
        return this.makeRequest(`/api/property-owner/properties/${propertyId}/report?${queryParams}`);
    }

    // Notification methods
    async getNotifications(filters = {}) {
        const queryParams = new URLSearchParams(filters).toString();
        const endpoint = `/api/notifications${queryParams ? '?' + queryParams : ''}`;
        return this.makeRequest(endpoint);
    }

    async markNotificationAsRead(notificationId) {
        return this.makeRequest(`/api/notifications/${notificationId}/read`, {
            method: 'PUT'
        });
    }

    // Public property search (no auth required)
    async searchProperties(searchParams = {}) {
        const queryParams = new URLSearchParams(searchParams).toString();
        const endpoint = `/api/properties/search${queryParams ? '?' + queryParams : ''}`;
        
        // Override headers to exclude auth for public search
        const config = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        try {
            const response = await fetch(`${this.baseUrl}${endpoint}`, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || `HTTP Error: ${response.status}`);
            }
            
            return data;
        } catch (error) {
            console.error(`Public property search failed:`, error);
            throw error;
        }
    }

    // Utility methods
    isAuthenticated() {
        return !!this.authToken;
    }

    async checkApiHealth() {
        try {
            const response = await fetch(this.baseUrl);
            return await response.json();
        } catch (error) {
            console.error('API health check failed:', error);
            return { status: 'error', message: 'API unavailable' };
        }
    }
}

// Export singleton instance
const apiService = new ApiService();
window.ApiService = ApiService;
window.apiService = apiService;

export default apiService;