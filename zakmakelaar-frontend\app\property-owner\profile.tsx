import React, { useEffect } from "react";
import { View, StyleSheet } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { StatusBar } from "expo-status-bar";
import { useNavigation } from "expo-router";
import { PropertyOwnerProfileScreen } from "../../components/property-owner/PropertyOwnerProfileScreen";

// Define theme colors matching user dashboard
const THEME = {
  lightGray: "#f3f4f6",
};

export default function PropertyOwnerProfile() {
  const navigation = useNavigation();

  // Disable the default navigation header
  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
      gestureEnabled: true,
    });
  }, [navigation]);

  return (
    <SafeAreaView style={styles.container} edges={["left", "right", "bottom"]}>
      <StatusBar style="dark" />
      <PropertyOwnerProfileScreen />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
});
