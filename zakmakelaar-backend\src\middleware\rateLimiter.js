const rateLimit = require('express-rate-limit');
const config = require('../config/config');

// General rate limiter for all requests
const generalLimiter = rateLimit({
  windowMs: config.rateLimitWindowMs, // 15 minutes by default
  max: config.nodeEnv === 'development' ? 10000 : config.rateLimitMaxRequests, // Much higher limit for development
  message: {
    status: 'error',
    message: 'Too many requests from this IP, please try again later.'
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  skip: (req) => {
    // Skip rate limiting for localhost in development
    if (config.nodeEnv === 'development' && 
        (req.ip === '127.0.0.1' || req.ip === '::1' || req.ip === 'localhost' || req.hostname === 'localhost')) {
      return true;
    }
    if (req.path && req.path.startsWith('/metrics')) {
      return true;
    }
    return false;
  },
});

const authenticatedUserLimiter = rateLimit({
  windowMs: parseInt(process.env.AUTHENTICATED_RATE_LIMIT_WINDOW_MS, 10) || 5 * 60 * 1000,
  max: parseInt(process.env.AUTHENTICATED_RATE_LIMIT_MAX_REQUESTS, 10) || 300,
  message: {
    status: 'error',
    message: 'Too many requests for this account, please slow down.',
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    if (req.user && req.user._id) {
      return `user_${req.user._id.toString()}`;
    }
    return req.ip;
  },
});

// Strict rate limiter for authentication endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 requests per windowMs
  message: {
    status: 'error',
    message: 'Too many authentication attempts from this IP, please try again after 15 minutes.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Don't count successful requests
});

// Rate limiter for scraping endpoints
const scrapingLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 5, // Limit each IP to 5 scraping requests per 5 minutes
  message: {
    status: 'error',
    message: 'Too many scraping requests from this IP, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiter for password reset (if implemented)
const passwordResetLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Limit each IP to 3 password reset requests per hour
  message: {
    status: 'error',
    message: 'Too many password reset attempts from this IP, please try again after an hour.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiter for property owner operations
const propertyOwnerLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // Higher limit for property owners (200 requests per 15 minutes)
  message: {
    status: 'error',
    message: 'Too many property owner requests from this IP, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Use user ID if available for authenticated requests, otherwise IP
    return req.user ? `user_${req.user._id}` : req.ip;
  }
});

// Rate limiter for tenant screening operations
const screeningLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 50, // Limit screening operations to prevent abuse
  message: {
    status: 'error',
    message: 'Too many screening requests, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    return req.user ? `screening_${req.user._id}` : req.ip;
  }
});

// Rate limiter for property management operations
const propertyManagementLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Allow frequent property management operations
  message: {
    status: 'error',
    message: 'Too many property management requests, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    return req.user ? `property_mgmt_${req.user._id}` : req.ip;
  }
});

// Rate limiter for report generation
const reportLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 20, // Limit report generation to prevent resource abuse
  message: {
    status: 'error',
    message: 'Too many report generation requests, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    return req.user ? `reports_${req.user._id}` : req.ip;
  }
});

module.exports = {
  generalLimiter,
  authLimiter,
  scrapingLimiter,
  passwordResetLimiter,
  propertyOwner: propertyOwnerLimiter,
  screening: screeningLimiter,
  propertyManagement: propertyManagementLimiter,
  reports: reportLimiter,
  authenticatedUserLimiter,
};
