const client = require("prom-client");
const observabilityAlerts = require("./observabilityAlerts");

const METRICS_PREFIX = (
  process.env.METRICS_PREFIX || "zakmakelaar_backend"
).replace(/[^a-zA-Z0-9_]/g, "_");

const register = new client.Registry();
register.setDefaultLabels({
  app: "zakmakelaar-backend",
  environment: process.env.NODE_ENV || "development",
});

client.collectDefaultMetrics({
  register,
  prefix: `${METRICS_PREFIX}_`,
});

const httpRequestDuration = new client.Histogram({
  name: `${METRICS_PREFIX}_http_request_duration_seconds`,
  help: "Duration of HTTP requests in seconds",
  labelNames: ["method", "route", "status_code"],
  buckets: [0.05, 0.1, 0.25, 0.5, 1, 2, 5, 10],
  registers: [register],
});

const httpRequestTotal = new client.Counter({
  name: `${METRICS_PREFIX}_http_requests_total`,
  help: "Total number of processed HTTP requests",
  labelNames: ["method", "route", "status_code"],
  registers: [register],
});

const scraperJobsTotal = new client.Counter({
  name: `${METRICS_PREFIX}_scraper_jobs_total`,
  help: "Total scraper jobs processed by site and status",
  labelNames: ["site", "status"],
  registers: [register],
});

const scraperJobDuration = new client.Histogram({
  name: `${METRICS_PREFIX}_scraper_job_duration_seconds`,
  help: "Scraper job duration in seconds",
  labelNames: ["site", "status"],
  buckets: [1, 5, 10, 30, 60, 120, 300, 600],
  registers: [register],
});

const scraperListingsGauge = new client.Gauge({
  name: `${METRICS_PREFIX}_scraper_last_listings_found`,
  help: "Listings found on the most recent scraper run",
  labelNames: ["site"],
  registers: [register],
});

const scraperDegradedGauge = new client.Gauge({
  name: `${METRICS_PREFIX}_scraper_degraded`,
  help: "Scraper degradation indicator (1 = degraded, 0 = healthy)",
  labelNames: ["site"],
  registers: [register],
});

const aiOperationsTotal = new client.Counter({
  name: `${METRICS_PREFIX}_ai_operations_total`,
  help: "Total AI operations executed separated by status",
  labelNames: ["operation", "status"],
  registers: [register],
});

const aiOperationDuration = new client.Histogram({
  name: `${METRICS_PREFIX}_ai_operation_duration_seconds`,
  help: "Duration of AI operations in seconds",
  labelNames: ["operation", "status"],
  buckets: [0.05, 0.1, 0.25, 0.5, 1, 2, 5, 10, 20],
  registers: [register],
});

const aiDegradedGauge = new client.Gauge({
  name: `${METRICS_PREFIX}_ai_degraded`,
  help: "AI degradation indicator (1 = degraded, 0 = healthy)",
  labelNames: ["operation"],
  registers: [register],
});

const scraperAlertCooldownMs =
  parseInt(process.env.OBSERVABILITY_SCRAPER_ALERT_COOLDOWN_MS, 10) || 5 * 60 * 1000;
const aiAlertCooldownMs =
  parseInt(process.env.OBSERVABILITY_AI_ALERT_COOLDOWN_MS, 10) || 5 * 60 * 1000;

const lastScraperAlert = new Map();
const lastAiAlert = new Map();

const sanitizeRoute = (value) => {
  if (!value) return "unknown";
  return value.replace(/[:?].*$/, "").replace(/[^\w/:-]/g, "_");
};

const resolveRoute = (req) => {
  if (req.route && req.route.path) {
    return sanitizeRoute(
      `${req.baseUrl || ""}${req.route.path}`.replace(/\/{2,}/g, "/") || "/"
    );
  }

  if (req.originalUrl) {
    return sanitizeRoute(req.originalUrl.split("?")[0] || req.originalUrl);
  }

  return "unknown";
};

const httpMetricsMiddleware = (req, res, next) => {
  if (req.path === "/metrics") {
    return next();
  }

  const method = (req.method || "GET").toUpperCase();
  const endTimer = httpRequestDuration.startTimer({ method });

  res.on("finish", () => {
    const route = resolveRoute(req);
    const statusCode = String(res.statusCode || 0);

    endTimer({ route, status_code: statusCode });
    httpRequestTotal.inc({ method, route, status_code: statusCode });
  });

  next();
};

const recordScraperResult = async ({
  site = "unknown",
  success = false,
  durationMs = 0,
  listingsFound = 0,
  error = null,
}) => {
  const status = success ? "success" : "failure";
  const durationSeconds = Math.max(0, Number(durationMs || 0) / 1000);

  scraperJobsTotal.inc({ site, status });
  scraperJobDuration.observe({ site, status }, durationSeconds);
  scraperListingsGauge.set({ site }, Number(listingsFound) || 0);

  if (success) {
    scraperDegradedGauge.set({ site }, 0);
  } else {
    scraperDegradedGauge.set({ site }, 1);

    const now = Date.now();
    const lastAlertAt = lastScraperAlert.get(site) || 0;
    if (now - lastAlertAt >= scraperAlertCooldownMs) {
      lastScraperAlert.set(site, now);
      try {
        await observabilityAlerts.notifyScraperDegradation({
          site,
          error: error ? error.message || String(error) : undefined,
        });
      } catch (alertError) {
        // Alerts should not break metric collection; swallow error silently.
      }
    }
  }
};

const recordAiOperation = async ({
  operation = "unknown",
  status = "success",
  durationMs = 0,
  error = null,
}) => {
  const normalizedStatus =
    status && typeof status === "string" ? status.toLowerCase() : "success";
  const durationSeconds = Math.max(0, Number(durationMs || 0) / 1000);

  aiOperationsTotal.inc({ operation, status: normalizedStatus });
  aiOperationDuration.observe({ operation, status: normalizedStatus }, durationSeconds);

  if (normalizedStatus === "error" || normalizedStatus === "degraded") {
    aiDegradedGauge.set({ operation }, 1);

    const now = Date.now();
    const lastAlertAt = lastAiAlert.get(operation) || 0;
    if (now - lastAlertAt >= aiAlertCooldownMs) {
      lastAiAlert.set(operation, now);
      try {
        await observabilityAlerts.notifyAiDegradation({
          operation,
          status: normalizedStatus,
          error: error ? error.message || String(error) : undefined,
        });
      } catch (alertError) {
        // Ignore alert errors to keep metrics flowing.
      }
    }
  } else if (normalizedStatus === "success") {
    aiDegradedGauge.set({ operation }, 0);
  }
};

const authorizeMetricsRequest = (req) => {
  const token = process.env.METRICS_ACCESS_TOKEN;
  if (!token) {
    return true;
  }

  const headerToken =
    req.headers["x-metrics-token"] ||
    req.headers["x-api-key"] ||
    (req.headers.authorization || "").replace("Bearer ", "");

  const queryToken = req.query.token || req.query.access_token;

  return headerToken === token || queryToken === token;
};

const handleMetricsRequest = async (req, res) => {
  if (!authorizeMetricsRequest(req)) {
    return res.status(401).json({
      status: "error",
      message: "Unauthorized metrics access",
    });
  }

  res.setHeader("Content-Type", register.contentType);
  res.send(await register.metrics());
};

module.exports = {
  register,
  httpMetricsMiddleware,
  handleMetricsRequest,
  recordScraperResult,
  recordAiOperation,
};
