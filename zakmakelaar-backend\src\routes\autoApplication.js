const express = require("express");
const router = express.Router();
const { auth } = require("../middleware/auth");
const { handleValidationErrors } = require("../middleware/validation");
const { body, param, query, validationResult } = require("express-validator");
const multer = require("multer");
const path = require("path");
const { loggers } = require("../services/logger");

// Custom validation middleware
const validate = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      status: "error",
      message: "Validation failed",
      errors: errors.array(),
    });
  }
  next();
};

// Initialize services
const autoApplicationService = require("../services/autoApplicationService");
const ApplicationQueueManager = require("../services/applicationQueueManager");
const FormAutomationEngine = require("../services/formAutomationEngine");
const AntiDetectionSystem = require("../services/antiDetectionSystem");
const ApplicationMonitor = require("../services/applicationMonitor");
const ApplicationResult = require("../models/ApplicationResult");
const ApplicationQueue = require("../models/ApplicationQueue");
const AutoApplicationSettings = require("../models/AutoApplicationSettings");
const User = require("../models/User");

const queueManager = new ApplicationQueueManager();
const formEngine = new FormAutomationEngine();
const antiDetectionSystem = new AntiDetectionSystem();
const applicationMonitor = new ApplicationMonitor();

// Initialize queue manager
// DISABLED: ApplicationQueueManager conflicts with AutoApplicationService
// Let AutoApplicationService handle queue processing instead
// queueManager.initialize().catch(error => {
//   console.error('Failed to initialize queue manager:', error);
// });

/**
 * @swagger
 * components:
 *   schemas:
 *     AutoApplicationSettings:
 *       type: object
 *       required:
 *         - userId
 *         - isEnabled
 *       properties:
 *         _id:
 *           type: string
 *           description: Unique identifier for the settings
 *         userId:
 *           type: string
 *           description: User ID who owns these settings
 *         isEnabled:
 *           type: boolean
 *           description: Whether auto-application is enabled
 *           default: false
 *         maxApplicationsPerDay:
 *           type: number
 *           description: Maximum number of applications per day
 *           default: 5
 *           minimum: 1
 *           maximum: 20
 *         delayBetweenApplications:
 *           type: number
 *           description: Delay between applications in minutes
 *           default: 30
 *           minimum: 5
 *           maximum: 1440
 *         targetCriteria:
 *           type: object
 *           properties:
 *             maxPrice:
 *               type: number
 *               description: Maximum price in euros
 *             minRooms:
 *               type: number
 *               description: Minimum number of rooms
 *             maxRooms:
 *               type: number
 *               description: Maximum number of rooms
 *             propertyTypes:
 *               type: array
 *               items:
 *                 type: string
 *                 enum: [apartment, house, studio, room]
 *               description: Preferred property types
 *             locations:
 *               type: array
 *               items:
 *                 type: string
 *               description: Preferred locations/cities
 *             excludeLocations:
 *               type: array
 *               items:
 *                 type: string
 *               description: Locations to exclude
 *         applicationTemplate:
 *           type: object
 *           properties:
 *             personalInfo:
 *               type: object
 *               properties:
 *                 firstName:
 *                   type: string
 *                 lastName:
 *                   type: string
 *                 email:
 *                   type: string
 *                   format: email
 *                 phone:
 *                   type: string
 *                 dateOfBirth:
 *                   type: string
 *                   format: date
 *             employment:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [employed, self-employed, student, unemployed]
 *                 employer:
 *                   type: string
 *                 position:
 *                   type: string
 *                 monthlyIncome:
 *                   type: number
 *             preferences:
 *               type: object
 *               properties:
 *                 moveInDate:
 *                   type: string
 *                   format: date
 *                 leaseDuration:
 *                   type: number
 *                   description: Preferred lease duration in months
 *                 petsAllowed:
 *                   type: boolean
 *                 smokingAllowed:
 *                   type: boolean
 *             customMessage:
 *               type: string
 *               description: Custom message to include in applications
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *
 *     ApplicationQueue:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *         userId:
 *           type: string
 *         listingId:
 *           type: string
 *         listingUrl:
 *           type: string
 *         status:
 *           type: string
 *           enum: [pending, processing, completed, failed, cancelled]
 *         priority:
 *           type: number
 *           minimum: 1
 *           maximum: 10
 *         scheduledAt:
 *           type: string
 *           format: date-time
 *         attempts:
 *           type: number
 *           default: 0
 *         maxAttempts:
 *           type: number
 *           default: 3
 *         lastAttempt:
 *           type: string
 *           format: date-time
 *         errorMessage:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *
 *     ApplicationResult:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *         userId:
 *           type: string
 *         queueId:
 *           type: string
 *         listingId:
 *           type: string
 *         listingUrl:
 *           type: string
 *         status:
 *           type: string
 *           enum: [success, failed, cancelled]
 *         submittedAt:
 *           type: string
 *           format: date-time
 *         responseTime:
 *           type: number
 *           description: Response time in milliseconds
 *         formData:
 *           type: object
 *           description: Data that was submitted in the form
 *         screenshots:
 *           type: array
 *           items:
 *             type: string
 *           description: Base64 encoded screenshots of the application process
 *         errorDetails:
 *           type: object
 *           properties:
 *             message:
 *               type: string
 *             stack:
 *               type: string
 *             captchaDetected:
 *               type: boolean
 *             blockingDetected:
 *               type: boolean
 *         createdAt:
 *           type: string
 *           format: date-time
 *
 *     AutoApplicationStats:
 *       type: object
 *       properties:
 *         totalApplications:
 *           type: number
 *         successfulApplications:
 *           type: number
 *         failedApplications:
 *           type: number
 *         pendingApplications:
 *           type: number
 *         successRate:
 *           type: number
 *           description: Success rate as a percentage
 *         averageResponseTime:
 *           type: number
 *           description: Average response time in milliseconds
 *         applicationsToday:
 *           type: number
 *         applicationsThisWeek:
 *           type: number
 *         applicationsThisMonth:
 *           type: number
 *         lastApplicationDate:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /api/auto-application/settings/{userId}:
 *   get:
 *     summary: Get user's auto-application settings
 *     description: Retrieve the current auto-application settings for a specific user
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID to get settings for
 *     responses:
 *       200:
 *         description: Auto-application settings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   $ref: '#/components/schemas/AutoApplicationSettings'
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *       404:
 *         description: Settings not found for user
 *       500:
 *         description: Internal server error
 */
router.get("/settings/:userId", auth, async (req, res, next) => {
  try {
    // Ensure user can only access their own settings or is admin
    if (req.user.id !== req.params.userId && req.user.role !== "admin") {
      return res.status(403).json({
        status: "error",
        message: "Access denied",
      });
    }

    const settings = await autoApplicationService.getSettings(
      req.params.userId
    );

    res.json({
      status: "success",
      data: settings,
    });
  } catch (error) {
    next(error);
  }
});

// Keep the original route for backward compatibility
router.get("/settings", auth, async (req, res, next) => {
  try {
    const settings = await autoApplicationService.getSettings(req.user._id);

    res.json({
      status: "success",
      data: settings,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/settings/{userId}:
 *   put:
 *     summary: Update auto-application settings for specific user
 *     description: Update auto-application settings for a specific user
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID to update settings for
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               enabled:
 *                 type: boolean
 *                 description: Enable or disable auto-application
 *               settings:
 *                 type: object
 *                 properties:
 *                   maxApplicationsPerDay:
 *                     type: number
 *                     minimum: 1
 *                     maximum: 20
 *                   applicationTemplate:
 *                     type: string
 *                     enum: [professional, casual, student, expat]
 *                   autoSubmit:
 *                     type: boolean
 *                   requireManualReview:
 *                     type: boolean
 *                   notificationPreferences:
 *                     type: object
 *                     properties:
 *                       immediate:
 *                         type: boolean
 *                       daily:
 *                         type: boolean
 *                       weekly:
 *                         type: boolean
 *                   language:
 *                     type: string
 *                     enum: [english, dutch]
 *               criteria:
 *                 type: object
 *                 properties:
 *                   maxPrice:
 *                     type: number
 *                   minRooms:
 *                     type: number
 *                   maxRooms:
 *                     type: number
 *                   propertyTypes:
 *                     type: array
 *                     items:
 *                       type: string
 *                   locations:
 *                     type: array
 *                     items:
 *                       type: string
 *                   excludeKeywords:
 *                     type: array
 *                     items:
 *                       type: string
 *                   includeKeywords:
 *                     type: array
 *                     items:
 *                       type: string
 *                   minSize:
 *                     type: number
 *                   maxSize:
 *                     type: number
 *                   furnished:
 *                     type: boolean
 *                   petsAllowed:
 *                     type: boolean
 *                   smokingAllowed:
 *                     type: boolean
 *               personalInfo:
 *                 type: object
 *                 properties:
 *                   fullName:
 *                     type: string
 *                   email:
 *                     type: string
 *                   phone:
 *                     type: string
 *     responses:
 *       200:
 *         description: Settings updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Auto-application settings updated successfully
 *                 data:
 *                   $ref: '#/components/schemas/AutoApplicationSettings'
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       500:
 *         description: Internal server error
 */
router.put(
  "/settings/:userId",
  auth,
  [
    param("userId").isMongoId().withMessage("Invalid user ID"),
    body("enabled")
      .optional()
      .isBoolean()
      .withMessage("enabled must be a boolean"),
    body("settings.maxApplicationsPerDay")
      .optional()
      .isInt({ min: 1, max: 20 })
      .withMessage("maxApplicationsPerDay must be between 1 and 20"),
    body("personalInfo.email")
      .optional()
      .isEmail()
      .withMessage("Email must be valid"),
  ],
  validate,
  async (req, res, next) => {
    try {
      // Ensure user can only update their own settings or is admin
      if (req.user.id !== req.params.userId && req.user.role !== "admin") {
        return res.status(403).json({
          status: "error",
          message: "Access denied",
        });
      }

      const settings = await autoApplicationService.updateSettings(
        req.params.userId,
        req.body
      );

      res.json({
        status: "success",
        message: "Auto-application settings updated successfully",
        data: settings,
      });
    } catch (error) {
      loggers.app.error("Error in PUT /settings/:userId:", {
        userId: req.params.userId,
        error: error.message,
        stack: error.stack,
      });
      next(error);
    }
  }
);

/**
 * @swagger
 * /api/auto-application/enable:
 *   post:
 *     summary: Enable auto-application
 *     description: Enable auto-application with provided settings
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *             properties:
 *               userId:
 *                 type: string
 *                 description: User ID to enable auto-application for
 *     responses:
 *       200:
 *         description: Auto-application enabled successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   $ref: '#/components/schemas/AutoApplicationSettings'
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /api/auto-application/disable:
 *   post:
 *     summary: Disable auto-application
 *     description: Disable auto-application for a user
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *             properties:
 *               userId:
 *                 type: string
 *                 description: User ID to disable auto-application for
 *     responses:
 *       200:
 *         description: Auto-application disabled successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /api/auto-application/settings:
 *   post:
 *     summary: Create or update auto-application settings (legacy)
 *     description: Create new or update existing auto-application settings for the authenticated user
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - isEnabled
 *             properties:
 *               isEnabled:
 *                 type: boolean
 *                 description: Enable or disable auto-application
 *               maxApplicationsPerDay:
 *                 type: number
 *                 minimum: 1
 *                 maximum: 20
 *                 description: Maximum applications per day
 *               delayBetweenApplications:
 *                 type: number
 *                 minimum: 5
 *                 maximum: 1440
 *                 description: Delay between applications in minutes
 *               targetCriteria:
 *                 type: object
 *                 properties:
 *                   maxPrice:
 *                     type: number
 *                   minRooms:
 *                     type: number
 *                   maxRooms:
 *                     type: number
 *                   propertyTypes:
 *                     type: array
 *                     items:
 *                       type: string
 *                   locations:
 *                     type: array
 *                     items:
 *                       type: string
 *               applicationTemplate:
 *                 type: object
 *                 properties:
 *                   personalInfo:
 *                     type: object
 *                   employment:
 *                     type: object
 *                   preferences:
 *                     type: object
 *                   customMessage:
 *                     type: string
 *     responses:
 *       200:
 *         description: Settings updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Auto-application settings updated successfully
 *                 data:
 *                   $ref: '#/components/schemas/AutoApplicationSettings'
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  "/settings",
  auth,
  [
    body("isEnabled").isBoolean().withMessage("isEnabled must be a boolean"),
    body("maxApplicationsPerDay")
      .optional()
      .isInt({ min: 1, max: 20 })
      .withMessage("maxApplicationsPerDay must be between 1 and 20"),
    body("delayBetweenApplications")
      .optional()
      .isInt({ min: 5, max: 1440 })
      .withMessage(
        "delayBetweenApplications must be between 5 and 1440 minutes"
      ),
    body("targetCriteria.maxPrice")
      .optional()
      .isNumeric()
      .withMessage("maxPrice must be a number"),
    body("applicationTemplate.personalInfo.email")
      .optional()
      .isEmail()
      .withMessage("Email must be valid"),
  ],
  validate,
  async (req, res, next) => {
    try {
      const settings = await autoApplicationService.updateSettings(
        req.user._id,
        req.body
      );

      res.json({
        status: "success",
        message: "Auto-application settings updated successfully",
        data: settings,
      });
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @swagger
 * /api/auto-application/queue/{userId}:
 *   get:
 *     summary: Get user's application queue by userId
 *     description: Retrieve all queued applications for a specific user with optional filtering
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID to get queue for
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, processing, completed, failed, cancelled]
 *         description: Filter by application status
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of results to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: Number of results to skip
 *     responses:
 *       200:
 *         description: Application queue retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ApplicationQueue'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       500:
 *         description: Internal server error
 */
router.get("/queue/:userId", auth, async (req, res, next) => {
  try {
    // Ensure user can only access their own queue or is admin
    if (req.user.id !== req.params.userId && req.user.role !== "admin") {
      return res.status(403).json({
        status: "error",
        message: "Access denied",
      });
    }

    const { status, limit = 20, offset = 0 } = req.query;
    const result = await queueManager.getUserQueue(req.params.userId, {
      status,
      limit: parseInt(limit),
      offset: parseInt(offset),
    });

    res.json({
      status: "success",
      data: result.applications || result || [],
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/queue:
 *   get:
 *     summary: Get user's application queue
 *     description: Retrieve all queued applications for the authenticated user with optional filtering
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, processing, completed, failed, cancelled]
 *         description: Filter by application status
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of results to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: Number of results to skip
 *     responses:
 *       200:
 *         description: Application queue retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     applications:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/ApplicationQueue'
 *                     total:
 *                       type: number
 *                     limit:
 *                       type: number
 *                     offset:
 *                       type: number
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get("/queue", auth, async (req, res, next) => {
  try {
    const { status, limit = 20, offset = 0 } = req.query;
    const result = await queueManager.getUserQueue(req.user.id, {
      status,
      limit: parseInt(limit),
      offset: parseInt(offset),
    });

    res.json({
      status: "success",
      data: result,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/queue:
 *   post:
 *     summary: Add listing to application queue
 *     description: Add a property listing to the user's auto-application queue
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - listingId
 *               - listingUrl
 *             properties:
 *               listingId:
 *                 type: string
 *                 description: Unique identifier for the listing
 *               listingUrl:
 *                 type: string
 *                 format: uri
 *                 description: URL of the property listing
 *               priority:
 *                 type: number
 *                 minimum: 1
 *                 maximum: 10
 *                 default: 5
 *                 description: Priority level for the application
 *               scheduledAt:
 *                 type: string
 *                 format: date-time
 *                 description: When to process this application (optional)
 *     responses:
 *       201:
 *         description: Application added to queue successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Application added to queue successfully
 *                 data:
 *                   $ref: '#/components/schemas/ApplicationQueue'
 *       400:
 *         description: Invalid input data or daily limit exceeded
 *       401:
 *         description: Unauthorized
 *       409:
 *         description: Application already exists in queue
 *       500:
 *         description: Internal server error
 */
router.post(
  "/queue",
  auth,
  [
    body("listingId").notEmpty().withMessage("listingId is required"),
    body("listingUrl").isURL().withMessage("listingUrl must be a valid URL"),
    body("priority")
      .optional()
      .isInt({ min: 1, max: 10 })
      .withMessage("priority must be between 1 and 10"),
  ],
  validate,
  async (req, res, next) => {
    try {
      console.log("Queue route called with user:", req.user.id);
      console.log("Request body:", req.body);

      // Fetch user details from DB
      const user = await User.findById(req.user.id).select(
        "email profile.firstName profile.lastName profile.phoneNumber"
      );

      if (!user) {
        return res.status(404).json({
          status: "error",
          message: "User not found",
        });
      }

      // Build personalInfo from user profile
      const personalInfo = {
        fullName: `${user.profile.firstName} ${user.profile.lastName}`.trim(),
        email: user.email,
        phone: user.profile.phoneNumber,
      };

      // Prepare application data with userId
      const applicationData = {
        userId: req.user.id,
        ...req.body,
        personalInfo,
      };

      console.log("Prepared application data:", applicationData);

      const queueItem = await queueManager.addToQueue(applicationData);

      console.log("Queue item created:", queueItem);

      res.status(201).json({
        status: "success",
        message: "Application added to queue successfully",
        data: queueItem,
      });
    } catch (error) {
      console.error("Queue route error:", error);
      console.error("Error stack:", error.stack);
      next(error);
    }
  }
);

/**
 * @swagger
 * /api/auto-application/queue/{queueId}:
 *   delete:
 *     summary: Remove application from queue
 *     description: Remove a specific application from the user's queue
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: queueId
 *         required: true
 *         schema:
 *           type: string
 *         description: Queue item ID to remove
 *     responses:
 *       200:
 *         description: Application removed from queue successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Application removed from queue successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Queue item not found
 *       500:
 *         description: Internal server error
 */
router.delete(
  "/queue/:queueId",
  auth,
  [param("queueId").isMongoId().withMessage("Invalid queue ID")],
  validate,
  async (req, res, next) => {
    try {
      const result = await queueManager.removeFromQueue(
        req.params.queueId,
        req.user.id
      );

      res.json({
        status: "success",
        message:
          result.message || "Application removed from queue successfully",
        data: result.removedItem,
      });
    } catch (error) {
      // Handle custom statusCode errors
      if (error.statusCode) {
        return res.status(error.statusCode).json({
          status: "error",
          message: error.message,
        });
      }
      next(error);
    }
  }
);

/**
 * @swagger
 * /api/auto-application/results/{userId}:
 *   get:
 *     summary: Get application results for specific user
 *     description: Retrieve results of completed auto-applications for a specific user
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID to get results for
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of results to return
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [success, failed, cancelled]
 *         description: Filter by result status
 *     responses:
 *       200:
 *         description: Application results retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     results:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/ApplicationResult'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: number
 *                         limit:
 *                           type: number
 *                         total:
 *                           type: number
 *                         totalPages:
 *                           type: number
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       500:
 *         description: Internal server error
 */
router.get("/results/:userId", auth, async (req, res, next) => {
  try {
    // Ensure user can only access their own results or is admin
    if (req.user.id !== req.params.userId && req.user.role !== "admin") {
      return res.status(403).json({
        status: "error",
        message: "Access denied",
      });
    }

    const { page = 1, limit = 20, status } = req.query;
    const results = await autoApplicationService.getApplicationResults(
      req.params.userId,
      {
        status,
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit),
      }
    );

    res.json({
      status: "success",
      data: {
        results: results.results || results || [],
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: results.total || 0,
          totalPages: Math.ceil((results.total || 0) / parseInt(limit)),
        },
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/results:
 *   get:
 *     summary: Get application results
 *     description: Retrieve results of completed auto-applications for the authenticated user
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [success, failed, cancelled]
 *         description: Filter by result status
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of results to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: Number of results to skip
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter results from this date
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter results to this date
 *     responses:
 *       200:
 *         description: Application results retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     results:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/ApplicationResult'
 *                     total:
 *                       type: number
 *                     limit:
 *                       type: number
 *                     offset:
 *                       type: number
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get("/results", auth, async (req, res, next) => {
  try {
    const { status, limit = 20, offset = 0, dateFrom, dateTo } = req.query;
    const results = await autoApplicationService.getApplicationResults(
      req.user.id,
      {
        status,
        limit: parseInt(limit),
        offset: parseInt(offset),
        dateFrom,
        dateTo,
      }
    );

    res.json({
      status: "success",
      data: results,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/stats/{userId}:
 *   get:
 *     summary: Get auto-application statistics for specific user
 *     description: Retrieve comprehensive statistics about a specific user's auto-application activity
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID to get statistics for
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   $ref: '#/components/schemas/AutoApplicationStats'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       500:
 *         description: Internal server error
 */
router.get("/stats/:userId", auth, async (req, res, next) => {
  try {
    // Ensure user can only access their own stats or is admin
    if (req.user.id !== req.params.userId && req.user.role !== "admin") {
      return res.status(403).json({
        status: "error",
        message: "Access denied",
      });
    }

    const stats = await autoApplicationService.getApplicationStats(
      req.params.userId
    );

    res.json({
      status: "success",
      data: stats,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/stats:
 *   get:
 *     summary: Get auto-application statistics
 *     description: Retrieve comprehensive statistics about the user's auto-application activity
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   $ref: '#/components/schemas/AutoApplicationStats'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get("/stats", auth, async (req, res, next) => {
  try {
    const stats = await autoApplicationService.getApplicationStats(req.user.id);

    res.json({
      status: "success",
      data: stats,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/process:
 *   post:
 *     summary: Manually trigger application processing
 *     description: Manually trigger the processing of pending applications in the queue
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               queueId:
 *                 type: string
 *                 description: Process specific queue item (optional)
 *               maxApplications:
 *                 type: number
 *                 minimum: 1
 *                 maximum: 10
 *                 default: 5
 *                 description: Maximum number of applications to process
 *     responses:
 *       200:
 *         description: Processing started successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Application processing started
 *                 data:
 *                   type: object
 *                   properties:
 *                     processedCount:
 *                       type: number
 *                     remainingInQueue:
 *                       type: number
 *       401:
 *         description: Unauthorized
 *       429:
 *         description: Daily application limit reached
 *       500:
 *         description: Internal server error
 */
router.post("/process", auth, async (req, res, next) => {
  try {
    const { queueId, maxApplications = 5 } = req.body;
    const result = await autoApplicationService.processApplications(
      req.user.id,
      {
        queueId,
        maxApplications,
      }
    );

    res.json({
      status: "success",
      message: "Application processing started",
      data: result,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/test-form:
 *   post:
 *     summary: Test form automation on a specific listing
 *     description: Test the form automation engine on a specific property listing without actually submitting
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - listingUrl
 *             properties:
 *               listingUrl:
 *                 type: string
 *                 format: uri
 *                 description: URL of the property listing to test
 *               dryRun:
 *                 type: boolean
 *                 default: true
 *                 description: Whether to perform a dry run (no actual submission)
 *     responses:
 *       200:
 *         description: Form test completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Form test completed successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     formDetected:
 *                       type: boolean
 *                     fieldsFound:
 *                       type: array
 *                       items:
 *                         type: string
 *                     fillableFields:
 *                       type: number
 *                     captchaDetected:
 *                       type: boolean
 *                     estimatedSuccessRate:
 *                       type: number
 *                     screenshots:
 *                       type: array
 *                       items:
 *                         type: string
 *                     warnings:
 *                       type: array
 *                       items:
 *                         type: string
 *       400:
 *         description: Invalid URL or test failed
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  "/test-form",
  auth,
  [
    body("listingUrl").isURL().withMessage("listingUrl must be a valid URL"),
    body("dryRun")
      .optional()
      .isBoolean()
      .withMessage("dryRun must be a boolean"),
  ],
  validate,
  async (req, res, next) => {
    try {
      const { listingUrl, dryRun = true } = req.body;
      const testResult = await formEngine.testFormAutomation(listingUrl, {
        dryRun,
        userId: req.user.id,
      });

      res.json({
        status: "success",
        message: "Form test completed successfully",
        data: testResult,
      });
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @swagger
 * /api/auto-application/pause:
 *   post:
 *     summary: Pause auto-application processing
 *     description: Temporarily pause all auto-application processing for the user
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               duration:
 *                 type: number
 *                 description: Pause duration in minutes (optional, default is indefinite)
 *               reason:
 *                 type: string
 *                 description: Reason for pausing (optional)
 *     responses:
 *       200:
 *         description: Auto-application paused successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Auto-application paused successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     pausedUntil:
 *                       type: string
 *                       format: date-time
 *                     reason:
 *                       type: string
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post("/pause", auth, async (req, res, next) => {
  try {
    const { duration, reason } = req.body;
    const result = await autoApplicationService.pauseProcessing(req.user.id, {
      duration,
      reason,
    });

    res.json({
      status: "success",
      message: "Auto-application paused successfully",
      data: result,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/resume:
 *   post:
 *     summary: Resume auto-application processing
 *     description: Resume paused auto-application processing for the user
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Auto-application resumed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Auto-application resumed successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post("/resume", auth, async (req, res, next) => {
  try {
    await autoApplicationService.resumeProcessing(req.user.id);

    res.json({
      status: "success",
      message: "Auto-application resumed successfully",
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/scan-existing:
 *   post:
 *     summary: Manually trigger a retroactive scan of existing listings
 *     description: Initiates a scan of all existing listings in the database against all active users' criteria.
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Scan started successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Retroactive scan started successfully.
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post("/scan-existing", auth, async (req, res, next) => {
  try {
    // Do not await this call, as it can take a long time.
    // The service will run in the background.
    autoApplicationService.scanExistingListingsAndApply();

    res.json({
      status: "success",
      message: "Retroactive scan started successfully.",
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/monitor/success-rates:
 *   get:
 *     summary: Get application success rates and analytics
 *     description: Retrieve detailed success rate analytics for the user's auto-applications
 *     tags: [Auto Application Monitoring]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 365
 *           default: 30
 *         description: Number of days to analyze
 *     responses:
 *       200:
 *         description: Success rates retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     timeframe:
 *                       type: number
 *                     total:
 *                       type: number
 *                     submitted:
 *                       type: number
 *                     successful:
 *                       type: number
 *                     submissionRate:
 *                       type: number
 *                     successRate:
 *                       type: number
 *                     responseRate:
 *                       type: number
 *                     acceptanceRate:
 *                       type: number
 *                     avgProcessingTime:
 *                       type: number
 *                     errorRate:
 *                       type: number
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get("/monitor/success-rates", auth, async (req, res, next) => {
  try {
    const { timeframe = 30 } = req.query;
    const successRates = await applicationMonitor.getSuccessRates(
      req.user.id,
      parseInt(timeframe)
    );

    res.json({
      status: "success",
      data: successRates,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/monitor/patterns:
 *   get:
 *     summary: Get application pattern analysis
 *     description: Retrieve pattern analysis to identify successful application strategies
 *     tags: [Auto Application Monitoring]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 365
 *           default: 30
 *         description: Number of days to analyze
 *     responses:
 *       200:
 *         description: Pattern analysis retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     timeframe:
 *                       type: number
 *                     timePatterns:
 *                       type: object
 *                     locationPatterns:
 *                       type: array
 *                     propertyTypePatterns:
 *                       type: array
 *                     recommendations:
 *                       type: array
 *                     confidence:
 *                       type: string
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get("/monitor/patterns", auth, async (req, res, next) => {
  try {
    const { timeframe = 30 } = req.query;
    const patterns = await applicationMonitor.detectPatterns(
      req.user.id,
      parseInt(timeframe)
    );

    res.json({
      status: "success",
      data: patterns,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/monitor/performance:
 *   get:
 *     summary: Get performance metrics
 *     description: Retrieve detailed performance metrics for auto-application system
 *     tags: [Auto Application Monitoring]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 365
 *           default: 30
 *         description: Number of days to analyze
 *     responses:
 *       200:
 *         description: Performance metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     timeframe:
 *                       type: number
 *                     totalApplications:
 *                       type: number
 *                     avgProcessingTime:
 *                       type: number
 *                     avgFormDetectionTime:
 *                       type: number
 *                     avgFormFillingTime:
 *                       type: number
 *                     complexFormRate:
 *                       type: number
 *                     captchaRate:
 *                       type: number
 *                     blockingRate:
 *                       type: number
 *                     processingGrade:
 *                       type: string
 *                     reliabilityGrade:
 *                       type: string
 *                     efficiencyGrade:
 *                       type: string
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get("/monitor/performance", auth, async (req, res, next) => {
  try {
    const { timeframe = 30 } = req.query;
    const performance = await applicationMonitor.getPerformanceMetrics(
      req.user.id,
      parseInt(timeframe)
    );

    res.json({
      status: "success",
      data: performance,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/monitor/errors:
 *   get:
 *     summary: Get error analysis
 *     description: Retrieve detailed error analysis and recommendations
 *     tags: [Auto Application Monitoring]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 365
 *           default: 7
 *         description: Number of days to analyze
 *     responses:
 *       200:
 *         description: Error analysis retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     timeframe:
 *                       type: number
 *                     totalErrors:
 *                       type: number
 *                     commonErrors:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           category:
 *                             type: string
 *                           count:
 *                             type: number
 *                           percentage:
 *                             type: number
 *                           retryablePercentage:
 *                             type: number
 *                           commonMessages:
 *                             type: array
 *                             items:
 *                               type: string
 *                     recommendations:
 *                       type: array
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get("/monitor/errors", auth, async (req, res, next) => {
  try {
    const { timeframe = 7 } = req.query;
    const errors = await applicationMonitor.getErrorAnalysis(
      req.user.id,
      parseInt(timeframe)
    );

    res.json({
      status: "success",
      data: errors,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/monitor/reports:
 *   get:
 *     summary: Generate comprehensive monitoring report
 *     description: Generate a comprehensive report combining all monitoring data
 *     tags: [Auto Application Monitoring]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: reportType
 *         schema:
 *           type: string
 *           enum: [daily, weekly, monthly]
 *           default: weekly
 *         description: Type of report to generate
 *     responses:
 *       200:
 *         description: Report generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     reportType:
 *                       type: string
 *                     timeframe:
 *                       type: number
 *                     generatedAt:
 *                       type: string
 *                       format: date-time
 *                     summary:
 *                       type: object
 *                       properties:
 *                         totalApplications:
 *                           type: number
 *                         successRate:
 *                           type: number
 *                         acceptanceRate:
 *                           type: number
 *                         avgProcessingTime:
 *                           type: number
 *                         topPerformingLocation:
 *                           type: string
 *                         mostCommonError:
 *                           type: string
 *                     successRates:
 *                       type: object
 *                     patterns:
 *                       type: object
 *                     performance:
 *                       type: object
 *                     errors:
 *                       type: object
 *                     insights:
 *                       type: array
 *                     actionItems:
 *                       type: array
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get("/monitor/reports", auth, async (req, res, next) => {
  try {
    const { reportType = "weekly" } = req.query;
    const report = await applicationMonitor.generateReports(
      req.user.id,
      reportType
    );

    res.json({
      status: "success",
      data: report,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/monitor/track:
 *   post:
 *     summary: Track application status change
 *     description: Manually track an application status change (primarily for internal use)
 *     tags: [Auto Application Monitoring]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - applicationId
 *               - status
 *             properties:
 *               applicationId:
 *                 type: string
 *                 description: Application result ID
 *               status:
 *                 type: string
 *                 enum: [submitted, failed, blocked, captcha_required]
 *                 description: New status
 *               metadata:
 *                 type: object
 *                 description: Additional metadata
 *     responses:
 *       200:
 *         description: Application tracked successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Application tracked successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Application not found
 *       500:
 *         description: Internal server error
 */
router.post(
  "/monitor/track",
  auth,
  [
    body("applicationId").isMongoId().withMessage("Invalid application ID"),
    body("status")
      .isIn(["submitted", "failed", "blocked", "captcha_required"])
      .withMessage("Invalid status"),
    body("metadata")
      .optional()
      .isObject()
      .withMessage("Metadata must be an object"),
  ],
  validate,
  async (req, res, next) => {
    try {
      const { applicationId, status, metadata = {} } = req.body;
      await applicationMonitor.trackApplication(
        applicationId,
        status,
        metadata
      );

      res.json({
        status: "success",
        message: "Application tracked successfully",
      });
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @swagger
 * /api/auto-application/monitor/dashboard:
 *   get:
 *     summary: Get dashboard data
 *     description: Get comprehensive dashboard data for monitoring interface
 *     tags: [Auto Application Monitoring]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     overview:
 *                       type: object
 *                       properties:
 *                         totalApplications:
 *                           type: number
 *                         successRate:
 *                           type: number
 *                         applicationsToday:
 *                           type: number
 *                         applicationsThisWeek:
 *                           type: number
 *                     recentActivity:
 *                       type: array
 *                     topRecommendations:
 *                       type: array
 *                     systemHealth:
 *                       type: object
 *                     alerts:
 *                       type: array
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get("/monitor/dashboard", auth, async (req, res, next) => {
  try {
    // Get comprehensive dashboard data
    const [
      weeklySuccessRates,
      dailySuccessRates,
      patterns,
      performance,
      errors,
    ] = await Promise.all([
      applicationMonitor.getSuccessRates(req.user.id, 7),
      applicationMonitor.getSuccessRates(req.user.id, 1),
      applicationMonitor.detectPatterns(req.user.id, 7),
      applicationMonitor.getPerformanceMetrics(req.user.id, 7),
      applicationMonitor.getErrorAnalysis(req.user.id, 7),
    ]);

    const dashboardData = {
      overview: {
        totalApplications: weeklySuccessRates.total,
        successRate: weeklySuccessRates.successRate,
        acceptanceRate: weeklySuccessRates.acceptanceRate,
        applicationsToday: dailySuccessRates.total,
        applicationsThisWeek: weeklySuccessRates.total,
        avgProcessingTime: performance.avgProcessingTime,
        processingGrade: performance.processingGrade,
        reliabilityGrade: performance.reliabilityGrade,
      },

      recentActivity: {
        submitted: weeklySuccessRates.submitted,
        successful: weeklySuccessRates.successful,
        failed: weeklySuccessRates.failed,
        blocked: weeklySuccessRates.blocked,
        captchaRequired: weeklySuccessRates.captchaRequired,
      },

      topRecommendations: patterns.recommendations.slice(0, 3),

      systemHealth: {
        errorRate: weeklySuccessRates.errorRate,
        blockingRate: weeklySuccessRates.blockingRate,
        captchaRate: weeklySuccessRates.captchaRate,
        avgProcessingTime: performance.avgProcessingTime,
        status:
          performance.processingGrade === "A" ||
          performance.processingGrade === "B"
            ? "healthy"
            : "warning",
      },

      alerts: [
        ...(weeklySuccessRates.successRate < 50
          ? [
              {
                type: "warning",
                message:
                  "Success rate is below 50%. Consider reviewing your application strategy.",
                priority: "high",
              },
            ]
          : []),
        ...(performance.blockingRate > 10
          ? [
              {
                type: "error",
                message:
                  "High blocking rate detected. Review anti-detection measures.",
                priority: "high",
              },
            ]
          : []),
        ...(errors.totalErrors > 10
          ? [
              {
                type: "info",
                message: `${
                  errors.totalErrors
                } errors in the last 7 days. Most common: ${
                  errors.commonErrors[0]?.category || "unknown"
                }`,
                priority: "medium",
              },
            ]
          : []),
      ],

      charts: {
        successTrend: {
          timeframe: 7,
          successRate: weeklySuccessRates.successRate,
          submissionRate: weeklySuccessRates.submissionRate,
          responseRate: weeklySuccessRates.responseRate,
        },
        errorDistribution: errors.commonErrors.map((error) => ({
          category: error.category,
          count: error.count,
          percentage: error.percentage,
        })),
        performanceMetrics: {
          avgProcessingTime: performance.avgProcessingTime,
          avgFormDetectionTime: performance.avgFormDetectionTime,
          avgFormFillingTime: performance.avgFormFillingTime,
          avgSubmissionTime: performance.avgSubmissionTime,
        },
      },
    };

    res.json({
      status: "success",
      data: dashboardData,
    });
  } catch (error) {
    next(error);
  }
});

// Document upload configuration for auto-application
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 5, // Maximum 5 files at once
  },
  fileFilter: (req, file, cb) => {
    // Allow common document formats
    const allowedTypes = [
      "application/pdf",
      "image/jpeg",
      "image/png",
      "image/jpg",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(
        new Error(
          "Invalid file type. Only PDF, DOC, DOCX, and image files are allowed."
        ),
        false
      );
    }
  },
});

/**
 * @swagger
 * /api/auto-application/enable:
 *   post:
 *     summary: Enable auto-application for user
 *     description: Enable auto-application functionality for the authenticated user
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Auto-application enabled successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Auto-application enabled successfully
 *                 data:
 *                   $ref: '#/components/schemas/AutoApplicationSettings'
 *       400:
 *         description: Profile not ready for auto-application
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post("/enable", auth, async (req, res, next) => {
  try {
    console.log("Enable endpoint called for user:", req.user._id);

    const AutoApplicationSettings = require("../models/AutoApplicationSettings");
    const userId = req.user._id.toString();

    // Find existing settings using direct query
    const existingSettings = await AutoApplicationSettings.findOne({
      userId: userId,
    });

    if (!existingSettings) {
      // Create new settings with complete default values
      const newSettings = new AutoApplicationSettings({
        userId: userId,
        enabled: true,
        settings: {
          maxApplicationsPerDay: 5,
          applicationTemplate: "professional",
          autoSubmit: false,
          requireManualReview: true,
          notificationPreferences: {
            immediate: true,
            daily: true,
            weekly: false,
          },
          language: "english",
        },
        criteria: {
          maxPrice: 2000,
          minRooms: 1,
          maxRooms: 5,
          propertyTypes: ["apartment"],
          locations: [],
          excludeKeywords: [],
          includeKeywords: [],
          minSize: 0,
          maxSize: 1000,
          furnished: false,
          petsAllowed: false,
          smokingAllowed: false,
        },
        personalInfo: {
          fullName: "To be filled",
          email: "<EMAIL>",
          phone: "+31000000000",
          dateOfBirth: new Date("1990-01-01"),
          nationality: "To be filled",
          occupation: "To be filled",
          employer: "To be filled",
          monthlyIncome: 0,
          moveInDate: new Date(),
          leaseDuration: 12,
          numberOfOccupants: 1,
          hasGuarantor: false,
          guarantorInfo: {
            name: "To be filled",
            email: "<EMAIL>",
            phone: "+31000000000",
            relationship: "To be filled",
            monthlyIncome: 0,
          },
          emergencyContact: {
            name: "To be filled",
            phone: "+31000000000",
            email: "<EMAIL>",
            relationship: "To be filled",
          },
        },
        documents: [],
        statistics: {
          totalApplications: 0,
          successfulApplications: 0,
          pendingApplications: 0,
          rejectedApplications: 0,
          averageResponseTime: 0,
        },
        status: {
          isActive: true,
          currentQueue: 0,
          dailyApplicationsUsed: 0,
          weeklyApplicationsUsed: 0,
          monthlyApplicationsUsed: 0,
          lastResetDate: new Date(),
        },
      });

      const savedSettings = await newSettings.save();

      res.json({
        status: "success",
        message: "Auto-application enabled successfully",
        data: savedSettings,
      });
    } else {
      // Update existing settings
      existingSettings.enabled = true;
      existingSettings.updatedAt = new Date();
      const updatedSettings = await existingSettings.save();

      res.json({
        status: "success",
        message: "Auto-application enabled successfully",
        data: updatedSettings,
      });
    }
  } catch (error) {
    console.error("Enable endpoint error:", error);

    res.status(500).json({
      status: "error",
      message: error.message,
      error:
        process.env.NODE_ENV === "development"
          ? error.stack
          : "Internal server error",
    });
  }
});

/**
 * @swagger
 * /api/auto-application/disable:
 *   post:
 *     summary: Disable auto-application for user
 *     description: Disable auto-application functionality for the authenticated user
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Auto-application disabled successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Auto-application disabled successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post("/disable", auth, async (req, res, next) => {
  try {
    console.log("Disable endpoint called for user:", req.user._id);
    await autoApplicationService.disableAutoApplication(req.user._id);
    console.log("Disable completed successfully");

    res.json({
      status: "success",
      message: "Auto-application disabled successfully",
    });
  } catch (error) {
    console.error("Disable endpoint error:", error);
    console.error("Error stack:", error.stack);
    console.error("Error name:", error.name);
    console.error("Error message:", error.message);
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/history:
 *   get:
 *     summary: Get application history with detailed status
 *     description: Retrieve comprehensive application history with status tracking
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [submitted, failed, blocked, captcha_required, pending, processing]
 *         description: Filter by application status
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of results to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: Number of results to skip
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter applications from this date
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter applications to this date
 *     responses:
 *       200:
 *         description: Application history retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     applications:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/ApplicationResult'
 *                           - type: object
 *                             properties:
 *                               property:
 *                                 type: object
 *                                 properties:
 *                                   title:
 *                                     type: string
 *                                   location:
 *                                     type: string
 *                                   price:
 *                                     type: string
 *                               timeline:
 *                                 type: array
 *                                 items:
 *                                   type: object
 *                                   properties:
 *                                     status:
 *                                       type: string
 *                                     timestamp:
 *                                       type: string
 *                                       format: date-time
 *                                     message:
 *                                       type: string
 *                     total:
 *                       type: number
 *                     limit:
 *                       type: number
 *                     offset:
 *                       type: number
 *                     summary:
 *                       type: object
 *                       properties:
 *                         totalApplications:
 *                           type: number
 *                         successfulApplications:
 *                           type: number
 *                         failedApplications:
 *                           type: number
 *                         pendingApplications:
 *                           type: number
 *                         successRate:
 *                           type: number
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get("/history", auth, async (req, res, next) => {
  try {
    const { status, limit = 20, offset = 0, dateFrom, dateTo } = req.query;
    const history = await autoApplicationService.getApplicationHistory(
      req.user.id,
      {
        status,
        limit: parseInt(limit),
        offset: parseInt(offset),
        dateFrom,
        dateTo,
      }
    );

    res.json({
      status: "success",
      data: history,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/documents/upload:
 *   post:
 *     summary: Upload required documents for auto-application
 *     description: Upload documents required for auto-application process
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               documents:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *               documentType:
 *                 type: string
 *                 enum: [income_proof, employment_contract, bank_statement, id_document, rental_reference]
 *                 description: Type of document being uploaded
 *               expiryDate:
 *                 type: string
 *                 format: date
 *                 description: Document expiry date (if applicable)
 *     responses:
 *       201:
 *         description: Documents uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Documents uploaded successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     uploadedDocuments:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           documentId:
 *                             type: string
 *                           filename:
 *                             type: string
 *                           type:
 *                             type: string
 *                           size:
 *                             type: number
 *                           uploadedAt:
 *                             type: string
 *                             format: date-time
 *                     profileCompleteness:
 *                       type: number
 *                       description: Updated profile completeness percentage
 *       400:
 *         description: Invalid file or parameters
 *       401:
 *         description: Unauthorized
 *       413:
 *         description: File too large
 *       500:
 *         description: Internal server error
 */
router.post(
  "/documents/upload",
  auth,
  upload.array("documents", 5),
  [
    body("documentType")
      .isIn([
        "income_proof",
        "employment_contract",
        "bank_statement",
        "id_document",
        "rental_reference",
      ])
      .withMessage("Invalid document type"),
    body("expiryDate")
      .optional()
      .isISO8601()
      .withMessage("Invalid expiry date format"),
  ],
  validate,
  async (req, res, next) => {
    try {
      const { documentType, expiryDate } = req.body;
      const files = req.files;

      if (!files || files.length === 0) {
        return res.status(400).json({
          status: "error",
          message: "No files uploaded",
        });
      }

      const result = await autoApplicationService.uploadDocuments(req.user.id, {
        files,
        documentType,
        expiryDate,
      });

      res.status(201).json({
        status: "success",
        message: "Documents uploaded successfully",
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @swagger
 * /api/auto-application/documents:
 *   get:
 *     summary: Get user's auto-application documents
 *     description: Retrieve all documents uploaded for auto-application
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [income_proof, employment_contract, bank_statement, id_document, rental_reference]
 *         description: Filter by document type
 *     responses:
 *       200:
 *         description: Documents retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     documents:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           documentId:
 *                             type: string
 *                           filename:
 *                             type: string
 *                           type:
 *                             type: string
 *                           size:
 *                             type: number
 *                           uploadedAt:
 *                             type: string
 *                             format: date-time
 *                           expiryDate:
 *                             type: string
 *                             format: date
 *                           verified:
 *                             type: boolean
 *                     requiredDocuments:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           type:
 *                             type: string
 *                           required:
 *                             type: boolean
 *                           uploaded:
 *                             type: boolean
 *                           description:
 *                             type: string
 *                     completeness:
 *                       type: number
 *                       description: Document completeness percentage
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get("/documents", auth, async (req, res, next) => {
  try {
    const { type } = req.query;
    const documents = await autoApplicationService.getDocuments(
      req.user.id,
      type
    );

    res.json({
      status: "success",
      data: documents,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/documents/{documentId}:
 *   delete:
 *     summary: Delete an auto-application document
 *     description: Delete a specific document from auto-application profile
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: documentId
 *         required: true
 *         schema:
 *           type: string
 *         description: Document ID to delete
 *     responses:
 *       200:
 *         description: Document deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Document deleted successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     profileCompleteness:
 *                       type: number
 *                       description: Updated profile completeness percentage
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Document not found
 *       500:
 *         description: Internal server error
 */
router.delete(
  "/documents/:documentId",
  auth,
  [param("documentId").isMongoId().withMessage("Invalid document ID")],
  validate,
  async (req, res, next) => {
    try {
      const result = await autoApplicationService.deleteDocument(
        req.user.id,
        req.params.documentId
      );

      res.json({
        status: "success",
        message: "Document deleted successfully",
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @swagger
 * /api/auto-application/status:
 *   get:
 *     summary: Get real-time auto-application status
 *     description: Get current status of auto-application system for the user
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     isEnabled:
 *                       type: boolean
 *                     isPaused:
 *                       type: boolean
 *                     pausedUntil:
 *                       type: string
 *                       format: date-time
 *                     currentQueue:
 *                       type: object
 *                       properties:
 *                         pending:
 *                           type: number
 *                         processing:
 *                           type: number
 *                         nextScheduled:
 *                           type: string
 *                           format: date-time
 *                     todaysActivity:
 *                       type: object
 *                       properties:
 *                         applicationsSubmitted:
 *                           type: number
 *                         applicationsRemaining:
 *                           type: number
 *                         dailyLimit:
 *                           type: number
 *                         resetTime:
 *                           type: string
 *                           format: date-time
 *                     systemHealth:
 *                       type: object
 *                       properties:
 *                         status:
 *                           type: string
 *                           enum: [healthy, warning, error]
 *                         lastSuccessfulApplication:
 *                           type: string
 *                           format: date-time
 *                         recentErrors:
 *                           type: number
 *                         blockingDetected:
 *                           type: boolean
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get("/status", auth, async (req, res, next) => {
  try {
    const status = await autoApplicationService.getStatus(req.user._id);

    res.json({
      status: "success",
      data: status,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/queue/{queueId}/priority:
 *   put:
 *     summary: Update queue item priority
 *     description: Update the priority of a specific queue item
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: queueId
 *         required: true
 *         schema:
 *           type: string
 *         description: Queue item ID to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - priority
 *             properties:
 *               priority:
 *                 type: number
 *                 minimum: 1
 *                 maximum: 10
 *                 description: New priority level
 *     responses:
 *       200:
 *         description: Priority updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   $ref: '#/components/schemas/ApplicationQueue'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Queue item not found
 *       500:
 *         description: Internal server error
 */
router.put(
  "/queue/:queueId/priority",
  auth,
  [
    param("queueId").isMongoId().withMessage("Invalid queue ID"),
    body("priority")
      .isInt({ min: 1, max: 10 })
      .withMessage("Priority must be between 1 and 10"),
  ],
  validate,
  async (req, res, next) => {
    try {
      const updatedItem = await queueManager.updatePriority(
        req.params.queueId,
        req.body.priority,
        req.user.id
      );

      res.json({
        status: "success",
        data: updatedItem,
      });
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @swagger
 * /api/auto-application/queue/{userId}/pause:
 *   post:
 *     summary: Pause user's application queue
 *     description: Pause all queue processing for a specific user
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID to pause queue for
 *     responses:
 *       200:
 *         description: Queue paused successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       500:
 *         description: Internal server error
 */
router.post("/queue/:userId/pause", auth, async (req, res, next) => {
  try {
    const userId = req.params.userId;

    // Validate userId format
    if (!userId || typeof userId !== "string") {
      return res.status(400).json({
        status: "error",
        message: "Invalid user ID format",
      });
    }

    // Ensure user can only pause their own queue or is admin
    if (req.user.id !== userId && req.user.role !== "admin") {
      return res.status(403).json({
        status: "error",
        message: "Access denied",
      });
    }

    console.log(`Attempting to pause queue for user: ${userId}`);
    console.log(`Request user ID: ${req.user.id}, Target user ID: ${userId}`);

    // Check if queueManager exists and has the pauseQueue method
    if (!queueManager || typeof queueManager.pauseQueue !== "function") {
      console.error("QueueManager or pauseQueue method not available");
      return res.status(500).json({
        status: "error",
        message: "Queue management service not available",
      });
    }

    // Call the pauseQueue method with proper error handling
    try {
      await queueManager.pauseQueue(userId);
      console.log(`Successfully paused queue for user: ${userId}`);
    } catch (queueError) {
      console.error("Queue manager error:", queueError);
      throw new Error(`Queue operation failed: ${queueError.message}`);
    }

    res.json({
      status: "success",
      message: "Queue paused successfully",
      data: { success: true },
    });
  } catch (error) {
    console.error("Error in pause queue route:", error);
    res.status(500).json({
      status: "error",
      message: error.message || "Failed to pause queue",
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
    });
  }
});

/**
 * @swagger
 * /api/auto-application/queue/{userId}/resume:
 *   post:
 *     summary: Resume user's application queue
 *     description: Resume queue processing for a specific user
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID to resume queue for
 *     responses:
 *       200:
 *         description: Queue resumed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       500:
 *         description: Internal server error
 */
// Debug endpoint to test queue manager
router.get("/queue/debug/:userId", auth, async (req, res) => {
  try {
    const userId = req.params.userId;

    // Check if user can access this debug info
    if (req.user.id !== userId && req.user.role !== "admin") {
      return res.status(403).json({
        status: "error",
        message: "Access denied",
      });
    }

    const debugInfo = {
      queueManagerExists: !!queueManager,
      pauseQueueMethod: typeof queueManager?.pauseQueue,
      resumeQueueMethod: typeof queueManager?.resumeQueue,
      userId: userId,
      userIdType: typeof userId,
      timestamp: new Date().toISOString(),
    };

    // Try to get queue items for this user
    try {
      const ApplicationQueue = require("../models/ApplicationQueue");
      const queueItems = await ApplicationQueue.find({ userId }).limit(5);
      debugInfo.queueItemsCount = queueItems.length;
      debugInfo.queueItems = queueItems.map((item) => ({
        id: item._id,
        status: item.status,
        listingId: item.listingId,
      }));
    } catch (dbError) {
      debugInfo.databaseError = dbError.message;
    }

    res.json({
      status: "success",
      data: debugInfo,
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      message: error.message,
      stack: error.stack,
    });
  }
});

router.post("/queue/:userId/resume", auth, async (req, res, next) => {
  try {
    const userId = req.params.userId;

    // Validate userId format
    if (!userId || typeof userId !== "string") {
      return res.status(400).json({
        status: "error",
        message: "Invalid user ID format",
      });
    }

    // Ensure user can only resume their own queue or is admin
    if (req.user.id !== userId && req.user.role !== "admin") {
      return res.status(403).json({
        status: "error",
        message: "Access denied",
      });
    }

    console.log(`Attempting to resume queue for user: ${userId}`);
    console.log(`Request user ID: ${req.user.id}, Target user ID: ${userId}`);

    // Check if queueManager exists and has the resumeQueue method
    if (!queueManager || typeof queueManager.resumeQueue !== "function") {
      console.error("QueueManager or resumeQueue method not available");
      return res.status(500).json({
        status: "error",
        message: "Queue management service not available",
      });
    }

    // Call the resumeQueue method with proper error handling
    try {
      await queueManager.resumeQueue(userId);
      console.log(`Successfully resumed queue for user: ${userId}`);
    } catch (queueError) {
      console.error("Queue manager error:", queueError);
      throw new Error(`Queue operation failed: ${queueError.message}`);
    }

    res.json({
      status: "success",
      message: "Queue resumed successfully",
      data: { success: true },
    });
  } catch (error) {
    console.error("Error in resume queue route:", error);
    res.status(500).json({
      status: "error",
      message: error.message || "Failed to resume queue",
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
    });
  }
});

/**
 * @swagger
 * /api/auto-application/scraper/stats:
 *   get:
 *     summary: Get scraper integration statistics
 *     description: Retrieve statistics about the scraper integration system
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Scraper integration statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     processedListingsCount:
 *                       type: number
 *                     qualityScoreCacheSize:
 *                       type: number
 *                     currentlyProcessingCount:
 *                       type: number
 *                     cacheExpiryMs:
 *                       type: number
 *                     minQualityScore:
 *                       type: number
 *                     autoApplicationsTriggered:
 *                       type: number
 *                     duplicatesSkipped:
 *                       type: number
 *                     lastProcessingTime:
 *                       type: string
 *                       format: date-time
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get("/scraper/stats", auth, async (req, res, next) => {
  try {
    // Import the scraper integration service
    const scraperAutoApplicationIntegration = require("../services/scraperAutoApplicationIntegration");
    const stats = scraperAutoApplicationIntegration.getStatistics();

    res.json({
      status: "success",
      data: {
        processedListingsCount: stats.processedListingsCount || 0,
        qualityScoreCacheSize: stats.qualityScoreCacheSize || 0,
        currentlyProcessingCount: stats.currentlyProcessingCount || 0,
        cacheExpiryMs: stats.cacheExpiryMs || 86400000,
        minQualityScore: stats.minQualityScore || 0.6,
        autoApplicationsTriggered: stats.autoApplicationsTriggered || 0,
        duplicatesSkipped: stats.duplicatesSkipped || 0,
        lastProcessingTime: stats.lastProcessingTime || null,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/results/detail/{resultId}:
 *   get:
 *     summary: Get detailed application result
 *     description: Retrieve detailed information about a specific application result
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: resultId
 *         required: true
 *         schema:
 *           type: string
 *         description: Application result ID
 *     responses:
 *       200:
 *         description: Application result retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   $ref: '#/components/schemas/ApplicationResult'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Result not found
 *       500:
 *         description: Internal server error
 */
router.get(
  "/results/detail/:resultId",
  auth,
  [param("resultId").isMongoId().withMessage("Invalid result ID")],
  validate,
  async (req, res, next) => {
    try {
      const result = await autoApplicationService.getApplicationResult(
        req.params.resultId,
        req.user.id
      );

      res.json({
        status: "success",
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @swagger
 * /api/auto-application/health:
 *   get:
 *     summary: Get system health status
 *     description: Retrieve health status of the auto-application system
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: System health retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     status:
 *                       type: string
 *                       enum: [healthy, degraded, unhealthy]
 *                     components:
 *                       type: object
 *                       properties:
 *                         queue:
 *                           type: string
 *                           enum: [operational, degraded, down]
 *                         scraper:
 *                           type: string
 *                           enum: [operational, degraded, down]
 *                         browser:
 *                           type: string
 *                           enum: [operational, degraded, down]
 *                         database:
 *                           type: string
 *                           enum: [operational, degraded, down]
 *                     metrics:
 *                       type: object
 *                       properties:
 *                         queueSize:
 *                           type: number
 *                         processingRate:
 *                           type: number
 *                         errorRate:
 *                           type: number
 *                         averageProcessingTime:
 *                           type: number
 *                     lastHealthCheck:
 *                       type: string
 *                       format: date-time
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get("/health", auth, async (req, res, next) => {
  try {
    const health = await autoApplicationService.getSystemHealth();

    res.json({
      status: "success",
      data: health,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/results/{userId}:
 *   get:
 *     summary: Get application results for specific user
 *     description: Retrieve results of completed auto-applications for a specific user
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 */
router.get("/results/:userId", auth, async (req, res, next) => {
  try {
    // Ensure user can only access their own results or is admin
    if (req.user.id !== req.params.userId && req.user.role !== "admin") {
      return res.status(403).json({
        status: "error",
        message: "Access denied",
      });
    }

    const { page = 1, limit = 20, status } = req.query;

    // Get ApplicationResult model
    const ApplicationResult = require("../models/ApplicationResult");

    // Build query
    const query = { userId: req.params.userId };
    if (status) query.status = status;

    // Get results with pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const results = await ApplicationResult.find(query)
      .sort({ submittedAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate("listingId", "title location price")
      .lean();

    // Get total count
    const total = await ApplicationResult.countDocuments(query);

    // Format results for frontend - ensure all required fields exist
    const formattedResults = results.map((result) => ({
      ...result,
      listingTitle:
        result.listingSnapshot?.title ||
        result.listingId?.title ||
        "Unknown Property",
      responseTime: result.response?.responseTime || 2000,
      // Ensure metadata exists and has qualityScore
      metadata: {
        ...result.metadata,
        qualityScore: result.metadata?.qualityScore || 0.85,
        processingTime:
          result.metadata?.processingTime ||
          result.metrics?.processingTime ||
          2000,
      },
      // Ensure metrics exists
      metrics: {
        ...result.metrics,
        successProbability: result.metrics?.successProbability || 85,
      },
    }));

    res.json({
      status: "success",
      data: {
        results: formattedResults,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / parseInt(limit)),
        },
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/process-existing:
 *   post:
 *     summary: Process existing listings for auto-application
 *     description: Process existing listings in the database and create auto-applications for matching criteria
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *                 description: Optional specific user ID (admin only)
 *               limit:
 *                 type: number
 *                 description: Maximum number of listings to process
 *                 default: 100
 *                 minimum: 1
 *                 maximum: 500
 *               daysBack:
 *                 type: number
 *                 description: How many days back to look for listings
 *                 default: 30
 *                 minimum: 1
 *                 maximum: 90
 *               skipRecentlyProcessed:
 *                 type: boolean
 *                 description: Skip listings that were processed in the last 6 hours
 *                 default: true
 *               minQualityScore:
 *                 type: number
 *                 description: Minimum quality score for listings to be processed
 *                 default: 0.6
 *                 minimum: 0.1
 *                 maximum: 1.0
 *     responses:
 *       200:
 *         description: Processing completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Existing listings processed successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     processed:
 *                       type: number
 *                       description: Total listings processed
 *                     applicationsCreated:
 *                       type: number
 *                       description: Total applications created
 *                     usersProcessed:
 *                       type: number
 *                       description: Number of users processed
 *                     listingsFound:
 *                       type: number
 *                       description: Total eligible listings found
 *                     userResults:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           userId:
 *                             type: string
 *                           applicationsCreated:
 *                             type: number
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       500:
 *         description: Internal server error
 */
router.post(
  "/process-existing",
  auth,
  [
    body("userId").optional().isMongoId().withMessage("Invalid user ID format"),
    body("limit")
      .optional()
      .isInt({ min: 1, max: 500 })
      .withMessage("Limit must be between 1 and 500"),
    body("daysBack")
      .optional()
      .isInt({ min: 1, max: 90 })
      .withMessage("DaysBack must be between 1 and 90"),
    body("skipRecentlyProcessed")
      .optional()
      .isBoolean()
      .withMessage("skipRecentlyProcessed must be a boolean"),
    body("minQualityScore")
      .optional()
      .isFloat({ min: 0.1, max: 1.0 })
      .withMessage("minQualityScore must be between 0.1 and 1.0"),
    validate,
  ],
  async (req, res, next) => {
    try {
      const {
        userId,
        limit = 100,
        daysBack = 30,
        skipRecentlyProcessed = true,
        minQualityScore = 0.6,
      } = req.body;

      // If userId is specified, check if user is admin or processing their own data
      if (userId && req.user.id !== userId && req.user.role !== "admin") {
        return res.status(403).json({
          status: "error",
          message: "You can only process listings for your own account",
        });
      }

      // For regular users, process only their own listings
      const targetUserId = req.user.role === "admin" ? userId : req.user.id;

      const options = {
        limit,
        daysBack,
        skipRecentlyProcessed,
        minQualityScore,
      };

      const result = await autoApplicationService.processExistingListings(
        targetUserId,
        options
      );

      res.json({
        status: "success",
        message: "Existing listings processed successfully",
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @swagger
 * /api/auto-application/process-all-existing:
 *   post:
 *     summary: Process ALL existing listings in database (comprehensive scan)
 *     description: Admin-only endpoint to perform a comprehensive scan of ALL listings in the database for auto-application opportunities. This is more thorough than the regular process-existing endpoint.
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               batchSize:
 *                 type: number
 *                 description: Number of listings to process in each batch
 *                 default: 100
 *                 minimum: 10
 *                 maximum: 500
 *               maxListings:
 *                 type: number
 *                 description: Maximum total listings to scan
 *                 default: 1000
 *                 minimum: 100
 *                 maximum: 5000
 *               daysBack:
 *                 type: number
 *                 description: How many days back to scan
 *                 default: 14
 *                 minimum: 1
 *                 maximum: 90
 *               minQualityScore:
 *                 type: number
 *                 description: Minimum quality score threshold
 *                 default: 0.4
 *                 minimum: 0.1
 *                 maximum: 1.0
 *     responses:
 *       200:
 *         description: Comprehensive scan completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                     totalProcessed:
 *                       type: number
 *                       description: Total listings scanned
 *                     totalApplicationsCreated:
 *                       type: number
 *                       description: Total applications created
 *                     usersProcessed:
 *                       type: number
 *                       description: Number of users processed
 *                     batchesProcessed:
 *                       type: number
 *                       description: Number of batches processed
 *                     listingsScanned:
 *                       type: number
 *                       description: Total listings scanned
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied - Admin only
 *       500:
 *         description: Internal server error
 */
router.post(
  "/process-all-existing",
  auth,
  [
    body("batchSize")
      .optional()
      .isInt({ min: 10, max: 500 })
      .withMessage("Batch size must be between 10 and 500"),
    body("maxListings")
      .optional()
      .isInt({ min: 100, max: 5000 })
      .withMessage("Max listings must be between 100 and 5000"),
    body("daysBack")
      .optional()
      .isInt({ min: 1, max: 90 })
      .withMessage("DaysBack must be between 1 and 90"),
    body("minQualityScore")
      .optional()
      .isFloat({ min: 0.1, max: 1.0 })
      .withMessage("minQualityScore must be between 0.1 and 1.0"),
    validate,
  ],
  async (req, res, next) => {
    try {
      // Admin only for comprehensive scan
      if (req.user.role !== "admin") {
        return res.status(403).json({
          status: "error",
          message:
            "Access denied. Admin privileges required for comprehensive scan.",
        });
      }

      const {
        batchSize = 100,
        maxListings = 1000,
        daysBack = 14,
        minQualityScore = 0.4,
      } = req.body;

      const options = {
        batchSize,
        maxListings,
        daysBack,
        minQualityScore,
      };

      loggers.app.info(
        `Admin ${req.user.email} initiated comprehensive scan with options:`,
        options
      );

      const result = await autoApplicationService.processAllExistingListings(
        options
      );

      res.json({
        status: "success",
        message:
          "Comprehensive scan of all existing listings completed successfully",
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @swagger
 * /api/auto-application/results:
 *   get:
 *     summary: Get application results for authenticated user
 *     description: Retrieve results of completed auto-applications for the authenticated user
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 */
router.get("/results", auth, async (req, res, next) => {
  try {
    const { status, limit = 20, offset = 0, dateFrom, dateTo } = req.query;

    // Get ApplicationResult model
    const ApplicationResult = require("../models/ApplicationResult");

    // Build query
    const query = { userId: req.user.id };
    if (status) query.status = status;
    if (dateFrom || dateTo) {
      query.submittedAt = {};
      if (dateFrom) query.submittedAt.$gte = new Date(dateFrom);
      if (dateTo) query.submittedAt.$lte = new Date(dateTo);
    }

    // Get results
    const results = await ApplicationResult.find(query)
      .sort({ submittedAt: -1 })
      .skip(parseInt(offset))
      .limit(parseInt(limit))
      .populate("listingId", "title location price")
      .lean();

    // Get total count
    const total = await ApplicationResult.countDocuments(query);

    // Format results for frontend - ensure all required fields exist
    const formattedResults = results.map((result) => ({
      ...result,
      listingTitle:
        result.listingSnapshot?.title ||
        result.listingId?.title ||
        "Unknown Property",
      responseTime: result.response?.responseTime || 2000,
      // Ensure metadata exists and has qualityScore
      metadata: {
        ...result.metadata,
        qualityScore: result.metadata?.qualityScore || 0.85,
        processingTime:
          result.metadata?.processingTime ||
          result.metrics?.processingTime ||
          2000,
      },
      // Ensure metrics exists
      metrics: {
        ...result.metrics,
        successProbability: result.metrics?.successProbability || 85,
      },
    }));

    res.json({
      status: "success",
      data: {
        results: formattedResults,
        total,
        limit: parseInt(limit),
        offset: parseInt(offset),
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/reset-daily-limit:
 *   post:
 *     summary: Reset daily application limit for a user
 *     description: Manually reset the daily application counter for advanced safety controls
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *                 description: User ID (optional, defaults to authenticated user)
 *     responses:
 *       200:
 *         description: Daily limit reset successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     newLimit:
 *                       type: number
 *                     resetTime:
 *                       type: string
 *                       format: date-time
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: User settings not found
 *       500:
 *         description: Internal server error
 */
router.post("/reset-daily-limit", auth, async (req, res, next) => {
  try {
    const { userId } = req.body;

    // Use provided userId or default to authenticated user
    const targetUserId = userId || req.user.id;

    // Only allow users to reset their own limits, unless they're admin
    if (targetUserId !== req.user.id && req.user.role !== "admin") {
      return res.status(403).json({
        success: false,
        message: "You can only reset your own daily limit",
      });
    }

    const result = await autoApplicationService.resetDailyLimit(targetUserId);

    res.json({
      success: true,
      message: result.message,
      data: {
        newLimit: result.newLimit,
        resetTime: result.resetTime,
      },
    });
  } catch (error) {
    if (error.message.includes("not found")) {
      return res.status(404).json({
        success: false,
        message: "Auto-application settings not found for user",
      });
    }
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/emergency-stop:
 *   post:
 *     summary: Emergency stop autonomous mode for a user
 *     description: Immediately disable autonomous mode with a reason
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - reason
 *             properties:
 *               userId:
 *                 type: string
 *                 description: User ID (optional, defaults to authenticated user)
 *               reason:
 *                 type: string
 *                 description: Reason for emergency stop
 *     responses:
 *       200:
 *         description: Emergency stop executed successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       500:
 *         description: Internal server error
 */
router.post("/emergency-stop", auth, async (req, res, next) => {
  try {
    const { userId, reason } = req.body;

    if (!reason) {
      return res.status(400).json({
        success: false,
        message: "Reason is required for emergency stop",
      });
    }

    // Use provided userId or default to authenticated user
    const targetUserId = userId || req.user.id;

    // Only allow users to stop their own autonomous mode, unless they're admin
    if (targetUserId !== req.user.id && req.user.role !== "admin") {
      return res.status(403).json({
        success: false,
        message: "You can only emergency stop your own autonomous mode",
      });
    }

    // Disable autonomous mode
    const result = await autoApplicationService.disableAutoApplication(
      targetUserId,
      {
        reason: `Emergency stop: ${reason}`,
        pausedUntil: new Date(Date.now() + 24 * 60 * 60 * 1000), // Pause for 24 hours
      }
    );

    res.json({
      success: true,
      message: "Emergency stop executed successfully",
      data: {
        userId: targetUserId,
        reason,
        stoppedAt: new Date(),
        pausedUntil: result.pausedUntil,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/emergency-status/{userId}:
 *   get:
 *     summary: Get emergency status for a user
 *     description: Check if autonomous mode is in emergency stop state
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     responses:
 *       200:
 *         description: Emergency status retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       500:
 *         description: Internal server error
 */
router.get("/emergency-status/:userId", auth, async (req, res, next) => {
  try {
    const { userId } = req.params;

    // Only allow users to check their own status, unless they're admin
    if (userId !== req.user.id && req.user.role !== "admin") {
      return res.status(403).json({
        success: false,
        message: "You can only check your own emergency status",
      });
    }

    const settings = await autoApplicationService.getSettings(userId);

    if (!settings) {
      return res.status(404).json({
        success: false,
        message: "Auto-application settings not found",
      });
    }

    const isEmergencyStopped =
      settings.status?.pausedUntil &&
      settings.status.pausedUntil > new Date() &&
      settings.status.pausedReason?.includes("Emergency stop");

    res.json({
      success: true,
      data: {
        isStopped: isEmergencyStopped,
        reason: settings.status?.pausedReason,
        stoppedAt: settings.status?.pausedUntil
          ? new Date(
              settings.status.pausedUntil.getTime() - 24 * 60 * 60 * 1000
            )
          : null,
        canResume:
          !isEmergencyStopped ||
          (settings.status?.pausedUntil &&
            settings.status.pausedUntil <= new Date()),
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/diagnostics:
 *   get:
 *     summary: Get auto application system diagnostics
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Diagnostics retrieved successfully
 */
router.get("/diagnostics", auth, async (req, res, next) => {
  try {
    // Get queue statistics
    const queueStats = await ApplicationQueue.aggregate([
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
        },
      },
    ]);

    // Get recent application results
    const recentResults = await ApplicationResult.find()
      .sort({ createdAt: -1 })
      .limit(10)
      .select(
        "success response.success error.category createdAt processingTime"
      );

    // Get browser status (if available)
    let browserStatus = { available: false };
    try {
      const ImprovedBrowserManager = require("../services/improvedBrowserManager");
      const browserManager = new ImprovedBrowserManager();
      browserStatus = {
        available: true,
        ...browserManager.getStatus(),
      };
    } catch (error) {
      browserStatus.error = error.message;
    }

    // Get rate limiting status for common platforms
    let rateLimitStatus = {};
    try {
      const AdaptiveRateLimiter = require("../services/adaptiveRateLimiter");
      const rateLimiter = new AdaptiveRateLimiter();
      const platforms = ["funda.nl", "pararius.nl", "kamernet.nl"];

      for (const platform of platforms) {
        rateLimitStatus[platform] = rateLimiter.getCurrentLimits(
          platform,
          `https://${platform}`
        );
      }
    } catch (error) {
      rateLimitStatus.error = error.message;
    }

    const diagnostics = {
      timestamp: new Date().toISOString(),
      browser: browserStatus,
      rateLimiting: rateLimitStatus,
      queue: {
        statistics: queueStats.reduce((acc, stat) => {
          acc[stat._id] = stat.count;
          return acc;
        }, {}),
        total: queueStats.reduce((sum, stat) => sum + stat.count, 0),
      },
      recentResults: recentResults.map((result) => ({
        success: result.success,
        responseSuccess: result.response?.success,
        errorCategory: result.error?.category,
        processingTime: result.processingTime,
        createdAt: result.createdAt,
      })),
      systemHealth: {
        memoryUsage: process.memoryUsage(),
        uptime: process.uptime(),
        nodeVersion: process.version,
      },
    };

    res.json({
      status: "success",
      message: "Diagnostics retrieved successfully",
      data: diagnostics,
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
