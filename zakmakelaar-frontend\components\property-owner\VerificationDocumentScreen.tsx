import React, { useState, useEffect } from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  Image,
  Platform,
  ToastAndroid,
  StyleSheet,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  FadeInUp,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import { propertyOwnerService } from '../../services';

// Define theme colors to match the rest of the app
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
};

// Enhanced Header Component
const Header = ({
  showBackButton = false,
  onBack,
}: {
  showBackButton?: boolean;
  onBack?: () => void;
}) => {
  return (
    <LinearGradient
      colors={[THEME.primary, THEME.secondary]}
      style={styles.header}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Animated.View
        style={styles.headerContent}
        entering={FadeInUp.duration(600)}
      >
        {showBackButton && (
          <TouchableOpacity
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              onBack?.();
            }}
            style={styles.backButton}
            activeOpacity={0.8}
          >
            <View style={styles.backButtonInner}>
              <Ionicons name="chevron-back" size={24} color={THEME.primary} />
            </View>
          </TouchableOpacity>
        )}

        <View style={styles.headerCenter}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>ZM</Text>
          </View>
          <View style={styles.headerTextContainer}>
            <Text style={styles.headerTitle}>Verification Documents</Text>
            <Text style={styles.headerSubtitle}>Upload your verification documents</Text>
          </View>
        </View>

        <View style={styles.headerSpacer} />
      </Animated.View>
    </LinearGradient>
  );
};

interface Document {
  id?: string;
  name: string;
  type: 'id' | 'business' | 'address' | 'other';
  uri: string;
  status: 'pending' | 'approved' | 'rejected';
  uploadDate?: string;
  fileType?: string;
}

export function VerificationDocumentScreen() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);

  useEffect(() => {
    fetchDocuments();
  }, []);

  const fetchDocuments = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Fetch documents from the API
      const response = await propertyOwnerService.getVerificationDocuments();
      setDocuments(response.data || []);
    } catch (err: any) {
      // Handle missing endpoint gracefully
      if (err.message?.includes("Can't find") || err.message?.includes("404")) {
        console.log('Verification documents endpoint not implemented yet, using empty state');
        setDocuments([]);
      } else {
        setError(err.message || 'Failed to load documents');
        console.error('Error fetching verification documents:', err);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Show toast message
  const showToast = (message: string) => {
    if (Platform.OS === 'android') {
      ToastAndroid.show(message, ToastAndroid.SHORT);
    } else {
      // For iOS, use Alert as a fallback
      Alert.alert('Notification', message);
    }
  };

  const handlePickImage = async (documentType: Document['type']) => {
    try {
      // Request permission
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera roll permissions to upload documents.');
        return;
      }
      
      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedAsset = result.assets[0];
        await uploadDocument(documentType, selectedAsset.uri, 'image');
      }
    } catch (err: any) {
      console.error('Error picking image:', err);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  const handlePickDocument = async (documentType: Document['type']) => {
    try {
      // Launch document picker
      const result = await DocumentPicker.getDocumentAsync({
        type: ['application/pdf'],
        copyToCacheDirectory: true,
      });
      
      if (result.canceled === false && result.assets && result.assets.length > 0) {
        const selectedAsset = result.assets[0];
        await uploadDocument(documentType, selectedAsset.uri, 'pdf');
      }
    } catch (err: any) {
      console.error('Error picking document:', err);
      Alert.alert('Error', 'Failed to pick document. Please try again.');
    }
  };

  const uploadDocument = async (documentType: Document['type'], uri: string, fileType: 'image' | 'pdf') => {
    setIsUploading(true);
    setError(null);
    
    try {
      // Get file name from URI
      const fileName = uri.split('/').pop() || `${documentType}_document`;
      
      // Create document object
      const newDocument: Document = {
        name: fileName,
        type: documentType,
        uri: uri,
        status: 'pending',
        uploadDate: new Date().toISOString(),
        fileType: fileType === 'image' ? 'image/jpeg' : 'application/pdf',
      };
      
      try {
        // Upload document to the API
        await propertyOwnerService.uploadVerificationDocument(newDocument);
        
        // Update local state
        setDocuments([...documents, newDocument]);
      } catch (uploadErr: any) {
        // Handle missing endpoint gracefully
        if (uploadErr.message?.includes("Can't find") || uploadErr.message?.includes("404")) {
          console.log('Upload endpoint not implemented yet, adding to local state only');
          // Still add to local state for demo purposes
          setDocuments([...documents, newDocument]);
        } else {
          throw uploadErr; // Re-throw other errors
        }
      }
      
      // Show success message
      showToast('Document uploaded successfully');
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (err: any) {
      setError(err.message || 'Failed to upload document');
      console.error('Error uploading document:', err);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDeleteDocument = async (documentId: string) => {
    Alert.alert(
      'Delete Document',
      'Are you sure you want to delete this document?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Delete document from the API
              await propertyOwnerService.deleteVerificationDocument(documentId);
              
              // Update local state
              setDocuments(documents.filter(doc => doc.id !== documentId));
              
              // Show success message
              showToast('Document deleted successfully');
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            } catch (err: any) {
              // Handle missing endpoint gracefully
              if (err.message?.includes("Can't find") || err.message?.includes("404")) {
                console.log('Delete endpoint not implemented yet, removing from local state only');
                // Still remove from local state for demo purposes
                setDocuments(documents.filter(doc => doc.id !== documentId));
                showToast('Document deleted successfully');
                Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
              } else {
                setError(err.message || 'Failed to delete document');
                console.error('Error deleting document:', err);
                Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
              }
            }
          }
        }
      ]
    );
  };

  const getDocumentTypeLabel = (type: Document['type']) => {
    switch (type) {
      case 'id': return 'ID Document';
      case 'business': return 'Business Registration';
      case 'address': return 'Proof of Address';
      case 'other': return 'Other Document';
      default: return 'Document';
    }
  };

  const renderDocumentItem = (document: Document) => {
    return (
      <Animated.View 
        key={document.id || document.uri}
        style={styles.documentItem}
        entering={FadeInUp.duration(600)}
      >
        <View style={styles.documentIconContainer}>
          {document.fileType?.includes('image') ? (
            <Image source={{ uri: document.uri }} style={styles.documentThumbnail} />
          ) : (
            <Ionicons name="document-text" size={32} color={THEME.primary} />
          )}
        </View>
        
        <View style={styles.documentInfo}>
          <Text style={styles.documentName} numberOfLines={1} ellipsizeMode="tail">
            {document.name}
          </Text>
          <Text style={styles.documentType}>
            {getDocumentTypeLabel(document.type)}
          </Text>
          <View style={styles.documentStatusContainer}>
            {document.status === 'approved' ? (
              <View style={[styles.statusBadge, styles.approvedBadge]}>
                <Ionicons name="checkmark-circle" size={12} color={THEME.success} />
                <Text style={[styles.statusText, styles.approvedText]}>Approved</Text>
              </View>
            ) : document.status === 'rejected' ? (
              <View style={[styles.statusBadge, styles.rejectedBadge]}>
                <Ionicons name="close-circle" size={12} color={THEME.danger} />
                <Text style={[styles.statusText, styles.rejectedText]}>Rejected</Text>
              </View>
            ) : (
              <View style={[styles.statusBadge, styles.pendingBadge]}>
                <Ionicons name="time" size={12} color={THEME.warning} />
                <Text style={[styles.statusText, styles.pendingText]}>Pending</Text>
              </View>
            )}
          </View>
        </View>
        
        <TouchableOpacity
          style={styles.documentDeleteButton}
          onPress={() => document.id && handleDeleteDocument(document.id)}
          disabled={isUploading}
        >
          <Ionicons name="trash-outline" size={20} color={THEME.danger} />
        </TouchableOpacity>
      </Animated.View>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <LinearGradient
          colors={[THEME.primary, THEME.secondary]}
          style={{
            padding: 24,
            borderRadius: 16,
            alignItems: 'center',
            justifyContent: 'center',
            width: '80%',
            shadowColor: THEME.dark,
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 5,
          }}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <ActivityIndicator size="large" color={THEME.light} />
          <Text style={styles.loadingText}>Loading documents...</Text>
          <Text style={{
            marginTop: 8,
            fontSize: 14,
            color: 'rgba(255, 255, 255, 0.8)',
            textAlign: 'center',
          }}>Please wait while we fetch your documents</Text>
        </LinearGradient>
      </View>
    );
  }

  return (
    <ScrollView 
      style={styles.container}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.scrollContent}
    >
      <Header
        showBackButton={true}
        onBack={() => router.back()}
      />

      {error && (
        <Animated.View 
          style={styles.errorContainer}
          entering={FadeInUp.duration(600)}
        >
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity 
            style={styles.retryButton}
            onPress={fetchDocuments}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </Animated.View>
      )}

      {/* Instructions */}
      <Animated.View 
        style={styles.instructionsCard}
        entering={FadeInUp.duration(600).delay(200)}
      >
        <LinearGradient
          colors={['rgba(67, 97, 238, 0.1)', 'rgba(114, 9, 183, 0.1)']}
          style={styles.instructionsGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <Ionicons name="information-circle" size={32} color={THEME.primary} />
          <Text style={styles.instructionsTitle}>Verification Requirements</Text>
          <Text style={styles.instructionsText}>
            To verify your property owner account, please upload the following documents:
          </Text>
          <View style={styles.instructionsList}>
            <View style={styles.instructionItem}>
              <Ionicons name="checkmark-circle" size={16} color={THEME.success} />
              <Text style={styles.instructionText}>Government-issued ID (passport or driver&apos;s license)</Text>
            </View>
            <View style={styles.instructionItem}>
              <Ionicons name="checkmark-circle" size={16} color={THEME.success} />
              <Text style={styles.instructionText}>Business registration certificate</Text>
            </View>
            <View style={styles.instructionItem}>
              <Ionicons name="checkmark-circle" size={16} color={THEME.success} />
              <Text style={styles.instructionText}>Proof of address (utility bill or bank statement)</Text>
            </View>
          </View>
          <Text style={styles.instructionsNote}>
            Documents will be reviewed within 1-2 business days. You will be notified once your account is verified.
          </Text>
        </LinearGradient>
      </Animated.View>

      {/* Development Notice */}
      <Animated.View 
        style={styles.developmentNotice}
        entering={FadeInUp.duration(600).delay(250)}
      >
        <Ionicons name="construct" size={20} color="#f59e0b" />
        <Text style={styles.developmentNoticeText}>
          Note: Document verification is currently in development. You can upload documents for testing, but they won't be processed yet.
        </Text>
      </Animated.View>

      {/* Upload Buttons */}
      <Animated.View 
        style={styles.uploadSection}
        entering={FadeInUp.duration(600).delay(300)}
      >
        <Text style={styles.sectionTitle}>Upload Documents</Text>
        
        <View style={styles.uploadButtonsContainer}>
          <TouchableOpacity
            style={styles.uploadButton}
            onPress={() => handlePickImage('id')}
            disabled={isUploading}
          >
            <LinearGradient
              colors={[THEME.primary, THEME.secondary]}
              style={styles.uploadButtonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Ionicons name="id-card" size={24} color={THEME.light} />
              <Text style={styles.uploadButtonText}>ID Document</Text>
            </LinearGradient>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.uploadButton}
            onPress={() => handlePickDocument('business')}
            disabled={isUploading}
          >
            <LinearGradient
              colors={[THEME.primary, THEME.secondary]}
              style={styles.uploadButtonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Ionicons name="business" size={24} color={THEME.light} />
              <Text style={styles.uploadButtonText}>Business Reg.</Text>
            </LinearGradient>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.uploadButton}
            onPress={() => handlePickImage('address')}
            disabled={isUploading}
          >
            <LinearGradient
              colors={[THEME.primary, THEME.secondary]}
              style={styles.uploadButtonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Ionicons name="home" size={24} color={THEME.light} />
              <Text style={styles.uploadButtonText}>Address Proof</Text>
            </LinearGradient>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.uploadButton}
            onPress={() => handlePickDocument('other')}
            disabled={isUploading}
          >
            <LinearGradient
              colors={[THEME.primary, THEME.secondary]}
              style={styles.uploadButtonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Ionicons name="document" size={24} color={THEME.light} />
              <Text style={styles.uploadButtonText}>Other Document</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
        
        {isUploading && (
          <View style={styles.uploadingContainer}>
            <ActivityIndicator size="small" color={THEME.primary} />
            <Text style={styles.uploadingText}>Uploading document...</Text>
          </View>
        )}
      </Animated.View>

      {/* Documents List */}
      <Animated.View 
        style={styles.documentsSection}
        entering={FadeInUp.duration(600).delay(400)}
      >
        <Text style={styles.sectionTitle}>Your Documents</Text>
        
        {documents.length === 0 ? (
          <View style={styles.emptyDocuments}>
            <Ionicons name="document-outline" size={48} color={THEME.gray} />
            <Text style={styles.emptyDocumentsText}>No documents uploaded yet</Text>
            <Text style={styles.emptyDocumentsSubtext}>
              Upload your verification documents to get your account verified
            </Text>
          </View>
        ) : (
          <View style={styles.documentsList}>
            {documents.map(renderDocumentItem)}
          </View>
        )}
      </Animated.View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  header: {
    paddingTop: 16,
    paddingBottom: 16,
    paddingHorizontal: 16,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: THEME.light,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  logoText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: THEME.primary,
  },
  headerTextContainer: {
    flexDirection: 'column',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: THEME.light,
  },
  headerSubtitle: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  headerSpacer: {
    width: 40,
  },
  backButton: {
    marginRight: 12,
  },
  backButtonInner: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: THEME.light,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    backgroundColor: THEME.lightGray,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: 'bold',
    color: THEME.light,
  },
  errorContainer: {
    margin: 16,
    padding: 16,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderRadius: 12,
    alignItems: 'center',
  },
  errorText: {
    color: THEME.danger,
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 12,
  },
  retryButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: THEME.danger,
    borderRadius: 8,
  },
  retryButtonText: {
    color: THEME.light,
    fontSize: 14,
    fontWeight: 'bold',
  },
  instructionsCard: {
    margin: 16,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  instructionsGradient: {
    padding: 16,
    alignItems: 'center',
  },
  instructionsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: THEME.dark,
    marginTop: 8,
    marginBottom: 8,
  },
  instructionsText: {
    fontSize: 14,
    color: THEME.dark,
    textAlign: 'center',
    marginBottom: 12,
  },
  instructionsList: {
    width: '100%',
    marginVertical: 8,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  instructionText: {
    fontSize: 14,
    color: THEME.dark,
    marginLeft: 8,
  },
  instructionsNote: {
    fontSize: 12,
    color: THEME.gray,
    textAlign: 'center',
    marginTop: 8,
    fontStyle: 'italic',
  },
  developmentNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    padding: 12,
    backgroundColor: '#fffbeb',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#fbbf24',
  },
  developmentNoticeText: {
    fontSize: 14,
    color: '#92400e',
    marginLeft: 8,
    flex: 1,
    lineHeight: 20,
  },
  uploadSection: {
    margin: 16,
    marginTop: 0,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: THEME.dark,
    marginBottom: 16,
  },
  uploadButtonsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  uploadButton: {
    width: '48%',
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  uploadButtonGradient: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    height: 100,
  },
  uploadButtonText: {
    color: THEME.light,
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 8,
  },
  uploadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
  },
  uploadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: THEME.primary,
  },
  documentsSection: {
    margin: 16,
    marginTop: 0,
  },
  documentsList: {
    marginTop: 8,
  },
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: THEME.light,
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  documentIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: 'rgba(67, 97, 238, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    overflow: 'hidden',
  },
  documentThumbnail: {
    width: 48,
    height: 48,
    borderRadius: 8,
  },
  documentInfo: {
    flex: 1,
  },
  documentName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: THEME.dark,
  },
  documentType: {
    fontSize: 12,
    color: THEME.gray,
    marginTop: 2,
  },
  documentStatusContainer: {
    marginTop: 4,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  approvedBadge: {
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
  },
  rejectedBadge: {
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
  },
  pendingBadge: {
    backgroundColor: 'rgba(245, 158, 11, 0.1)',
  },
  statusText: {
    fontSize: 10,
    marginLeft: 4,
  },
  approvedText: {
    color: THEME.success,
  },
  rejectedText: {
    color: THEME.danger,
  },
  pendingText: {
    color: THEME.warning,
  },
  documentDeleteButton: {
    padding: 8,
  },
  emptyDocuments: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    backgroundColor: THEME.light,
    borderRadius: 12,
  },
  emptyDocumentsText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: THEME.dark,
    marginTop: 12,
  },
  emptyDocumentsSubtext: {
    fontSize: 14,
    color: THEME.gray,
    textAlign: 'center',
    marginTop: 8,
  },
});
