import React from "react";
import { View, StyleSheet } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { StatusBar } from "expo-status-bar";
import { TenantScreeningInterface } from "../../components/property-owner/TenantScreeningInterface";

// Define theme colors
const THEME = {
  lightGray: "#f3f4f6",
};

export default function TenantScreeningScreen() {
  return (
    <SafeAreaView style={styles.container} edges={["left", "right", "bottom"]}>
      <StatusBar style="dark" />
      <TenantScreeningInterface />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
});
