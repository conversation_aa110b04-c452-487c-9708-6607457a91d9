// Original V1 scraper service - restored
const { scrapeFunda } = require("./scrapers/fundaScraper");
const { scrapePararius } = require("./scrapers/parariusScraper");
const { scrapeHuurwoningen } = require("./scrapers/huurwoningenScraper");
const {
  cleanup,
  getScrapingMetrics,
  scrapingMetrics,
} = require("./scraperUtils");
const {
  transformationMonitor,
} = require("../monitoring/transformationMonitor");
const scraperAutoApplicationIntegration = require("./scraperAutoApplicationIntegration");
const { Queue, Worker, QueueEvents } = require("bullmq");
const config = require("../config/config");
const { loggers } = require("./logger");
const metricsService = require("./metricsService");

// Agent management
let agentStatus = {
  isRunning: false,
  currentTask: "idle",
  config: {
    scrapeInterval: 15 * 60 * 1000, // 15 minutes
    maxRetries: 3,
    timeout: 30000,
    activeScrapers: ["funda"],
  },
};

let agentRepeatJobKey = null;

const queueConnection = {
  host: config.redisHost,
  port: config.redisPort,
  password: config.redisPassword || undefined,
  maxRetriesPerRequest: null,
  enableReadyCheck: false,
};

const scrapingQueue = new Queue("scraping-jobs", {
  connection: queueConnection,
  defaultJobOptions: {
    attempts: agentStatus.config.maxRetries || 3,
    backoff: { type: "exponential", delay: 60000 },
    removeOnComplete: 100,
    removeOnFail: 200,
    timeout: config.scraperQueueJobTimeoutMs,
  },
});

const scrapingEvents = new QueueEvents("scraping-jobs", {
  connection: queueConnection,
});

scrapingEvents.on("error", (error) => {
  loggers.scraper.error("Scraping queue events error", {
    error: error.message,
  });
});

scrapingEvents.on("failed", ({ jobId, failedReason }) => {
  loggers.scraper.error("Scraping queue job failed", {
    jobId,
    error: failedReason,
  });
});

scrapingEvents.on("completed", ({ jobId }) => {
  loggers.scraper.debug("Scraping queue job completed", { jobId });
});

scrapingEvents.waitUntilReady().catch((error) => {
  loggers.scraper.error("Scraping queue events failed to initialize", {
    error: error.message,
  });
});

const workerConcurrency =
  parseInt(process.env.SCRAPER_QUEUE_CONCURRENCY, 10) || 1;
const workerLockDuration = Math.max(config.scraperQueueLockDurationMs, 60000);
const workerSettings = {
  lockDuration: workerLockDuration,
  lockRenewTime: Math.max(5000, Math.floor(workerLockDuration / 2)),
  stalledInterval: config.scraperQueueStalledIntervalMs,
  maxStalledCount: config.scraperQueueMaxStalledCount,
};

const scrapingWorker = new Worker(
  "scraping-jobs",
  async (job) => {
    loggers.scraper.info("Processing scraping job", {
      jobId: job.id,
      trigger: job.data?.trigger,
    });
    return scrapeAll(job.data?.options || {});
  },
  {
    connection: queueConnection,
    concurrency: workerConcurrency,
    settings: workerSettings,
  }
);

scrapingWorker.on("error", (error) => {
  loggers.scraper.error("Scraping worker encountered an error", {
    error: error.message,
  });
});

scrapingWorker.on("stalled", (jobId) => {
  loggers.scraper.warn("Scraping job stalled, retrying", { jobId });
});

scrapingWorker.on("completed", (job, result) => {
  loggers.scraper.info("Scraping job completed", {
    jobId: job.id,
    totalListingsFound: result?.totalListingsFound || 0,
    durationMs: result?.durationMs,
  });
});

scrapingWorker.on("failed", (job, error) => {
  loggers.scraper.error("Scraping job failed", {
    jobId: job?.id,
    error: error.message,
  });
});

const shutdownQueueInfrastructure = async () => {
  loggers.scraper.info("Shutting down scraping queue infrastructure");

  const tasks = [];

  if (scrapingWorker) {
    tasks.push(
      scrapingWorker.close().catch((error) => {
        loggers.scraper.error("Failed to close scraping worker", {
          error: error.message,
        });
      })
    );
  }

  if (scrapingEvents) {
    tasks.push(
      scrapingEvents.close().catch((error) => {
        loggers.scraper.error("Failed to close scraping queue events", {
          error: error.message,
        });
      })
    );
  }

  if (scrapingQueue) {
    tasks.push(
      scrapingQueue.close().catch((error) => {
        loggers.scraper.error("Failed to close scraping queue", {
          error: error.message,
        });
      })
    );
  }

  await Promise.allSettled(tasks);
};

const getAgentStatus = () => {
  return { ...agentStatus };
};

const enqueueScrapeJob = async (
  { trigger = "manual", sites = null } = {},
  jobOptions = {}
) => {
  return scrapingQueue.add(
    "scraper-run",
    {
      trigger,
      options: {
        sites,
      },
    },
    {
      removeOnComplete: 100,
      removeOnFail: 200,
      timeout: config.scraperQueueJobTimeoutMs,
      ...jobOptions,
    }
  );
};

const scheduleAgentJob = async () => {
  if (agentRepeatJobKey) {
    try {
      await scrapingQueue.removeRepeatableByKey(agentRepeatJobKey);
    } catch (error) {
      loggers.scraper.warn("Failed to remove existing scheduled scraper job", {
        error: error.message,
      });
    }
    agentRepeatJobKey = null;
  }

  const scheduledJob = await scrapingQueue.add(
    "scraper-scheduled",
    {
      trigger: "agent",
      options: { sites: agentStatus.config.activeScrapers },
    },
    {
      repeat: {
        every: agentStatus.config.scrapeInterval,
      },
      removeOnComplete: true,
      jobId: "scraper:scheduled",
    }
  );

  agentRepeatJobKey = scheduledJob?.repeatJobKey || null;
  loggers.scraper.info("Scheduled recurring scraping job", {
    intervalMs: agentStatus.config.scrapeInterval,
    repeatKey: agentRepeatJobKey,
    activeScrapers: agentStatus.config.activeScrapers,
  });
};

const cancelAgentJob = async () => {
  if (!agentRepeatJobKey) {
    return;
  }

  try {
    await scrapingQueue.removeRepeatableByKey(agentRepeatJobKey);
    loggers.scraper.info("Removed recurring scraping job", {
      repeatKey: agentRepeatJobKey,
    });
  } catch (error) {
    loggers.scraper.warn("Failed to remove recurring scraping job", {
      error: error.message,
      repeatKey: agentRepeatJobKey,
    });
  } finally {
    agentRepeatJobKey = null;
  }
};

const startAgent = async () => {
  if (agentStatus.isRunning) {
    loggers.scraper.warn("Scraper agent start requested while running");
    return { success: false, message: "Agent is already running" };
  }

  agentStatus.isRunning = true;
  agentStatus.currentTask = "scraping";

  try {
    await scheduleAgentJob();
    await enqueueScrapeJob({
      trigger: "agent-start",
      sites: agentStatus.config.activeScrapers,
    });
    loggers.scraper.info("Scraper agent started", {
      intervalMs: agentStatus.config.scrapeInterval,
      activeScrapers: agentStatus.config.activeScrapers,
    });
    return { success: true, message: "Agent started successfully" };
  } catch (error) {
    agentStatus.isRunning = false;
    agentStatus.currentTask = "idle";
    loggers.scraper.error("Failed to start scraper agent", {
      error: error.message,
    });
    return {
      success: false,
      message: "Failed to start scraper agent",
      error: error.message,
    };
  }
};

const stopAgent = async () => {
  if (!agentStatus.isRunning) {
    return { success: false, message: "Agent is not running" };
  }

  await cancelAgentJob();
  agentStatus.isRunning = false;
  agentStatus.currentTask = "idle";

  return { success: true, message: "Agent stopped successfully" };
};

const updateAgentConfig = (newConfig) => {
  const oldConfig = { ...agentStatus.config };
  agentStatus.config = { ...agentStatus.config, ...newConfig };

  // If interval changed and agent is running, restart it
  if (
    agentStatus.isRunning &&
    oldConfig.scrapeInterval !== agentStatus.config.scrapeInterval
  ) {
    stopAgent();
    startAgent();
  }

  return {
    success: true,
    message: "Configuration updated",
    config: agentStatus.config,
  };
};

// Individual scraper management functions
const enableScraper = (scraperName) => {
  const validScrapers = ["funda", "pararius", "huurwoningen"];

  if (!validScrapers.includes(scraperName)) {
    return {
      success: false,
      message: `Invalid scraper name. Valid options: ${validScrapers.join(
        ", "
      )}`,
    };
  }

  if (agentStatus.config.activeScrapers.includes(scraperName)) {
    return {
      success: false,
      message: `Scraper "${scraperName}" is already enabled`,
    };
  }

  agentStatus.config.activeScrapers.push(scraperName);

  return {
    success: true,
    message: `Scraper "${scraperName}" enabled successfully`,
    activeScrapers: agentStatus.config.activeScrapers,
  };
};

const disableScraper = (scraperName) => {
  const validScrapers = ["funda", "pararius", "huurwoningen"];

  if (!validScrapers.includes(scraperName)) {
    return {
      success: false,
      message: `Invalid scraper name. Valid options: ${validScrapers.join(
        ", "
      )}`,
    };
  }

  const index = agentStatus.config.activeScrapers.indexOf(scraperName);
  if (index === -1) {
    return {
      success: false,
      message: `Scraper "${scraperName}" is already disabled`,
    };
  }

  agentStatus.config.activeScrapers.splice(index, 1);

  return {
    success: true,
    message: `Scraper "${scraperName}" disabled successfully`,
    activeScrapers: agentStatus.config.activeScrapers,
  };
};

const getScraperStatus = () => {
  const allScrapers = ["funda", "pararius", "huurwoningen"];
  const scraperStatus = {};

  allScrapers.forEach((scraper) => {
    scraperStatus[scraper] = {
      enabled: agentStatus.config.activeScrapers.includes(scraper),
      available: true,
    };
  });

  return {
    success: true,
    scrapers: scraperStatus,
    activeScrapers: agentStatus.config.activeScrapers,
    totalActive: agentStatus.config.activeScrapers.length,
    totalAvailable: allScrapers.length,
  };
};

// Convenience function to scrape all sites
const scrapeAll = async () => {
  console.log("🚀 Starting scrape of all sites...");
  const results = [];
  let overallSuccess = true;
  let totalListingsFound = 0;
  let totalListingsSaved = 0;
  let totalDuplicatesSkipped = 0;

  // Record the start of the overall scraping session
  scrapingMetrics.recordScrapeStart();

  // Initialize transformation monitor if needed
  await transformationMonitor.initialize();

  if (agentStatus.config.activeScrapers.includes("funda")) {
    const context = transformationMonitor.startTransformation("funda", {
      type: "scraping",
    });
    const siteStart = Date.now();
    try {
      console.log("📍 Scraping Funda...");
      const fundaResult = await scrapeFunda();
      const listingsFound = fundaResult?.length || 0;
      const listingsSaved = listingsFound; // Assuming all found listings are saved
      const duplicatesSkipped = 0; // This would need to be tracked in the scraper

      // Process new listings for auto-application
      if (fundaResult && fundaResult.length > 0) {
        try {
          console.log("🤖 Processing Funda listings for auto-application...");
          const autoAppResult =
            await scraperAutoApplicationIntegration.processNewListings(
              fundaResult,
              "funda.nl"
            );
          console.log(
            `✅ Auto-application processing complete: ${autoAppResult.autoApplicationTriggered} applications triggered`
          );
        } catch (autoAppError) {
          console.error(
            "❌ Auto-application processing failed:",
            autoAppError.message
          );
          // Don't fail the entire scraping process if auto-application fails
        }
      }

      // Record per-site metrics (without calling recordScrapeStart again)
      if (
        scrapingMetrics.metrics &&
        scrapingMetrics.metrics.siteMetrics &&
        scrapingMetrics.metrics.siteMetrics.funda
      ) {
        scrapingMetrics.metrics.siteMetrics.funda.listingsFound +=
          listingsFound;
        scrapingMetrics.metrics.siteMetrics.funda.listingsSaved +=
          listingsSaved;
        scrapingMetrics.metrics.siteMetrics.funda.duplicatesSkipped +=
          duplicatesSkipped;
        scrapingMetrics.metrics.siteMetrics.funda.successfulScrapes++;
        scrapingMetrics.metrics.siteMetrics.funda.lastScrapeTime = new Date();
      }

      totalListingsFound += listingsFound;
      totalListingsSaved += listingsSaved;
      totalDuplicatesSkipped += duplicatesSkipped;

      await transformationMonitor.endTransformation(context, {
        success: true,
        itemsProcessed: listingsFound,
        source: "funda",
      });
      await metricsService.recordScraperResult({
        site: "funda",
        success: true,
        durationMs: Date.now() - siteStart,
        listingsFound,
      });
      results.push({
        site: "funda",
        success: true,
        data: fundaResult,
        listingsFound,
      });
    } catch (error) {
      console.error("❌ Funda scraping failed:", error.message);
      overallSuccess = false;

      // Record per-site failure
      if (
        scrapingMetrics.metrics &&
        scrapingMetrics.metrics.siteMetrics &&
        scrapingMetrics.metrics.siteMetrics.funda
      ) {
        scrapingMetrics.metrics.siteMetrics.funda.failedScrapes++;
      }

      await transformationMonitor.recordError(context, error);
      await metricsService.recordScraperResult({
        site: "funda",
        success: false,
        durationMs: Date.now() - siteStart,
        listingsFound: 0,
        error,
      });
      results.push({ site: "funda", success: false, error: error.message });
    }
  }

  if (agentStatus.config.activeScrapers.includes("pararius")) {
    const context = transformationMonitor.startTransformation("pararius", {
      type: "scraping",
    });
    const siteStart = Date.now();
    try {
      console.log("📍 Scraping Pararius...");
      const parariusResult = await scrapePararius();
      const listingsFound = parariusResult?.length || 0;
      const listingsSaved = listingsFound; // Assuming all found listings are saved
      const duplicatesSkipped = 0; // This would need to be tracked in the scraper

      // Process new listings for auto-application
      if (parariusResult && parariusResult.length > 0) {
        try {
          console.log(
            "🤖 Processing Pararius listings for auto-application..."
          );
          const autoAppResult =
            await scraperAutoApplicationIntegration.processNewListings(
              parariusResult,
              "pararius.nl"
            );
          console.log(
            `✅ Auto-application processing complete: ${autoAppResult.autoApplicationTriggered} applications triggered`
          );
        } catch (autoAppError) {
          console.error(
            "❌ Auto-application processing failed:",
            autoAppError.message
          );
          // Don't fail the entire scraping process if auto-application fails
        }
      }

      // Record per-site metrics
      if (
        scrapingMetrics.metrics &&
        scrapingMetrics.metrics.siteMetrics &&
        scrapingMetrics.metrics.siteMetrics.pararius
      ) {
        scrapingMetrics.metrics.siteMetrics.pararius.listingsFound +=
          listingsFound;
        scrapingMetrics.metrics.siteMetrics.pararius.listingsSaved +=
          listingsSaved;
        scrapingMetrics.metrics.siteMetrics.pararius.duplicatesSkipped +=
          duplicatesSkipped;
        scrapingMetrics.metrics.siteMetrics.pararius.successfulScrapes++;
        scrapingMetrics.metrics.siteMetrics.pararius.lastScrapeTime =
          new Date();
      }

      totalListingsFound += listingsFound;
      totalListingsSaved += listingsSaved;
      totalDuplicatesSkipped += duplicatesSkipped;

      await transformationMonitor.endTransformation(context, {
        success: true,
        itemsProcessed: listingsFound,
        source: "pararius",
      });
      await metricsService.recordScraperResult({
        site: "pararius",
        success: true,
        durationMs: Date.now() - siteStart,
        listingsFound,
      });
      results.push({
        site: "pararius",
        success: true,
        data: parariusResult,
        listingsFound,
      });
    } catch (error) {
      console.error("❌ Pararius scraping failed:", error.message);
      overallSuccess = false;

      // Record per-site failure
      if (
        scrapingMetrics.metrics &&
        scrapingMetrics.metrics.siteMetrics &&
        scrapingMetrics.metrics.siteMetrics.pararius
      ) {
        scrapingMetrics.metrics.siteMetrics.pararius.failedScrapes++;
      }

      await transformationMonitor.recordError(context, error);
      await metricsService.recordScraperResult({
        site: "pararius",
        success: false,
        durationMs: Date.now() - siteStart,
        listingsFound: 0,
        error,
      });
      results.push({ site: "pararius", success: false, error: error.message });
    }
  }

  if (agentStatus.config.activeScrapers.includes("huurwoningen")) {
    const context = transformationMonitor.startTransformation("huurwoningen", {
      type: "scraping",
    });
    const siteStart = Date.now();
    try {
      console.log("📍 Scraping Huurwoningen...");
      const huurwoningenResult = await scrapeHuurwoningen();
      const listingsFound = huurwoningenResult?.length || 0;
      const listingsSaved = listingsFound; // Assuming all found listings are saved
      const duplicatesSkipped = 0; // This would need to be tracked in the scraper

      // Process new listings for auto-application
      if (huurwoningenResult && huurwoningenResult.length > 0) {
        try {
          console.log(
            "🤖 Processing Huurwoningen listings for auto-application..."
          );
          const autoAppResult =
            await scraperAutoApplicationIntegration.processNewListings(
              huurwoningenResult,
              "huurwoningen.nl"
            );
          console.log(
            `✅ Auto-application processing complete: ${autoAppResult.autoApplicationTriggered} applications triggered`
          );
        } catch (autoAppError) {
          console.error(
            "❌ Auto-application processing failed:",
            autoAppError.message
          );
          // Don't fail the entire scraping process if auto-application fails
        }
      }

      // Record per-site metrics
      if (
        scrapingMetrics.metrics &&
        scrapingMetrics.metrics.siteMetrics &&
        scrapingMetrics.metrics.siteMetrics.huurwoningen
      ) {
        scrapingMetrics.metrics.siteMetrics.huurwoningen.listingsFound +=
          listingsFound;
        scrapingMetrics.metrics.siteMetrics.huurwoningen.listingsSaved +=
          listingsSaved;
        scrapingMetrics.metrics.siteMetrics.huurwoningen.duplicatesSkipped +=
          duplicatesSkipped;
        scrapingMetrics.metrics.siteMetrics.huurwoningen.successfulScrapes++;
        scrapingMetrics.metrics.siteMetrics.huurwoningen.lastScrapeTime =
          new Date();
      }

      totalListingsFound += listingsFound;
      totalListingsSaved += listingsSaved;
      totalDuplicatesSkipped += duplicatesSkipped;

      await transformationMonitor.endTransformation(context, {
        success: true,
        itemsProcessed: listingsFound,
        source: "huurwoningen",
      });
      await metricsService.recordScraperResult({
        site: "huurwoningen",
        success: true,
        durationMs: Date.now() - siteStart,
        listingsFound,
      });
      results.push({
        site: "huurwoningen",
        success: true,
        data: huurwoningenResult,
        listingsFound,
      });
    } catch (error) {
      console.error("❌ Huurwoningen scraping failed:", error.message);
      overallSuccess = false;

      // Record per-site failure
      if (
        scrapingMetrics.metrics &&
        scrapingMetrics.metrics.siteMetrics &&
        scrapingMetrics.metrics.siteMetrics.huurwoningen
      ) {
        scrapingMetrics.metrics.siteMetrics.huurwoningen.failedScrapes++;
      }

      await transformationMonitor.recordError(context, error);
      await metricsService.recordScraperResult({
        site: "huurwoningen",
        success: false,
        durationMs: Date.now() - siteStart,
        listingsFound: 0,
        error,
      });
      results.push({
        site: "huurwoningen",
        success: false,
        error: error.message,
      });
    }
  }

  // Record the overall result of the scraping session
  if (overallSuccess && results.some((r) => r.success)) {
    scrapingMetrics.recordScrapeSuccess(
      totalListingsFound,
      totalListingsSaved,
      totalDuplicatesSkipped
    );
  } else {
    scrapingMetrics.recordScrapeFailure(
      new Error("One or more sites failed to scrape")
    );
  }

  console.log("✅ All sites scraping completed");
  return results;
};

// Health check function
const healthCheck = () => {
  return {
    status: "healthy",
    timestamp: new Date().toISOString(),
    agent: {
      isRunning: agentStatus.isRunning,
      currentTask: agentStatus.currentTask,
    },
    scrapers: {
      available: ["funda", "pararius", "huurwoningen"],
      active: agentStatus.config.activeScrapers,
    },
  };
};

// Force stop all scraping processes
const forceStopAll = async () => {
  console.log("🛑 Force stopping all scraping processes...");

  // Stop the agent
  const stopResult = stopAgent();

  // Disable all scrapers
  disableScraper("funda");
  disableScraper("pararius");
  disableScraper("huurwoningen");

  // Call cleanup to close any browser instances
  try {
    await cleanup();
    console.log("✅ Browser cleanup completed");
  } catch (error) {
    console.error("❌ Browser cleanup failed:", error.message);
  }

  // Kill Chrome processes (aggressive cleanup)
  try {
    const { exec } = require("child_process");
    const util = require("util");
    const execAsync = util.promisify(exec);

    // Kill Chrome processes on Windows
    await execAsync("taskkill /F /IM chrome.exe /T").catch(() => {
      console.log("No Chrome processes to kill or kill failed");
    });

    console.log("✅ Chrome processes terminated");
  } catch (error) {
    console.error("❌ Failed to kill Chrome processes:", error.message);
  }


  await shutdownQueueInfrastructure();

  return {
    success: true,
    message: "All scraping processes force stopped",
    agentStopped: stopResult.success,
    scrapersDisabled: true,
    browsersClosed: true,
    queueInfrastructureShutdown: true,
  };
};

// Export functions
module.exports = {
  // Core scraping functions
  scrapeFunda,
  scrapePararius,
  scrapeHuurwoningen,
  scrapeAll,

  // Agent management
  getAgentStatus,
  startAgent,
  stopAgent,
  updateAgentConfig,

  // Individual scraper management
  enableScraper,
  disableScraper,
  getScraperStatus,

  // Force stop
  forceStopAll,
  shutdownQueueInfrastructure,

  // Utility functions
  cleanup,
  getScrapingMetrics,
  healthCheck,

  // Auto-application integration
  scraperAutoApplicationIntegration,

  // Legacy compatibility
  getAgentMetrics: getScrapingMetrics,
};

