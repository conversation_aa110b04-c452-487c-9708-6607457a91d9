const puppeteer = require("puppeteer-extra");
const StealthPlugin = require("puppeteer-extra-plugin-stealth");
const fs = require("fs");
const path = require("path");
const os = require("os");
const { loggers } = require("./logger");
const {
  classifyError,
  handleErrorWithRecovery,
  createHealthCheck,
  NetworkError,
  TimeoutError,
  BrowserError,
  ParsingError,
} = require("../utils/scraperErrors");

// Try to use chromium if available
let chromiumPath = undefined;
puppeteer.use(StealthPlugin());
try {
  const chromium = require("chromium");
  chromiumPath = chromium.path;
  console.log("Using Chromium for scraping");
} catch (error) {
  console.log("Chromium not available, using default Chrome");
}

// Enhanced browser pool with better cleanup for Windows
class BrowserPool {
  constructor(maxBrowsers = 2) {
    this.browsers = [];
    this.maxBrowsers = maxBrowsers;
    this.currentIndex = 0;
    this.tempDirs = new Set(); // Track temporary directories
    this.isWindows = process.platform === "win32";
  }

  async getBrowser() {
    if (this.browsers.length < this.maxBrowsers) {
      // Check for Docker environment
      const isDocker =
        process.env.PUPPETEER_EXECUTABLE_PATH &&
        require("fs").existsSync("/.dockerenv");

      // Check scraping-specific headless settings
      const isDemoMode = process.env.DEMO_MODE === "true";
      const useScrapingHeadless = process.env.SCRAPING_HEADLESS === "true";
      const showDevtools = process.env.SCRAPING_SHOW_DEVTOOLS === "true";
      const scrapingSlowMo = parseInt(process.env.SCRAPING_SLOW_MOTION || "0");

      // Determine headless mode for scraping - force headless in Docker
      let headlessMode;
      if (isDocker) {
        // Force headless mode in Docker environment
        headlessMode = true;
      } else if (isDemoMode) {
        // In demo mode, use SCRAPING_HEADLESS setting
        headlessMode = useScrapingHeadless;
      } else {
        // In production, default to headless unless explicitly set
        headlessMode = process.env.SCRAPING_HEADLESS !== "false";
      }

      console.log(
        `🕷️  Scraping Browser: headless=${headlessMode}, devtools=${showDevtools}, slowMo=${scrapingSlowMo}ms`
      );
      if (isDemoMode) {
        console.log(`🎬 Demo Mode Active for Scraping`);
      }

      // Create custom user data directory for better cleanup control
      const userDataDir = this.isWindows
        ? path.join(
            os.tmpdir(),
            `puppeteer_scraper_${Date.now()}_${Math.random()
              .toString(36)
              .substr(2, 9)}`
          )
        : undefined;

      if (userDataDir) {
        this.tempDirs.add(userDataDir);
      }

      const browser = await puppeteer.launch({
        headless: headlessMode,
        slowMo: scrapingSlowMo,
        devtools: showDevtools && !headlessMode,
        userDataDir: userDataDir,
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-infobars",
          ...(!headlessMode
            ? ["--window-position=100,100", "--window-size=1200,800"]
            : ["--window-position=0,0"]),
          "--ignore-certificate-errors",
          "--disable-background-timer-throttling",
          "--disable-backgrounding-occluded-windows",
          "--disable-renderer-backgrounding",
          "--disable-dev-shm-usage",
          "--disable-extensions",
          "--disable-web-security",
          "--disable-features=VizDisplayCompositor",
          "--no-first-run",
          "--no-default-browser-check",
          "--disable-default-apps",
          "--disable-popup-blocking",
          "--disable-translate",
          "--disable-background-networking",
          "--disable-sync",
          "--metrics-recording-only",
          "--no-report-upload",
          "--disable-gpu-sandbox",
          "--disable-software-rasterizer",
          // Windows-specific fixes for lockfile issues
          ...(this.isWindows
            ? [
                "--disable-background-networking",
                "--disable-client-side-phishing-detection",
                "--disable-component-update",
                "--disable-domain-reliability",
                "--disable-sync",
                "--disable-features=AudioServiceOutOfProcess",
              ]
            : []),
          // Docker-specific arguments when in container
          ...(isDocker
            ? [
                "--disable-gpu",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                // Removed --no-zygote and --single-process as they cause Target.createTarget issues in Docker
              ]
            : []),
        ],
        ignoreDefaultArgs: ["--disable-extensions"],
        executablePath:
          process.env.CHROME_EXECUTABLE_PATH ||
          process.env.PUPPETEER_EXECUTABLE_PATH ||
          chromiumPath,
        timeout: 30000,
        handleSIGINT: false,
        handleSIGTERM: false,
      });
      this.browsers.push(browser);
      return browser;
    }

    // Round-robin selection
    const browser = this.browsers[this.currentIndex];
    this.currentIndex = (this.currentIndex + 1) % this.browsers.length;
    return browser;
  }

  async closeAll() {
    // Close all browsers with timeout protection
    const closePromises = this.browsers.map(async (browser) => {
      try {
        // Set a timeout for browser close operation
        const closePromise = browser.close();
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error("Browser close timeout")), 5000)
        );

        await Promise.race([closePromise, timeoutPromise]);
      } catch (error) {
        loggers.scraper.warn("Browser close failed:", error.message);
        try {
          // Force kill if normal close fails
          await browser.process()?.kill("SIGKILL");
        } catch (killError) {
          loggers.scraper.warn("Browser force kill failed:", killError.message);
        }
      }
    });

    await Promise.allSettled(closePromises);
    this.browsers = [];

    // Clean up temporary directories on Windows
    if (this.isWindows && this.tempDirs.size > 0) {
      await this.cleanupTempDirs();
    }
  }

  async cleanupTempDirs() {
    const cleanupPromises = Array.from(this.tempDirs).map(async (tempDir) => {
      try {
        await this.forceRemoveDir(tempDir);
        this.tempDirs.delete(tempDir);
      } catch (error) {
        loggers.scraper.warn(
          `Failed to cleanup temp dir ${tempDir}:`,
          error.message
        );
      }
    });

    await Promise.allSettled(cleanupPromises);
  }

  async forceRemoveDir(dirPath) {
    if (!fs.existsSync(dirPath)) {
      return;
    }

    // Retry mechanism for Windows lockfile issues
    const maxRetries = 5;
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Try to remove the directory
        await fs.promises.rm(dirPath, { recursive: true, force: true });
        return; // Success
      } catch (error) {
        lastError = error;

        if (error.code === "EBUSY" || error.code === "ENOTEMPTY") {
          // Wait a bit and retry for busy/locked files
          await new Promise((resolve) => setTimeout(resolve, attempt * 200));
          continue;
        }

        // For other errors, try alternative cleanup methods
        if (this.isWindows && attempt < maxRetries) {
          try {
            // Try to unlock files first
            await this.unlockWindowsFiles(dirPath);
            await new Promise((resolve) => setTimeout(resolve, 100));
          } catch (unlockError) {
            // Continue to next attempt
          }
        }
      }
    }

    // If all retries failed, log but don't throw
    loggers.scraper.warn(
      `Failed to remove temp directory after ${maxRetries} attempts:`,
      {
        path: dirPath,
        error: lastError?.message,
      }
    );
  }

  async unlockWindowsFiles(dirPath) {
    if (!this.isWindows) return;

    try {
      // Recursively unlock files in the directory
      const items = await fs.promises.readdir(dirPath, { withFileTypes: true });

      for (const item of items) {
        const itemPath = path.join(dirPath, item.name);

        if (item.isDirectory()) {
          await this.unlockWindowsFiles(itemPath);
        } else {
          try {
            // Change file permissions to allow deletion
            await fs.promises.chmod(itemPath, 0o777);
          } catch (chmodError) {
            // Ignore chmod errors
          }
        }
      }
    } catch (error) {
      // Ignore unlock errors
    }
  }

  async getVisibleBrowser() {
    // Launch a visible browser with the same configuration as getBrowser()
    // but with headless: false for testing and demonstration
    const browser = await puppeteer.launch({
      headless: false, // Force visible mode
      slowMo: 0, // Remove slow motion to allow faster typing
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-infobars",
        "--window-position=100,100", // Position window for visibility
        "--window-size=1200,800", // Set reasonable size
        "--ignore-certificate-errors",
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-renderer-backgrounding",
        "--disable-dev-shm-usage",
        "--disable-extensions",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
        "--no-first-run",
        "--no-default-browser-check",
        "--disable-default-apps",
        "--disable-popup-blocking",
        "--disable-translate",
        "--disable-background-networking",
        "--disable-sync",
        "--metrics-recording-only",
        "--no-report-upload",
        "--disable-gpu-sandbox",
        "--disable-software-rasterizer",
      ],
      ignoreDefaultArgs: ["--disable-extensions"],
      executablePath: process.env.CHROME_EXECUTABLE_PATH || chromiumPath,
      timeout: 30000,
    });
    return browser;
  }
}

const browserPool = new BrowserPool();

// Data validation and normalization utilities
// Import the enhanced version that uses the unified schema transformer
const {
  validateAndNormalizeListing: enhancedValidator,
} = require("./transformationIntegration");

// Original implementation kept for fallback
const validateAndNormalizeListingOriginal = (listingData) => {
  if (!listingData.title || !listingData.url || !listingData.location) {
    return null; // Invalid listing
  }

  // Normalize title
  let normalizedTitle = listingData.title.trim();
  // Remove extra whitespace and normalize
  normalizedTitle = normalizedTitle.replace(/\s+/g, " ");
  // Ensure proper capitalization
  if (normalizedTitle.length > 0) {
    normalizedTitle =
      normalizedTitle.charAt(0).toUpperCase() + normalizedTitle.slice(1);
  }

  // Normalize price
  let normalizedPrice = listingData.price;
  if (normalizedPrice && typeof normalizedPrice === "string") {
    // Clean up HTML entities, non-breaking spaces, and extra content
    normalizedPrice = normalizedPrice
      .replace(/&nbsp;/g, " ")
      .replace(/\u00A0/g, " ") // Non-breaking space character (160)
      .replace(/\s+/g, " ")
      .trim();

    // Extract only the price part (before any newlines or extra content)
    const priceLineMatch = normalizedPrice.match(/^([^\\n]+)/);
    if (priceLineMatch) {
      normalizedPrice = priceLineMatch[1].trim();
    }

    // Handle common Dutch price formats
    if (
      normalizedPrice.toLowerCase().includes("op aanvraag") ||
      normalizedPrice.toLowerCase().includes("on request")
    ) {
      normalizedPrice = "Prijs op aanvraag";
    } else {
      // Extract numeric value and format consistently
      const priceMatch = normalizedPrice.match(/€\s*([\d.,]+)/); // Modified to capture numbers with thousands separators
      if (priceMatch) {
        // Handle European number formats where comma can be thousands separator
        let extractedPrice = priceMatch[1].trim();

        let numericPrice;

        // Special case for the format in the screenshot: €3,950 (treat as 3950, not 3.95)
        if (
          extractedPrice.match(/^\d{1,3},\d{3}$/) &&
          !normalizedPrice.includes(".")
        ) {
          // This is the format from the screenshot - comma is a thousands separator
          numericPrice = parseFloat(extractedPrice.replace(/,/g, ""));
        }
        // Case 1: Format with multiple thousands separators and decimal comma (e.g., 1.234.567,89)
        else if (extractedPrice.match(/\d{1,3}(\.\d{3})+,\d+$/)) {
          numericPrice = parseFloat(
            extractedPrice.replace(/\./g, "").replace(",", ".")
          );
        }
        // Case 2: Format with single thousands separator and decimal comma (e.g., 3.950,00)
        else if (extractedPrice.match(/\d{1,3}\.\d{3},\d+$/)) {
          numericPrice = parseFloat(
            extractedPrice.replace(/\./g, "").replace(",", ".")
          );
        }
        // Case 3: Format with comma as thousands separator (e.g., 3,950)
        else if (extractedPrice.match(/\d{1,3},\d{3}$/)) {
          numericPrice = parseFloat(extractedPrice.replace(/,/g, ""));
        }
        // Case 4: Format with period as thousands separator (e.g., 3.950)
        else if (extractedPrice.match(/\d{1,3}\.\d{3}$/)) {
          numericPrice = parseFloat(extractedPrice.replace(/\./g, ""));
        }
        // Case 5: Regular decimal format or other formats
        else {
          numericPrice = parseFloat(extractedPrice.replace(",", "."));
        }
        if (numericPrice > 0) {
          normalizedPrice = `€ ${Math.round(numericPrice).toLocaleString(
            "nl-NL"
          )}`;
          // Add per month if it seems to be a rental price
          if (numericPrice < 10000) {
            normalizedPrice += " per maand";
          }
        }
      }
    }
  } else {
    normalizedPrice = "Prijs op aanvraag";
  }

  // Normalize location
  let normalizedLocation = listingData.location.replace(/\s+/g, " ").trim();

  // Remove common prefixes and clean up
  normalizedLocation = normalizedLocation
    .replace(/^(huis|appartement|kamer|woning)\s+/i, "")
    .toLowerCase()
    .replace(/\b\w/g, (l) => l.toUpperCase()); // Title case

  // Validate URL
  const isValidUrl =
    listingData.url.startsWith("http") &&
    (listingData.url.includes("funda.nl") ||
      listingData.url.includes("pararius.nl") ||
      listingData.url.includes("huurwoningen.nl"));

  if (!isValidUrl) {
    return null;
  }

  // Validate property type
  const validPropertyTypes = [
    "huis",
    "appartement",
    "kamer",
    "parkeergelegenheid",
    "woning",
    "studio",
  ];
  let normalizedPropertyType = listingData.propertyType || "woning";
  if (!validPropertyTypes.includes(normalizedPropertyType)) {
    normalizedPropertyType = "woning";
  }

  return {
    ...listingData,
    title: normalizedTitle,
    price: normalizedPrice,
    location: normalizedLocation,
    propertyType: normalizedPropertyType,
    dateAdded: new Date(),
  };
};

// Use the original implementation for now
// In a future update, we can switch to using the enhanced validator
const validateAndNormalizeListing = validateAndNormalizeListingOriginal;

// Anti-detection utilities
const getRandomUserAgent = () => {
  const userAgents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15",
  ];
  return userAgents[Math.floor(Math.random() * userAgents.length)];
};

const getRandomDelay = (min = 2000, max = 8000) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

const setupPageStealth = async (page) => {
  // Set random user agent
  await page.setUserAgent(getRandomUserAgent());

  // Set realistic viewport
  const viewports = [
    { width: 1920, height: 1080 },
    { width: 1366, height: 768 },
    { width: 1440, height: 900 },
    { width: 1536, height: 864 },
  ];
  const viewport = viewports[Math.floor(Math.random() * viewports.length)];
  await page.setViewport({ ...viewport, deviceScaleFactor: 1 });

  // Override webdriver detection
  await page.evaluateOnNewDocument(() => {
    Object.defineProperty(navigator, "webdriver", {
      get: () => undefined,
    });
  });

  // Add realistic headers
  await page.setExtraHTTPHeaders({
    "Accept-Language": "en-US,en;q=0.9,nl;q=0.8",
    "Accept-Encoding": "gzip, deflate, br",
    Accept:
      "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    Connection: "keep-alive",
    "Upgrade-Insecure-Requests": "1",
  });
};

// Metrics and monitoring
class ScrapingMetrics {
  constructor() {
    this.metrics = {
      totalScrapes: 0,
      successfulScrapes: 0,
      failedScrapes: 0,
      totalListingsFound: 0,
      totalListingsSaved: 0,
      duplicatesSkipped: 0,
      averageScrapingTime: 0,
      lastScrapeTime: null,
      errorsByType: {},
      // Per-site metrics
      siteMetrics: {
        funda: {
          listingsFound: 0,
          listingsSaved: 0,
          duplicatesSkipped: 0,
          successfulScrapes: 0,
          failedScrapes: 0,
          lastScrapeTime: null,
        },
        pararius: {
          listingsFound: 0,
          listingsSaved: 0,
          duplicatesSkipped: 0,
          successfulScrapes: 0,
          failedScrapes: 0,
          lastScrapeTime: null,
        },
        huurwoningen: {
          listingsFound: 0,
          listingsSaved: 0,
          duplicatesSkipped: 0,
          successfulScrapes: 0,
          failedScrapes: 0,
          lastScrapeTime: null,
        },
      },
    };
  }

  recordScrapeStart() {
    this.startTime = Date.now();
    this.metrics.totalScrapes++;
  }

  recordScrapeSuccess(
    listingsFound,
    listingsSaved,
    duplicatesSkipped,
    site = null
  ) {
    const duration = Date.now() - this.startTime;
    this.metrics.successfulScrapes++;
    this.metrics.totalListingsFound += listingsFound;
    this.metrics.totalListingsSaved += listingsSaved;
    this.metrics.duplicatesSkipped += duplicatesSkipped;
    this.metrics.lastScrapeTime = new Date();

    // Update per-site metrics if site is specified
    if (site && this.metrics.siteMetrics[site]) {
      this.metrics.siteMetrics[site].listingsFound += listingsFound;
      this.metrics.siteMetrics[site].listingsSaved += listingsSaved;
      this.metrics.siteMetrics[site].duplicatesSkipped += duplicatesSkipped;
      this.metrics.siteMetrics[site].successfulScrapes++;
      this.metrics.siteMetrics[site].lastScrapeTime = new Date();
    }

    // Update average scraping time
    this.metrics.averageScrapingTime =
      (this.metrics.averageScrapingTime * (this.metrics.successfulScrapes - 1) +
        duration) /
      this.metrics.successfulScrapes;
  }

  recordScrapeFailure(error, site = null) {
    this.metrics.failedScrapes++;

    // Update per-site metrics if site is specified
    if (site && this.metrics.siteMetrics[site]) {
      this.metrics.siteMetrics[site].failedScrapes++;
    }

    // Classify and log the error
    const classifiedError = classifyError(error);

    const errorType = classifiedError.code || "UNKNOWN_ERROR";
    this.metrics.errorsByType[errorType] =
      (this.metrics.errorsByType[errorType] || 0) + 1;

    // Log the error with context
    loggers.scraper.error("Scrape failure", {
      error: classifiedError,
      context: "scrape_failure",
      site: site,
      totalScrapes: this.metrics.totalScrapes,
      failedScrapes: this.metrics.failedScrapes,
    });
  }

  getMetrics() {
    return {
      ...this.metrics,
      successRate:
        this.metrics.totalScrapes > 0
          ? (
              (this.metrics.successfulScrapes / this.metrics.totalScrapes) *
              100
            ).toFixed(2) + "%"
          : "0%",
      averageScrapingTimeFormatted: `${(
        this.metrics.averageScrapingTime / 1000
      ).toFixed(2)}s`,
    };
  }

  // Method to reset metrics to zero
  resetMetrics() {
    this.metrics = {
      totalScrapes: 0,
      successfulScrapes: 0,
      failedScrapes: 0,
      totalListingsFound: 0,
      totalListingsSaved: 0,
      duplicatesSkipped: 0,
      averageScrapingTime: 0,
      lastScrapeTime: null,
      errorsByType: {},
      siteMetrics: {
        funda: {
          listingsFound: 0,
          listingsSaved: 0,
          duplicatesSkipped: 0,
          successfulScrapes: 0,
          failedScrapes: 0,
          lastScrapeTime: null,
        },
        pararius: {
          listingsFound: 0,
          listingsSaved: 0,
          duplicatesSkipped: 0,
          successfulScrapes: 0,
          failedScrapes: 0,
          lastScrapeTime: null,
        },
        huurwoningen: {
          listingsFound: 0,
          listingsSaved: 0,
          duplicatesSkipped: 0,
          successfulScrapes: 0,
          failedScrapes: 0,
          lastScrapeTime: null,
        },
      },
    };
    console.log("Metrics reset to zero");
  }

  // Test method to populate sample data for dashboard verification
  populateTestData() {
    this.metrics.totalScrapes = 5;
    this.metrics.successfulScrapes = 4;
    this.metrics.failedScrapes = 1;
    this.metrics.totalListingsFound = 150;
    this.metrics.totalListingsSaved = 145;
    this.metrics.duplicatesSkipped = 5;
    this.metrics.averageScrapingTime = 45000; // 45 seconds
    this.metrics.lastScrapeTime = new Date();

    // Populate site-specific data
    this.metrics.siteMetrics.funda.listingsFound = 75;
    this.metrics.siteMetrics.funda.listingsSaved = 73;
    this.metrics.siteMetrics.funda.duplicatesSkipped = 2;
    this.metrics.siteMetrics.funda.successfulScrapes = 2;
    this.metrics.siteMetrics.funda.failedScrapes = 0;
    this.metrics.siteMetrics.funda.lastScrapeTime = new Date();

    this.metrics.siteMetrics.pararius.listingsFound = 45;
    this.metrics.siteMetrics.pararius.listingsSaved = 43;
    this.metrics.siteMetrics.pararius.duplicatesSkipped = 2;
    this.metrics.siteMetrics.pararius.successfulScrapes = 1;
    this.metrics.siteMetrics.pararius.failedScrapes = 1;
    this.metrics.siteMetrics.pararius.lastScrapeTime = new Date();

    this.metrics.siteMetrics.huurwoningen.listingsFound = 30;
    this.metrics.siteMetrics.huurwoningen.listingsSaved = 29;
    this.metrics.siteMetrics.huurwoningen.duplicatesSkipped = 1;
    this.metrics.siteMetrics.huurwoningen.successfulScrapes = 1;
    this.metrics.siteMetrics.huurwoningen.failedScrapes = 0;
    this.metrics.siteMetrics.huurwoningen.lastScrapeTime = new Date();

    console.log("Test data populated for dashboard verification");
  }
}

const scrapingMetrics = new ScrapingMetrics();

// Helper function to determine if an error is retryable
const isRetryableError = (error) => {
  const retryableErrors = [
    "TimeoutError",
    "NetworkError",
    "ECONNRESET",
    "ENOTFOUND",
    "ECONNREFUSED",
    "ERR_NETWORK_CHANGED",
  ];

  return retryableErrors.some(
    (errorType) => error.message.includes(errorType) || error.name === errorType
  );
};

// Helper function to scroll the page to load all content
async function autoScroll(page) {
  await page.evaluate(async () => {
    await new Promise((resolve) => {
      let totalHeight = 0;
      const distance = 100;
      const timer = setInterval(() => {
        const scrollHeight = document.body.scrollHeight;
        window.scrollBy(0, distance);
        totalHeight += distance;

        // Add some random delay to make it look more human-like
        const randomDelay = Math.floor(Math.random() * 40) + 10;
        setTimeout(() => {}, randomDelay);

        if (totalHeight >= scrollHeight - window.innerHeight) {
          clearInterval(timer);
          resolve();
        }
      }, 100);
    });
  });
}

// Enhanced cleanup function for graceful shutdown
const cleanup = async () => {
  console.log("Cleaning up scraper resources...");

  try {
    // Close all browsers with enhanced cleanup
    await browserPool.closeAll();

    // Additional Windows-specific cleanup
    if (process.platform === "win32") {
      await cleanupWindowsPuppeteerDirs();
    }

    console.log("Scraper cleanup completed");
  } catch (error) {
    console.warn("Scraper cleanup encountered errors:", error.message);
  }
};

// Clean up any remaining Puppeteer temporary directories on Windows
const cleanupWindowsPuppeteerDirs = async () => {
  try {
    const tmpDir = os.tmpdir();
    const items = await fs.promises.readdir(tmpDir);

    const puppeteerDirs = items.filter(
      (item) =>
        item.startsWith("puppeteer_dev_chrome_profile-") ||
        item.startsWith("puppeteer_scraper_")
    );

    const cleanupPromises = puppeteerDirs.map(async (dir) => {
      const dirPath = path.join(tmpDir, dir);
      try {
        await fs.promises.rm(dirPath, { recursive: true, force: true });
      } catch (error) {
        // Ignore cleanup errors for orphaned directories
      }
    });

    await Promise.allSettled(cleanupPromises);

    if (puppeteerDirs.length > 0) {
      console.log(
        `Cleaned up ${puppeteerDirs.length} orphaned Puppeteer directories`
      );
    }
  } catch (error) {
    // Ignore errors in Windows cleanup
  }
};

// Get scraping metrics
const getScrapingMetrics = () => {
  return scrapingMetrics.getMetrics();
};

module.exports = {
  browserPool,
  validateAndNormalizeListing,
  getRandomUserAgent,
  getRandomDelay,
  setupPageStealth,
  scrapingMetrics,
  isRetryableError,
  autoScroll,
  cleanup,
  getScrapingMetrics,
};
