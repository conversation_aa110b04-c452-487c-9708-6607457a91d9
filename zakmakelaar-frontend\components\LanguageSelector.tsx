import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { useTranslation } from "react-i18next";
import { LinearGradient } from "expo-linear-gradient";
import Animated, {
  FadeInUp,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
} from "react-native-reanimated";
import * as Haptics from "expo-haptics";

interface LanguageSelectorProps {
  style?: any;
  showLabel?: boolean;
}

const THEME = {
  primary: "#4361ee",
  secondary: "#7209b7",
  accent: "#f72585",
  dark: "#0a0a18",
  light: "#ffffff",
  gray: "#6b7280",
  lightGray: "#f3f4f6",
  success: "#10b981",
};

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  style,
  showLabel = true,
}) => {
  const { t, i18n } = useTranslation();

  const changeLanguage = (language: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    i18n.changeLanguage(language);
  };

  const currentLanguage = i18n.language;

  const languages = [
    {
      code: "nl",
      name: t("languages.nl"),
      flag: "🇳🇱",
      shortName: "NL",
    },
    {
      code: "en",
      name: t("languages.en"),
      flag: "🇺🇸",
      shortName: "EN",
    },
  ];

  return (
    <Animated.View
      style={[styles.container, style]}
      entering={FadeInUp.duration(600)}
    >
      {showLabel && <Text style={styles.label}>{t("settings.language")}</Text>}
      <View style={styles.buttonContainer}>
        {languages.map((language, index) => {
          const isActive = currentLanguage === language.code;

          return (
            <TouchableOpacity
              key={language.code}
              style={[
                styles.languageButton,
                isActive && styles.activeButton,
                index === 0 && styles.firstButton,
                index === languages.length - 1 && styles.lastButton,
              ]}
              onPress={() => changeLanguage(language.code)}
              activeOpacity={0.8}
            >
              {isActive ? (
                <LinearGradient
                  colors={[THEME.primary, THEME.secondary]}
                  style={styles.activeGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <View style={styles.buttonContent}>
                    <Text style={styles.flagText}>{language.flag}</Text>
                    <View style={styles.textContainer}>
                      <Text
                        style={[styles.buttonText, styles.activeButtonText]}
                      >
                        {language.name}
                      </Text>
                      <Text style={[styles.shortText, styles.activeShortText]}>
                        {language.shortName}
                      </Text>
                    </View>
                  </View>
                </LinearGradient>
              ) : (
                <View style={styles.buttonContent}>
                  <Text style={styles.flagText}>{language.flag}</Text>
                  <View style={styles.textContainer}>
                    <Text style={styles.buttonText}>{language.name}</Text>
                    <Text style={styles.shortText}>{language.shortName}</Text>
                  </View>
                </View>
              )}
            </TouchableOpacity>
          );
        })}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 0,
  },
  label: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 12,
    color: THEME.dark,
  },
  buttonContainer: {
    flexDirection: "row",
    borderRadius: 16,
    overflow: "hidden",
    backgroundColor: THEME.lightGray,
    padding: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  languageButton: {
    flex: 1,
    paddingVertical: 16,
    paddingHorizontal: 20,
    backgroundColor: "transparent",
    borderRadius: 12,
    overflow: "hidden",
  },
  firstButton: {
    marginRight: 2,
  },
  lastButton: {
    marginLeft: 2,
  },
  activeButton: {
    shadowColor: THEME.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  activeGradient: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 20,
    marginVertical: -16,
    marginHorizontal: -20,
  },
  buttonContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 12,
  },
  flagText: {
    fontSize: 24,
    lineHeight: 28,
  },
  textContainer: {
    alignItems: "center",
  },
  buttonText: {
    fontSize: 16,
    fontWeight: "600",
    color: THEME.dark,
  },
  activeButtonText: {
    color: THEME.light,
    fontWeight: "700",
  },
  shortText: {
    fontSize: 12,
    fontWeight: "500",
    color: THEME.gray,
    marginTop: 2,
  },
  activeShortText: {
    color: "rgba(255, 255, 255, 0.8)",
  },
});

export default LanguageSelector;
