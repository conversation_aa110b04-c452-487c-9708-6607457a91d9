const { GoogleGenerativeAI } = require('@google/generative-ai');
const config = require('../config/config');
const Application = require('../models/Application');
const Property = require('../models/Property');
const User = require('../models/User');
const { loggers } = require('./logger');

class ApplicantFilteringService {
  constructor() {
    this.genAI = new GoogleGenerativeAI(config.googleAI.apiKey);
    this.model = this.genAI.getGenerativeModel({ model: config.googleAI.models.analysis });
    this.logger = loggers.aiLogger;
  }

  /**
   * Filter and rank applications based on property owner preferences
   * @param {string} propertyId - The property ID
   * @param {Object} ownerPreferences - Owner's filtering preferences
   * @returns {Promise<Object>} Filtered and ranked applications with reasoning
   */
  async filterAndRankApplicants(propertyId, ownerPreferences = {}) {
    try {
      this.logger.info('Starting AI-based applicant filtering', { 
        propertyId, 
        preferences: ownerPreferences 
      });

      // Get property details
      const property = await Property.findById(propertyId);
      if (!property) {
        throw new Error('Property not found');
      }

      // Get all submitted applications for this property
      const applications = await Application.find({
        'property.propertyId': propertyId,
        status: { $in: ['submitted', 'under_review', 'screening'] }
      })
      .populate('applicant.userId', 'profile tenantScore preferences')
      .lean();

      if (applications.length === 0) {
        return {
          filteredApplications: [],
          totalApplications: 0,
          filteringCriteria: ownerPreferences,
          summary: 'No applications found for filtering'
        };
      }

      // Set default owner preferences if not provided
      const defaultPreferences = this.getDefaultOwnerPreferences(property);
      const finalPreferences = { ...defaultPreferences, ...ownerPreferences };

      // Apply basic filtering first
      let filteredApplications = this.applyBasicFiltering(applications, finalPreferences, property);

      // Apply AI-based analysis and ranking
      const aiAnalyzedApplications = await this.performAIAnalysis(
        filteredApplications, 
        property, 
        finalPreferences
      );

      // Calculate final scores and ranking
      const rankedApplications = this.calculateFinalRanking(aiAnalyzedApplications, finalPreferences);

      // Generate summary insights
      const summary = await this.generateFilteringSummary(
        rankedApplications, 
        applications.length, 
        finalPreferences
      );

      const result = {
        filteredApplications: rankedApplications,
        totalApplications: applications.length,
        filteredCount: rankedApplications.length,
        filteringCriteria: finalPreferences,
        summary,
        generatedAt: new Date()
      };

      this.logger.info('Applicant filtering completed', {
        propertyId,
        totalApplications: applications.length,
        filteredCount: rankedApplications.length
      });

      return result;

    } catch (error) {
      this.logger.error('Error in applicant filtering', { error: error.message, propertyId });
      throw error;
    }
  }

  /**
   * Get default owner preferences based on property characteristics
   */
  getDefaultOwnerPreferences(property) {
    return {
      // Income requirements
      minimumIncomeRatio: property.policies?.minimumIncome ? 
        (property.policies.minimumIncome / property.rent.amount) : 3.0, // 3x rent minimum
      
      // Occupation preferences
      preferredOccupations: ['employed', 'freelancer', 'student', 'retired'],
      excludedOccupations: [],
      
      // Family size
      maximumOccupants: property.policies?.maximumOccupants || property.rooms || 2,
      
      // Rental history
      requireRentalHistory: false,
      minimumRentalHistoryYears: 0,
      
      // Employment stability
      minimumEmploymentMonths: 6,
      acceptTemporaryContracts: true,
      
      // Other preferences
      minimumAge: 18,
      maximumAge: null,
      petsRequired: property.policies?.petsAllowed ? null : false,
      smokingRequired: property.policies?.smokingAllowed ? null : false,
      
      // Scoring weights (out of 100)
      weights: {
        income: 30,
        employment: 20,
        rentalHistory: 15,
        tenantScore: 20,
        completeness: 10,
        personalFit: 5
      },
      
      // Minimum scores
      minimumTenantScore: property.applicationSettings?.minimumTenantScore || 60,
      minimumOverallScore: 65
    };
  }

  /**
   * Apply basic filtering rules
   */
  applyBasicFiltering(applications, preferences, property) {
    return applications.filter(app => {
      const reasons = [];
      let passed = true;

      // Income requirement
      if (app.applicationData?.employment?.monthlyIncome) {
        const incomeRatio = app.applicationData.employment.monthlyIncome / property.rent.amount;
        if (incomeRatio < preferences.minimumIncomeRatio) {
          reasons.push(`Income ratio ${incomeRatio.toFixed(1)}x below minimum ${preferences.minimumIncomeRatio}x`);
          passed = false;
        }
      }

      // Occupancy limit
      if (app.applicationData?.numberOfOccupants > preferences.maximumOccupants) {
        reasons.push(`Too many occupants: ${app.applicationData.numberOfOccupants} > ${preferences.maximumOccupants}`);
        passed = false;
      }

      // Minimum tenant score
      const tenantScore = app.applicant?.userId?.tenantScore?.overallScore || 0;
      if (tenantScore < preferences.minimumTenantScore) {
        reasons.push(`Tenant score ${tenantScore} below minimum ${preferences.minimumTenantScore}`);
        passed = false;
      }

      // Age requirements (if birth date available)
      // This would need to be implemented based on available user data

      // Store filtering result
      app.filteringResult = {
        passed,
        reasons: reasons.length > 0 ? reasons : ['Passed basic filtering'],
        score: passed ? null : 0 // Will be calculated later for passed applications
      };

      return passed;
    });
  }

  /**
   * Perform AI analysis on applications
   */
  async performAIAnalysis(applications, property, preferences) {
    const batchSize = 5; // Process in batches to avoid API limits
    const analyzedApplications = [];

    for (let i = 0; i < applications.length; i += batchSize) {
      const batch = applications.slice(i, i + batchSize);
      const batchPromises = batch.map(app => this.analyzeIndividualApplication(app, property, preferences));
      
      try {
        const batchResults = await Promise.all(batchPromises);
        analyzedApplications.push(...batchResults);
        
        // Small delay between batches to respect API limits
        if (i + batchSize < applications.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (error) {
        this.logger.error('Error in AI analysis batch', { error: error.message, batchIndex: i });
        // Add applications without AI analysis
        batch.forEach(app => {
          app.aiAnalysis = {
            recommendation: 'neutral',
            confidence: 50,
            reasoning: 'AI analysis unavailable',
            strengths: [],
            concerns: [],
            score: 50
          };
        });
        analyzedApplications.push(...batch);
      }
    }

    return analyzedApplications;
  }

  /**
   * Analyze individual application using AI
   */
  async analyzeIndividualApplication(application, property, preferences) {
    try {
      const prompt = this.buildAIAnalysisPrompt(application, property, preferences);
      
      const result = await this.model.generateContent(prompt);
      const response = result.response.text();
      
      // Parse AI response
      const aiAnalysis = this.parseAIResponse(response);
      application.aiAnalysis = aiAnalysis;
      
      return application;
    } catch (error) {
      this.logger.error('Error in individual AI analysis', { 
        error: error.message, 
        applicationId: application._id 
      });
      
      // Fallback analysis
      application.aiAnalysis = {
        recommendation: 'neutral',
        confidence: 50,
        reasoning: 'AI analysis failed, manual review recommended',
        strengths: [],
        concerns: ['AI analysis unavailable'],
        score: 50
      };
      
      return application;
    }
  }

  /**
   * Build AI analysis prompt
   */
  buildAIAnalysisPrompt(application, property, preferences) {
    return `
As an expert rental application analyst, analyze this rental application and provide detailed insights.

PROPERTY DETAILS:
- Type: ${property.propertyType}
- Rent: €${property.rent.amount}/month
- Size: ${property.size}m²
- Location: ${property.address.city}
- Rooms: ${property.rooms}
- Policies: ${JSON.stringify(property.policies)}

APPLICANT PROFILE:
- Monthly Income: €${application.applicationData?.employment?.monthlyIncome || 'Not provided'}
- Occupation: ${application.applicationData?.employment?.occupation || 'Not provided'}
- Employer: ${application.applicationData?.employment?.employer || 'Not provided'}
- Employment Type: ${application.applicationData?.employment?.contractType || 'Not provided'}
- Number of Occupants: ${application.applicationData?.numberOfOccupants || 1}
- Move-in Date: ${application.applicationData?.moveInDate || 'Not specified'}
- Tenant Score: ${application.applicant?.userId?.tenantScore?.overallScore || 'Not available'}
- Personal Message: ${application.applicationData?.personalMessage || 'None provided'}
- References: ${application.applicationData?.references?.length || 0} references provided

OWNER PREFERENCES:
- Minimum Income Ratio: ${preferences.minimumIncomeRatio}x
- Maximum Occupants: ${preferences.maximumOccupants}
- Minimum Employment Duration: ${preferences.minimumEmploymentMonths} months
- Preferred Occupations: ${preferences.preferredOccupations.join(', ')}

Please analyze this application and respond with a JSON object containing:
{
  "recommendation": "approve" | "conditional" | "reject",
  "confidence": number (0-100),
  "reasoning": "detailed explanation of your recommendation",
  "strengths": ["list of positive aspects"],
  "concerns": ["list of concerns or red flags"],
  "score": number (0-100),
  "specificAdvice": "specific advice for the property owner"
}

Focus on:
1. Financial stability and income adequacy
2. Employment stability and reliability
3. Rental history and responsibility
4. Overall fit with property and owner preferences
5. Risk assessment
6. Application completeness and professionalism
`;
  }

  /**
   * Parse AI response
   */
  parseAIResponse(response) {
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          recommendation: parsed.recommendation || 'neutral',
          confidence: Math.min(100, Math.max(0, parsed.confidence || 50)),
          reasoning: parsed.reasoning || 'No reasoning provided',
          strengths: Array.isArray(parsed.strengths) ? parsed.strengths : [],
          concerns: Array.isArray(parsed.concerns) ? parsed.concerns : [],
          score: Math.min(100, Math.max(0, parsed.score || 50)),
          specificAdvice: parsed.specificAdvice || ''
        };
      }
    } catch (error) {
      this.logger.error('Error parsing AI response', { error: error.message });
    }

    // Fallback parsing
    return {
      recommendation: response.toLowerCase().includes('approve') ? 'approve' : 
                    response.toLowerCase().includes('reject') ? 'reject' : 'conditional',
      confidence: 50,
      reasoning: response.substring(0, 500),
      strengths: [],
      concerns: [],
      score: 50,
      specificAdvice: ''
    };
  }

  /**
   * Calculate final ranking
   */
  calculateFinalRanking(applications, preferences) {
    // Calculate weighted scores for each application
    applications.forEach(app => {
      let finalScore = 0;
      const weights = preferences.weights;
      const factors = {};

      // Income score
      if (app.applicationData?.employment?.monthlyIncome) {
        const incomeRatio = app.applicationData.employment.monthlyIncome / app.property.snapshot.rent;
        factors.income = Math.min(100, (incomeRatio / preferences.minimumIncomeRatio) * 100);
      } else {
        factors.income = 0;
      }

      // Employment score
      factors.employment = this.calculateEmploymentScore(app.applicationData?.employment);

      // Rental history score (placeholder - would need rental history data)
      factors.rentalHistory = 70; // Default assumption

      // Tenant score
      factors.tenantScore = app.applicant?.userId?.tenantScore?.overallScore || 50;

      // Application completeness
      factors.completeness = this.calculateCompletenessScore(app);

      // AI personal fit score
      factors.personalFit = app.aiAnalysis?.score || 50;

      // Calculate weighted final score
      finalScore = 
        (factors.income * weights.income / 100) +
        (factors.employment * weights.employment / 100) +
        (factors.rentalHistory * weights.rentalHistory / 100) +
        (factors.tenantScore * weights.tenantScore / 100) +
        (factors.completeness * weights.completeness / 100) +
        (factors.personalFit * weights.personalFit / 100);

      app.finalRanking = {
        score: Math.round(finalScore),
        factors,
        recommendation: app.aiAnalysis?.recommendation || 'neutral',
        aiConfidence: app.aiAnalysis?.confidence || 50,
        passesMinimum: finalScore >= preferences.minimumOverallScore
      };
    });

    // Sort by final score (descending)
    applications.sort((a, b) => b.finalRanking.score - a.finalRanking.score);

    // Add rank positions
    applications.forEach((app, index) => {
      app.finalRanking.rank = index + 1;
    });

    return applications;
  }

  /**
   * Calculate employment stability score
   */
  calculateEmploymentScore(employment) {
    if (!employment) return 0;
    
    let score = 0;
    
    // Has employment info
    if (employment.employer) score += 30;
    if (employment.occupation) score += 20;
    if (employment.contractType) {
      if (employment.contractType === 'permanent') score += 30;
      else if (employment.contractType === 'temporary') score += 20;
      else score += 10;
    }
    if (employment.monthlyIncome) score += 20;
    
    return Math.min(100, score);
  }

  /**
   * Calculate application completeness score
   */
  calculateCompletenessScore(application) {
    let score = 0;
    const maxScore = 100;
    
    // Basic info
    if (application.applicationData?.personalMessage) score += 15;
    if (application.applicationData?.moveInDate) score += 10;
    if (application.applicationData?.numberOfOccupants) score += 5;
    
    // Employment info
    if (application.applicationData?.employment?.monthlyIncome) score += 20;
    if (application.applicationData?.employment?.employer) score += 15;
    
    // References
    score += Math.min(20, (application.applicationData?.references?.length || 0) * 7);
    
    // Documents
    score += Math.min(15, (application.documents?.length || 0) * 3);
    
    return Math.min(maxScore, score);
  }

  /**
   * Generate filtering summary
   */
  async generateFilteringSummary(rankedApplications, totalApplications, preferences) {
    try {
      const prompt = `
Analyze these rental application filtering results and provide a concise summary for the property owner.

FILTERING RESULTS:
- Total applications received: ${totalApplications}
- Applications after filtering: ${rankedApplications.length}
- Top 3 candidates scores: ${rankedApplications.slice(0, 3).map(app => app.finalRanking.score).join(', ')}

FILTERING CRITERIA USED:
- Minimum income ratio: ${preferences.minimumIncomeRatio}x
- Maximum occupants: ${preferences.maximumOccupants}
- Minimum tenant score: ${preferences.minimumTenantScore}
- Minimum overall score: ${preferences.minimumOverallScore}

TOP CANDIDATE INFO:
${rankedApplications.length > 0 ? `
- Score: ${rankedApplications[0].finalRanking.score}
- Income: €${rankedApplications[0].applicationData?.employment?.monthlyIncome || 'N/A'}
- Occupation: ${rankedApplications[0].applicationData?.employment?.occupation || 'N/A'}
- AI Recommendation: ${rankedApplications[0].aiAnalysis?.recommendation || 'N/A'}
` : 'No qualified candidates found'}

Provide a brief, actionable summary (2-3 sentences) for the property owner about the quality of applications and next steps.
`;

      const result = await this.model.generateContent(prompt);
      return result.response.text().trim();
    } catch (error) {
      this.logger.error('Error generating summary', { error: error.message });
      return `Filtered ${rankedApplications.length} applications from ${totalApplications} total submissions. ${rankedApplications.length > 0 ? `Top candidate scored ${rankedApplications[0].finalRanking.score}/100.` : 'No applications met the minimum criteria.'} Review recommended.`;
    }
  }

  /**
   * Get quick insights for an application
   */
  async getQuickInsights(applicationId) {
    try {
      const application = await Application.findById(applicationId)
        .populate('applicant.userId', 'profile tenantScore')
        .lean();

      if (!application) {
        throw new Error('Application not found');
      }

      const property = await Property.findById(application.property.propertyId);
      if (!property) {
        throw new Error('Property not found');
      }

      const preferences = this.getDefaultOwnerPreferences(property);
      const analyzed = await this.analyzeIndividualApplication(application, property, preferences);

      return {
        applicationId,
        quickInsights: analyzed.aiAnalysis,
        scores: {
          income: analyzed.applicationData?.employment?.monthlyIncome ? 
            Math.min(100, (analyzed.applicationData.employment.monthlyIncome / property.rent.amount / preferences.minimumIncomeRatio) * 100) : 0,
          employment: this.calculateEmploymentScore(analyzed.applicationData?.employment),
          completeness: this.calculateCompletenessScore(analyzed),
          tenantScore: analyzed.applicant?.userId?.tenantScore?.overallScore || 0
        },
        recommendation: analyzed.aiAnalysis?.recommendation || 'neutral'
      };
    } catch (error) {
      this.logger.error('Error getting quick insights', { error: error.message, applicationId });
      throw error;
    }
  }
}

module.exports = new ApplicantFilteringService();