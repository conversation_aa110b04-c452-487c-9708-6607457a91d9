#!/bin/bash

# MongoDB Authentication Troubleshooting Script for Linux Production
# Usage: ./mongo-troubleshoot.sh [check-env|test-connection|create-admin|all]

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
GRAY='\033[0;90m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== ZakMakelaar MongoDB Authentication Troubleshooter ===${NC}"

# Function to check environment variables
check_environment() {
    echo -e "\n${YELLOW}🔍 Checking Environment Variables...${NC}"
    
    local env_vars=("NODE_ENV" "MONGO_URI" "MONGO_AUTH_DB" "JWT_SECRET" "PORT")
    
    for var in "${env_vars[@]}"; do
        local value="${!var}"
        if [ -n "$value" ]; then
            if [ "$var" = "MONGO_URI" ]; then
                # Mask credentials for security
                local masked_value=$(echo "$value" | sed 's|://[^:]*:[^@]*@|://***:***@|')
                echo -e "   ${GREEN}✅ $var = $masked_value${NC}"
            elif [ "$var" = "JWT_SECRET" ]; then
                echo -e "   ${GREEN}✅ $var = [HIDDEN]${NC}"
            else
                echo -e "   ${GREEN}✅ $var = $value${NC}"
            fi
        else
            echo -e "   ${RED}❌ $var = [NOT SET]${NC}"
        fi
    done
    
    # Check .env file
    if [ -f ".env" ]; then
        echo -e "\n${GREEN}📄 .env file exists${NC}"
        local env_content=$(grep "^MONGO_URI=" .env 2>/dev/null)
        if [ -n "$env_content" ]; then
            local masked_content=$(echo "$env_content" | sed 's|://[^:]*:[^@]*@|://***:***@|')
            echo -e "   Found MONGO_URI in .env: $masked_content"
        else
            echo -e "   ${YELLOW}⚠️  MONGO_URI not found in .env file${NC}"
        fi
    else
        echo -e "\n${RED}❌ .env file not found${NC}"
    fi
}

# Function to test MongoDB connection
test_mongo_connection() {
    echo -e "\n${YELLOW}🔌 Testing MongoDB Connection...${NC}"
    
    if [ ! -f "test-mongo-connection.js" ]; then
        echo -e "   ${RED}❌ test-mongo-connection.js not found${NC}"
        return 1
    fi
    
    echo -e "   Running connection test script..."
    
    # Run the test and capture both stdout and stderr
    local output
    if output=$(node test-mongo-connection.js 2>&1); then
        echo -e "   ${GREEN}✅ MongoDB connection test passed!${NC}"
        echo -e "${GRAY}$output${NC}"
    else
        echo -e "   ${RED}❌ MongoDB connection test failed!${NC}"
        echo -e "${YELLOW}$output${NC}"
        return 1
    fi
}

# Function to create admin user
create_admin_user() {
    echo -e "\n${YELLOW}👑 Creating Admin User...${NC}"
    
    if [ ! -f "create-admin.js" ]; then
        echo -e "   ${RED}❌ create-admin.js not found${NC}"
        return 1
    fi
    
    echo -e "   Running enhanced admin creation script..."
    
    # Run the admin creation script
    local output
    if output=$(node create-admin.js 2>&1); then
        echo -e "   ${GREEN}✅ Admin user creation completed!${NC}"
        echo -e "${GRAY}$output${NC}"
    else
        echo -e "   ${RED}❌ Admin user creation failed!${NC}"
        echo -e "${YELLOW}$output${NC}"
        
        echo -e "\n${CYAN}🔧 Troubleshooting Suggestions:${NC}"
        echo -e "   ${NC}1. Verify your MONGO_URI includes authentication:${NC}"
        echo -e "      ${GRAY}********************************:port/database${NC}"
        echo -e "   ${NC}2. Check if the MongoDB user has readWrite permissions${NC}"
        echo -e "   ${NC}3. Ensure the database name in the URI is correct${NC}"
        echo -e "   ${NC}4. Verify the authentication database (authSource)${NC}"
        return 1
    fi
}

# Function to show usage
show_usage() {
    echo -e "\n${YELLOW}Usage:${NC}"
    echo -e "   ${NC}./mongo-troubleshoot.sh check-env          # Check environment variables${NC}"
    echo -e "   ${NC}./mongo-troubleshoot.sh test-connection    # Test MongoDB connection${NC}"
    echo -e "   ${NC}./mongo-troubleshoot.sh create-admin       # Create admin user${NC}"
    echo -e "   ${NC}./mongo-troubleshoot.sh all                # Run all tests${NC}"
}

# Function to show MongoDB URI formats
show_uri_formats() {
    echo -e "\n${CYAN}📚 Common MongoDB URI Formats:${NC}"
    echo -e "   ${GRAY}Local:     mongodb://localhost:27017/zakmakelaar${NC}"
    echo -e "   ${GRAY}Auth:      ***********************************************${NC}"
    echo -e "   ${GRAY}Cloud:     mongodb+srv://user:<EMAIL>/zakmakelaar${NC}"
    echo -e "   ${GRAY}AuthDB:    ***********************************************************${NC}"
}

# Main execution
case "${1:-help}" in
    "check-env")
        check_environment
        ;;
    "test-connection")
        test_mongo_connection
        ;;
    "create-admin")
        create_admin_user
        ;;
    "all")
        check_environment
        test_mongo_connection
        if [ $? -eq 0 ]; then
            create_admin_user
        else
            echo -e "\n${RED}❌ Skipping admin creation due to connection issues${NC}"
        fi
        ;;
    "help"|*)
        show_usage
        show_uri_formats
        exit 1
        ;;
esac

echo -e "\n${BLUE}=== Troubleshooting Complete ===${NC}"
show_uri_formats