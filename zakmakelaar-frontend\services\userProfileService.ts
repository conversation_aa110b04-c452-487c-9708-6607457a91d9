import { apiService, ApiResponse } from "./api";

export interface UserProfile {
  id: string;
  email: string;
  profile: {
    firstName?: string;
    lastName?: string;
    phoneNumber?: string;
    dateOfBirth?: Date;
    nationality?: string;
    userType?: string[];
    employment?: {
      occupation?: string;
      employmentType?: string;
      contractType?: string;
      employer?: string;
      workLocation?: string;
      monthlyIncome?: number;
    };
  };
  preferences: {
    minPrice?: number;
    maxPrice?: number;
    minRooms?: number;
    maxRooms?: number;
    propertyTypes?: string[];
    preferredLocations?: string[];
  };
  notifications: {
    email: {
      newListings: boolean;
      priceChanges: boolean;
      applicationUpdates: boolean;
    };
    push: {
      newMatches: boolean;
      messages: boolean;
      systemUpdates: boolean;
    };
  };
}

export interface AutoApplicationGuidance {
  nextSteps?: Array<{
    type: string;
    title: string;
    description?: string;
    priority?: "high" | "medium" | "low";
    fields?: string[];
    documents?: string[];
    action?: string;
  }>;
  warnings?: Array<{
    type: string;
    title: string;
    description?: string;
    severity?: "high" | "medium" | "low";
  }>;
  recommendations?: Array<{
    type: string;
    title: string;
    description?: string;
    action?: string;
  }>;
  completeness?: {
    personalInfo?: number;
    documents?: number;
    overall?: number;
    lastCalculated?: string;
  };
}

export interface AutoApplicationRequiredDocument {
  type: string;
  required: boolean;
  uploaded: boolean;
  verified?: boolean;
  documentId?: string | null;
  lastChecked?: string;
}

export interface AutoApplicationDocumentStatus {
  requiredDocuments: AutoApplicationRequiredDocument[];
  missingDocuments: string[];
  allRequiredUploaded: boolean;
  documentCompleteness: number;
}



class UserProfileService {

  /**
   * Get user profile
   */
  async getProfile(): Promise<ApiResponse<UserProfile>> {
    return apiService.get<UserProfile>('/user-profile');
  }

  /**
   * Update user profile
   */
  async updateProfile(profileData: Partial<UserProfile>): Promise<ApiResponse<UserProfile>> {
    return apiService.put<UserProfile>('/user-profile', profileData);
  }

  /**
   * Update only personal information
   */
  async updatePersonalInfo(personalInfo: UserProfile['profile']): Promise<ApiResponse<UserProfile>> {
    return apiService.put<UserProfile>('/user-profile', { profile: personalInfo });
  }

  /**
   * Update only preferences
   */
  async updatePreferences(preferences: UserProfile['preferences']): Promise<ApiResponse<UserProfile>> {
    return apiService.put<UserProfile>('/user-profile', { preferences });
  }

  /**
   * Update only notification settings
   */
  async updateNotifications(notifications: UserProfile['notifications']): Promise<ApiResponse<UserProfile>> {
    return apiService.put<UserProfile>('/user-profile', { notifications });
  }

  /**
   * Fetch auto-application readiness guidance (missing steps, warnings, etc.)
   */
  async getAutoApplicationGuidance(): Promise<ApiResponse<AutoApplicationGuidance>> {
    return apiService.get<AutoApplicationGuidance>("/user-profile/auto-application/guidance");
  }

  /**
   * Fetch document completion status required for auto-application
   */
  async getAutoApplicationDocumentStatus(): Promise<ApiResponse<AutoApplicationDocumentStatus>> {
    return apiService.get<AutoApplicationDocumentStatus>("/user-profile/auto-application/documents");
  }

  /**
   * Get profile completeness percentage
   */
  getProfileCompleteness(profile: UserProfile): number {
    let completedFields = 0;
    let totalFields = 0;

    // Personal information fields
    const personalFields = [
      'firstName',
      'lastName',
      'phoneNumber',
      'dateOfBirth',
      'nationality',
    ];
    
    personalFields.forEach(field => {
      totalFields++;
      if (profile.profile && profile.profile[field as keyof typeof profile.profile]) {
        completedFields++;
      }
    });

    // Employment fields
    const employmentFields = [
      'occupation',
      'employmentType',
      'contractType',
      'employer',
      'monthlyIncome',
    ];
    
    employmentFields.forEach(field => {
      totalFields++;
      if (profile.profile?.employment && profile.profile.employment[field as keyof typeof profile.profile.employment]) {
        completedFields++;
      }
    });

    // Preferences fields
    const preferenceFields = [
      'minPrice',
      'maxPrice',
      'minRooms',
      'maxRooms',
    ];
    
    preferenceFields.forEach(field => {
      totalFields++;
      if (profile.preferences && profile.preferences[field as keyof typeof profile.preferences]) {
        completedFields++;
      }
    });

    return Math.round((completedFields / totalFields) * 100);
  }

  /**
   * Get missing profile fields
   */
  getMissingFields(profile: UserProfile): string[] {
    const missing: string[] = [];

    // Check personal information
    if (!profile.profile?.firstName) missing.push('First Name');
    if (!profile.profile?.lastName) missing.push('Last Name');
    if (!profile.profile?.phoneNumber) missing.push('Phone Number');
    if (!profile.profile?.dateOfBirth) missing.push('Date of Birth');
    if (!profile.profile?.nationality) missing.push('Nationality');

    // Check employment
    if (!profile.profile?.employment?.occupation) missing.push('Occupation');
    if (!profile.profile?.employment?.employmentType) missing.push('Employment Type');
    if (!profile.profile?.employment?.monthlyIncome) missing.push('Monthly Income');

    // Check preferences
    if (!profile.preferences?.maxPrice) missing.push('Maximum Price');
    if (!profile.preferences?.minRooms) missing.push('Minimum Rooms');

    return missing;
  }

  /**
   * Validate profile data
   */
  validateProfile(profileData: Partial<UserProfile>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate personal information
    if (profileData.profile) {
      const { profile } = profileData;
      
      if (profile.firstName && profile.firstName.length < 1) {
        errors.push('First name is required');
      }
      
      if (profile.lastName && profile.lastName.length < 1) {
        errors.push('Last name is required');
      }
      
      if (profile.phoneNumber && !/^\+?[\d\s\-\(\)]+$/.test(profile.phoneNumber)) {
        errors.push('Invalid phone number format');
      }
      
      if (profile.employment?.monthlyIncome && profile.employment.monthlyIncome < 0) {
        errors.push('Monthly income cannot be negative');
      }
    }

    // Validate preferences
    if (profileData.preferences) {
      const { preferences } = profileData;
      
      if (preferences.minPrice && preferences.maxPrice && preferences.minPrice > preferences.maxPrice) {
        errors.push('Minimum price cannot be higher than maximum price');
      }
      
      if (preferences.minRooms && preferences.maxRooms && preferences.minRooms > preferences.maxRooms) {
        errors.push('Minimum rooms cannot be higher than maximum rooms');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Format profile data for display
   */
  formatProfileForDisplay(profile: UserProfile) {
    return {
      fullName: profile.profile?.firstName && profile.profile?.lastName 
        ? `${profile.profile.firstName} ${profile.profile.lastName}`
        : profile.profile?.firstName || profile.profile?.lastName || 'User',
      
      employmentStatus: profile.profile?.employment?.employmentType 
        ? profile.profile.employment.employmentType.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())
        : 'Not specified',
      
      monthlyIncomeFormatted: profile.profile?.employment?.monthlyIncome 
        ? `€${profile.profile.employment.monthlyIncome.toLocaleString()}`
        : 'Not specified',
      
      priceRange: profile.preferences?.minPrice && profile.preferences?.maxPrice
        ? `€${profile.preferences.minPrice} - €${profile.preferences.maxPrice}`
        : 'Not specified',
      
      roomRange: profile.preferences?.minRooms && profile.preferences?.maxRooms
        ? `${profile.preferences.minRooms} - ${profile.preferences.maxRooms} rooms`
        : 'Not specified',
    };
  }
}

export const userProfileService = new UserProfileService();
