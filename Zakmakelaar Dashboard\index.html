<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZakMakelaar - Real Estate Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.4.3/echarts.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pixi.js/7.3.2/pixi.min.js"></script>
    <script src="https://unpkg.com/splitting@1.0.6/dist/splitting.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/typed.js/2.0.12/typed.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/js/splide.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/splitting@1.0.6/dist/splitting.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Inter', sans-serif;
        }
        .hero-title {
            font-family: 'Playfair Display', serif;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #1a2332 0%, #2d3748 100%);
        }
        .card-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .card-hover:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        .stats-counter {
            font-size: 2.5rem;
            font-weight: 700;
            color: #4a90e2;
        }
        .particle-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        .content-overlay {
            position: relative;
            z-index: 2;
        }
        .typing-text {
            border-right: 2px solid #4a90e2;
            animation: blink 1s infinite;
        }
        @keyframes blink {
            0%, 50% { border-color: transparent; }
            51%, 100% { border-color: #4a90e2; }
        }
        .property-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
        }
        .filter-chip {
            transition: all 0.2s ease;
        }
        .filter-chip:hover {
            transform: scale(1.05);
        }
        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-gray-900 hero-title">ZakMakelaar</h1>
                    </div>
                </div>
                <div class="flex items-center space-x-8">
                    <a href="index.html" class="text-blue-600 font-medium border-b-2 border-blue-600 pb-1">Dashboard</a>
                    <a href="properties.html" class="text-gray-700 hover:text-blue-600 transition-colors">Properties</a>
                    <a href="analytics.html" class="text-gray-700 hover:text-blue-600 transition-colors">Analytics</a>
                    <div class="flex items-center space-x-4">
                        <button id="login-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            Login
                        </button>
                        <div id="user-menu" class="hidden items-center space-x-4">
                            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm font-medium" id="user-initials">JD</span>
                            </div>
                            <button id="logout-btn" class="text-gray-700 hover:text-blue-600 transition-colors">
                                Logout
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="gradient-bg relative overflow-hidden" style="height: 60vh;">
        <div class="particle-container" id="particles"></div>
        <div class="content-overlay max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-center">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center w-full">
                <div class="text-white">
                    <h1 class="text-5xl lg:text-6xl font-bold mb-6 hero-title">
                        <span id="typed-text"></span>
                    </h1>
                    <p class="text-xl text-gray-300 mb-8 leading-relaxed">
                        Advanced real estate analytics and property management platform with AI-powered insights, 
                        comprehensive market analysis, and seamless workflow automation.
                    </p>
                    <div class="flex space-x-4">
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors">
                            Explore Properties
                        </button>
                        <button class="border border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 rounded-lg font-medium transition-colors">
                            View Analytics
                        </button>
                    </div>
                </div>
                <div class="relative">
                    <img src="resources/hero-dashboard.png" alt="Real Estate Dashboard" class="rounded-lg shadow-2xl w-full h-auto">
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Stats -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-xl card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-blue-600 text-sm font-medium">Total Properties</p>
                            <p class="stats-counter" data-count="2847">0</p>
                        </div>
                        <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mt-4">
                        <span class="text-green-600 text-sm font-medium">+12.5%</span>
                        <span class="text-gray-600 text-sm">from last month</span>
                    </div>
                </div>

                <div class="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-xl card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-green-600 text-sm font-medium">Active Listings</p>
                            <p class="stats-counter" data-count="1923">0</p>
                        </div>
                        <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mt-4">
                        <span class="text-green-600 text-sm font-medium">+8.2%</span>
                        <span class="text-gray-600 text-sm">from last month</span>
                    </div>
                </div>

                <div class="bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-xl card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-purple-600 text-sm font-medium">Avg. Price</p>
                            <p class="stats-counter" data-count="425">€0K</p>
                        </div>
                        <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mt-4">
                        <span class="text-green-600 text-sm font-medium">+3.1%</span>
                        <span class="text-gray-600 text-sm">from last month</span>
                    </div>
                </div>

                <div class="bg-gradient-to-br from-orange-50 to-orange-100 p-6 rounded-xl card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-orange-600 text-sm font-medium">Applications</p>
                            <p class="stats-counter" data-count="847">0</p>
                        </div>
                        <div class="w-12 h-12 bg-orange-600 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mt-4">
                        <span class="text-green-600 text-sm font-medium">+15.7%</span>
                        <span class="text-gray-600 text-sm">from last month</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Property Search Interface -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Find Your Perfect Property</h2>
                <p class="text-gray-600 text-lg">Advanced search with AI-powered recommendations</p>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                        <select class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option>All Cities</option>
                            <option>Amsterdam</option>
                            <option>Rotterdam</option>
                            <option>The Hague</option>
                            <option>Utrecht</option>
                            <option>Eindhoven</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Property Type</label>
                        <select class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option>All Types</option>
                            <option>Apartment</option>
                            <option>House</option>
                            <option>Commercial</option>
                            <option>Studio</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
                        <div class="flex space-x-2">
                            <input type="number" placeholder="Min" class="w-1/2 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <input type="number" placeholder="Max" class="w-1/2 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>
                    <div class="flex items-end">
                        <button class="w-full bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-lg font-medium transition-colors">
                            Search Properties
                        </button>
                    </div>
                </div>

                <div class="mt-6 flex flex-wrap gap-3">
                    <span class="filter-chip bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium cursor-pointer">2+ Bedrooms</span>
                    <span class="filter-chip bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium cursor-pointer">Furnished</span>
                    <span class="filter-chip bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium cursor-pointer">Pets Allowed</span>
                    <span class="filter-chip bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium cursor-pointer">Parking</span>
                    <span class="filter-chip bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium cursor-pointer">Balcony</span>
                </div>
            </div>

            <!-- Property Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="property-grid">
                <!-- Property cards will be dynamically generated -->
            </div>
        </div>
    </section>

    <!-- Market Analytics -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Market Analytics</h2>
                <p class="text-gray-600 text-lg">Real-time insights and trends</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="chart-container p-6 rounded-xl shadow-lg">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Price Trends</h3>
                    <div id="price-trend-chart" style="height: 300px;"></div>
                </div>
                <div class="chart-container p-6 rounded-xl shadow-lg">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Property Types</h3>
                    <div id="property-types-chart" style="height: 300px;"></div>
                </div>
            </div>

            <div class="mt-8 grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div class="chart-container p-6 rounded-xl shadow-lg">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Market Distribution</h3>
                    <div id="market-distribution-chart" style="height: 250px;"></div>
                </div>
                <div class="chart-container p-6 rounded-xl shadow-lg lg:col-span-2">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Monthly Activity</h3>
                    <div id="activity-chart" style="height: 250px;"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Recent Activity -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Recent Activity</h2>
                <p class="text-gray-600 text-lg">Latest updates and notifications</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-6">New Listings</h3>
                    <div class="space-y-4" id="recent-listings">
                        <!-- Recent listings will be dynamically generated -->
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-6">System Alerts</h3>
                    <div class="space-y-4" id="system-alerts">
                        <!-- System alerts will be dynamically generated -->
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h3 class="text-2xl font-bold hero-title mb-4">ZakMakelaar</h3>
                <p class="text-gray-400 mb-6">Advanced real estate analytics and property management</p>
                <p class="text-gray-500 text-sm">© 2024 ZakMakelaar. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Login Modal -->
    <div id="login-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl shadow-2xl p-8 w-full max-w-md">
            <div class="text-center mb-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">Welcome to ZakMakelaar</h2>
                <p class="text-gray-600">Sign in to access your dashboard</p>
            </div>
            
            <form id="login-form">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <input type="email" id="email" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="<EMAIL>">
                </div>
                
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                    <input type="password" id="password" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="••••••••">
                </div>
                
                <div id="login-error" class="hidden mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p class="text-sm text-red-600"></p>
                </div>
                
                <div class="flex space-x-3">
                    <button type="button" id="cancel-login" class="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 py-3 px-4 rounded-lg font-medium transition-colors">
                        Cancel
                    </button>
                    <button type="submit" id="submit-login" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors">
                        Sign In
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="api-service.js"></script>
    <script src="main.js"></script>
</body>
</html>
