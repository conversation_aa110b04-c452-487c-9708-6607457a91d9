# Separate Browser Controls for Scraping and Auto-Application

## 🎛️ Overview

You now have **independent control** over the browser visibility for two different operations:

1. **🕷️ Scraping Operations** - Web scraping from rental websites (Funda, Pararius, etc.)
2. **🤖 Auto-Application Operations** - Automated form filling and application submission

Each operation can be configured separately to show/hide browsers, control speed, and display developer tools.

## 📋 Environment Variables

### Scraping Browser Controls
```env
# Scraping browser visibility (for web scraping operations)
SCRAPING_HEADLESS=false              # false = visible, true = hidden
SCRAPING_SLOW_MOTION=100             # Milliseconds delay between actions
SCRAPING_SHOW_DEVTOOLS=true          # Show developer tools panel
```

### Auto-Application Browser Controls
```env
# Auto-application browser visibility (for form automation)
AUTO_APPLICATION_HEADLESS=false      # false = visible, true = hidden
AUTO_APPLICATION_SLOW_MOTION=200     # Milliseconds delay between actions (slower for form filling)
AUTO_APPLICATION_SHOW_DEVTOOLS=true  # Show developer tools panel
```

### Demo Mode (affects both)
```env
DEMO_MODE=true                       # Enable demo mode features
```

## 🎬 Usage Scenarios

### 1. Show ONLY Scraping Browser
Perfect for debugging web scraping without auto-application distractions:
```env
SCRAPING_HEADLESS=false
AUTO_APPLICATION_HEADLESS=true
```

### 2. Show ONLY Auto-Application Browser
Perfect for testing form automation without scraping noise:
```env
SCRAPING_HEADLESS=true
AUTO_APPLICATION_HEADLESS=false
```

### 3. Show BOTH Browsers
Full visibility for comprehensive testing:
```env
SCRAPING_HEADLESS=false
AUTO_APPLICATION_HEADLESS=false
```

### 4. Hide BOTH Browsers
Production mode with no visual distractions:
```env
SCRAPING_HEADLESS=true
AUTO_APPLICATION_HEADLESS=true
```

## 🪟 Browser Positioning

The browsers appear at different positions to avoid overlap:

- **🕷️ Scraping Browser**: Position (100, 100) - Top-left area
- **🤖 Auto-Application Browser**: Position (200, 200) - Slightly offset

Both browsers use size: 1200x800 pixels when visible.

## ⚡ Speed Controls

Different operations benefit from different speeds:

- **Scraping**: `SCRAPING_SLOW_MOTION=100` (moderate speed for visibility)
- **Auto-Application**: `AUTO_APPLICATION_SLOW_MOTION=200` (slower for form reliability)

Set to `0` for maximum speed (no delays).

## 🛠️ Developer Tools

Developer tools can be controlled independently:

- **Scraping**: `SCRAPING_SHOW_DEVTOOLS=true` - Useful for debugging selectors
- **Auto-Application**: `AUTO_APPLICATION_SHOW_DEVTOOLS=true` - Useful for form inspection

DevTools only appear when the browser is visible (`*_HEADLESS=false`).

## 📝 Current Configuration

Your current `.env` settings:
```env
# Demo mode
DEMO_MODE=true

# Scraping browser
SCRAPING_HEADLESS=false
SCRAPING_SLOW_MOTION=100
SCRAPING_SHOW_DEVTOOLS=true

# Auto-application browser
AUTO_APPLICATION_HEADLESS=false
AUTO_APPLICATION_SLOW_MOTION=200
AUTO_APPLICATION_SHOW_DEVTOOLS=true
```

## 🚀 Testing Your Settings

### Test Scraping Browser
Run any scraping operation:
```bash
# Via API
curl -X POST http://localhost:3000/api/scraper/scrape/funda

# Via server start (auto-scraping every 5 minutes)
npm start
```

### Test Auto-Application Browser
Trigger auto-application:
```bash
# Enable auto-application for a user (via API)
# The browser will appear when applications are processed
```

### Monitor Console Output
Watch for these log messages:
```
🕷️  Scraping Browser: headless=false, devtools=true, slowMo=100ms
🎬 Demo Mode Active for Scraping

🤖 Auto-Application Browser: headless=false, devtools=true, slowMo=200ms
🎬 Demo Mode Active for Auto-Application
```

## 🔧 Advanced Configuration

### Production vs Development
The system automatically detects your environment:

- **Demo Mode (`DEMO_MODE=true`)**: Uses your explicit headless settings
- **Production Mode**: Defaults to headless unless explicitly set to `false`

### Performance Considerations
- **Visible browsers** use more system resources
- **Slow motion** increases operation time
- **DevTools** consume additional memory
- Consider hiding browsers in production for better performance

### Troubleshooting

#### Browser Not Appearing
1. Check `*_HEADLESS=false` in your `.env`
2. Ensure `DEMO_MODE=true`
3. Look for console messages confirming settings
4. Verify no firewall is blocking browser processes

#### Browser Crashes
1. Try increasing `*_SLOW_MOTION` values
2. Disable `*_SHOW_DEVTOOLS` temporarily
3. Check system resources (RAM/CPU)
4. Restart the application

#### Multiple Browser Windows
This is expected! You now have independent control:
- One window for scraping operations
- Another window for auto-application operations

## 🎯 Examples by Use Case

### Development & Debugging
```env
DEMO_MODE=true
SCRAPING_HEADLESS=false
SCRAPING_SLOW_MOTION=200
SCRAPING_SHOW_DEVTOOLS=true
AUTO_APPLICATION_HEADLESS=false
AUTO_APPLICATION_SLOW_MOTION=300
AUTO_APPLICATION_SHOW_DEVTOOLS=true
```

### Client Demonstrations
```env
DEMO_MODE=true
SCRAPING_HEADLESS=false
SCRAPING_SLOW_MOTION=150
SCRAPING_SHOW_DEVTOOLS=false
AUTO_APPLICATION_HEADLESS=false
AUTO_APPLICATION_SLOW_MOTION=250
AUTO_APPLICATION_SHOW_DEVTOOLS=false
```

### Production Deployment
```env
DEMO_MODE=false
SCRAPING_HEADLESS=true
AUTO_APPLICATION_HEADLESS=true
```

### Scraping-Only Testing
```env
DEMO_MODE=true
SCRAPING_HEADLESS=false
SCRAPING_SLOW_MOTION=100
AUTO_APPLICATION_HEADLESS=true
```

### Auto-Application-Only Testing
```env
DEMO_MODE=true
SCRAPING_HEADLESS=true
AUTO_APPLICATION_HEADLESS=false
AUTO_APPLICATION_SLOW_MOTION=200
```

## ✨ Benefits

1. **🎯 Focused Testing**: Show only the browser you're working on
2. **🔍 Better Debugging**: Different speeds for different operations
3. **💻 Resource Management**: Hide unused browsers to save system resources
4. **👥 Demo Flexibility**: Show relevant browsers for different audiences
5. **⚡ Performance Tuning**: Optimize speed independently for each operation
6. **🛠️ Development Workflow**: Separate concerns for better development experience

Your separate browser controls are now active and ready to use! 🎉