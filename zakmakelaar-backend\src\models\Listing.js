const mongoose = require("mongoose");

const parsePrice = (value) => {
  if (value === null || value === undefined) return null;
  if (typeof value === "number" && Number.isFinite(value)) {
    return Math.round(value);
  }
  if (typeof value !== "string") return null;

  const digitsOnly = value.replace(/[^\d]/g, "");
  if (!digitsOnly) return null;

  const numeric = parseInt(digitsOnly, 10);
  if (!Number.isFinite(numeric)) return null;
  return numeric;
};

const parseNumberField = (value) => {
  if (value === null || value === undefined) return null;
  if (typeof value === "number" && Number.isFinite(value)) {
    return value;
  }
  if (typeof value !== "string") return null;

  const match = value.match(/[\d.]+/);
  if (!match) return null;

  const numeric = parseFloat(match[0]);
  return Number.isFinite(numeric) ? numeric : null;
};

const applyNumericFields = (target) => {
  if (!target) return;

  if (Object.prototype.hasOwnProperty.call(target, "price")) {
    const computedPrice = parsePrice(target.price);
    target.priceNumeric = computedPrice;
  }

  if (Object.prototype.hasOwnProperty.call(target, "rooms")) {
    const roomsValue = parseNumberField(target.rooms);
    target.roomsNumeric = roomsValue;
  }

  if (Object.prototype.hasOwnProperty.call(target, "size")) {
    const sizeValue = parseNumberField(target.size);
    target.sizeNumeric = sizeValue;
  }
};

const listingSchema = new mongoose.Schema({
  title: { type: String, required: true },
  price: {
    type: String,
    required: true,
    // Price format: '? X.XXX per maand' with proper handling of European number formats
    // Supports various input formats like ?3,950 (thousands separator) and properly normalizes them
  },
  priceNumeric: { type: Number, index: true },
  location: { type: String, required: true },
  url: { type: String, required: true, unique: true },
  size: { type: String },
  sizeNumeric: { type: Number, index: true },
  bedrooms: { type: String },
  rooms: { type: String }, // Total number of rooms
  roomsNumeric: { type: Number, index: true },
  propertyType: { type: String },
  description: { type: String },
  year: { type: String }, // Build year
  interior: { type: String }, // Kaal, Gestoffeerd, Gemeubileerd
  source: { type: String }, // Which site the listing came from
  images: { type: [String], default: [] }, // Array of image URLs
  dateAdded: { type: Date, default: Date.now },
  timestamp: { type: Date, default: Date.now },
});

listingSchema.pre("save", function (next) {
  applyNumericFields(this);
  next();
});

listingSchema.pre("findOneAndUpdate", function (next) {
  const update = this.getUpdate();
  if (!update) return next();

  if (update.$set) {
    applyNumericFields(update.$set);
  } else {
    applyNumericFields(update);
  }

  next();
});

listingSchema.pre("insertMany", function (next, docs) {
  if (Array.isArray(docs)) {
    docs.forEach((doc) => applyNumericFields(doc));
  }
  next();
});

const Listing = mongoose.model("Listing", listingSchema);
Listing.parsePrice = parsePrice;
Listing.applyNumericFields = applyNumericFields;

module.exports = Listing;
