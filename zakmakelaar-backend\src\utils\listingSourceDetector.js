/**
 * Utility to detect listing source and determine appropriate submission method
 */

const { loggers } = require("../services/logger");

/**
 * Detects if a listing is from a scraped external site or the platform
 * @param {Object} listing - The listing object
 * @param {Object} applicationData - The application data containing URLs and source info
 * @returns {Object} Source detection result
 */
function detectListingSource(listing, applicationData) {
  const result = {
    isScrapedSite: false,
    isPlatformListing: false,
    source: 'unknown',
    requiresFormAutomation: false,
    platformHandler: null
  };

  // Check listing source field first
  if (listing && listing.source) {
    const source = listing.source.toLowerCase();
    
    // Known scraped sites
    const scrapedSites = ['funda', 'pararius', 'kamernet', 'huurwoningen'];
    if (scrapedSites.includes(source)) {
      result.isScrapedSite = true;
      result.source = source;
      result.requiresFormAutomation = true;
      result.platformHandler = getPlatformHandler(source);
      loggers.app.info(`Detected scraped site listing: ${source}`);
      return result;
    }

    // Platform listings
    if (source === 'zakmakelaar' || source === 'platform') {
      result.isPlatformListing = true;
      result.source = 'platform';
      result.requiresFormAutomation = false;
      loggers.app.info('Detected platform listing');
      return result;
    }
  }

  // Check listing URL if source field is not available
  if (listing && listing.url) {
    const url = listing.url.toLowerCase();
    
    if (url.includes('funda.nl')) {
      result.isScrapedSite = true;
      result.source = 'funda';
      result.requiresFormAutomation = true;
      result.platformHandler = 'handleFundaApplication';
    } else if (url.includes('pararius.nl')) {
      result.isScrapedSite = true;
      result.source = 'pararius';
      result.requiresFormAutomation = true;
      result.platformHandler = 'handlePariusApplication';
    } else if (url.includes('kamernet.nl')) {
      result.isScrapedSite = true;
      result.source = 'kamernet';
      result.requiresFormAutomation = true;
      result.platformHandler = 'handleKamernetApplication';
    } else if (url.includes('huurwoningen.nl')) {
      result.isScrapedSite = true;
      result.source = 'huurwoningen';
      result.requiresFormAutomation = true;
      result.platformHandler = 'handleHuurwoningenApplication';
    } else {
      // Default to platform listing for internal URLs
      result.isPlatformListing = true;
      result.source = 'platform';
      result.requiresFormAutomation = false;
    }
    
    loggers.app.info(`Detected listing source from URL: ${result.source}`);
    return result;
  }

  // Check application data URL as fallback
  if (applicationData && applicationData.listingUrl) {
    const url = applicationData.listingUrl.toLowerCase();
    
    if (url.includes('funda.nl')) {
      result.isScrapedSite = true;
      result.source = 'funda';
      result.requiresFormAutomation = true;
      result.platformHandler = 'handleFundaApplication';
    } else if (url.includes('pararius.nl')) {
      result.isScrapedSite = true;
      result.source = 'pararius';
      result.requiresFormAutomation = true;
      result.platformHandler = 'handlePariusApplication';
    } else if (url.includes('kamernet.nl')) {
      result.isScrapedSite = true;
      result.source = 'kamernet';
      result.requiresFormAutomation = true;
      result.platformHandler = 'handleKamernetApplication';
    } else if (url.includes('huurwoningen.nl')) {
      result.isScrapedSite = true;
      result.source = 'huurwoningen';
      result.requiresFormAutomation = true;
      result.platformHandler = 'handleHuurwoningenApplication';
    } else {
      result.isPlatformListing = true;
      result.source = 'platform';
      result.requiresFormAutomation = false;
    }
    
    loggers.app.info(`Detected listing source from application URL: ${result.source}`);
    return result;
  }

  // Default to platform listing if no clear indicators
  result.isPlatformListing = true;
  result.source = 'platform';
  result.requiresFormAutomation = false;
  
  loggers.app.warn('Could not determine listing source, defaulting to platform listing');
  return result;
}

/**
 * Gets the platform handler method name for a given source
 * @param {string} source - The listing source
 * @returns {string|null} Handler method name
 */
function getPlatformHandler(source) {
  const handlers = {
    'funda': 'handleFundaApplication',
    'pararius': 'handlePariusApplication', 
    'kamernet': 'handleKamernetApplication',
    'huurwoningen': 'handleHuurwoningenApplication'
  };
  
  return handlers[source.toLowerCase()] || null;
}

/**
 * Checks if a listing requires form automation
 * @param {Object} listing - The listing object
 * @param {Object} applicationData - The application data
 * @returns {boolean} True if form automation is required
 */
function requiresFormAutomation(listing, applicationData) {
  const detection = detectListingSource(listing, applicationData);
  return detection.requiresFormAutomation;
}

/**
 * Gets user settings for form automation from user data
 * @param {Object} user - User object from request
 * @returns {Object} User settings formatted for form automation
 */
function getUserSettingsForFormAutomation(user) {
  return {
    formData: {
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      email: user.email || '',
      phone: user.phone || '',
      age: user.age || '',
      monthlyIncome: user.income || user.monthlyIncome || '',
      occupation: user.occupation || '',
      additionalInfo: user.additionalInfo || ''
    },
    messageTemplates: user.messageTemplates || {},
    autoApplicationSettings: user.autoApplicationSettings || {}
  };
}

module.exports = {
  detectListingSource,
  getPlatformHandler,
  requiresFormAutomation,
  getUserSettingsForFormAutomation
};