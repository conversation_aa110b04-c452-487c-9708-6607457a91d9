import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SimpleFormInput } from '../SimpleFormInput';
import { propertyOwnerService } from '../../services/propertyOwnerService';

// Define types for tenant application data
interface TenantApplication {
  id: string;
  applicantName: string;
  applicantEmail: string;
  applicantPhone: string;
  propertyId: string;
  propertyAddress: string;
  applicationDate: string;
  status: 'pending' | 'approved' | 'rejected';
  creditScore?: number;
  incomeVerified: boolean;
  backgroundCheckPassed?: boolean;
  documents: {
    id: string;
    name: string;
    type: string;
    url: string;
  }[];
}

// Define props for the component
interface TenantScreeningInterfaceProps {
  propertyId?: string; // Optional - if provided, will filter applications for this property
}

// Transform backend application data to frontend format
const transformBackendApplication = (backendApp: any): TenantApplication => {
  return {
    id: backendApp._id || backendApp.id,
    applicantName: backendApp.applicantName || `${backendApp.firstName || ''} ${backendApp.lastName || ''}`.trim() || 'Unknown Applicant',
    applicantEmail: backendApp.applicantEmail || backendApp.email || 'No email provided',
    applicantPhone: backendApp.applicantPhone || backendApp.phone || 'No phone provided',
    propertyId: backendApp.propertyId || backendApp.property?._id || '',
    propertyAddress: backendApp.propertyAddress || 
                    (backendApp.property?.address ? 
                      `${backendApp.property.address.street} ${backendApp.property.address.houseNumber}, ${backendApp.property.address.city}` : 
                      'Address not available'),
    applicationDate: backendApp.applicationDate || backendApp.submittedAt || backendApp.createdAt || new Date().toISOString().split('T')[0],
    status: backendApp.status || 'pending',
    creditScore: backendApp.creditScore || backendApp.screening?.creditScore,
    incomeVerified: backendApp.incomeVerified || backendApp.screening?.incomeVerified || false,
    backgroundCheckPassed: backendApp.backgroundCheckPassed || backendApp.screening?.backgroundCheckPassed,
    documents: backendApp.documents || []
  };
};

export const TenantScreeningInterface: React.FC<TenantScreeningInterfaceProps> = ({ propertyId }) => {
  const [loading, setLoading] = useState(true);
  const [applications, setApplications] = useState<TenantApplication[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all');
  const [selectedApplication, setSelectedApplication] = useState<TenantApplication | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  const fetchApplications = useCallback(async () => {
    setLoading(true);
    try {
      console.log('🔄 Fetching tenant applications...');
      
      // Get all applications for the property owner
      const response = await propertyOwnerService.getApplications();
      
      if (response.success && response.data) {
        let applicationsData = Array.isArray(response.data) ? response.data : [];
        
        // Filter by propertyId if provided
        if (propertyId) {
          applicationsData = applicationsData.filter(app => 
            app.propertyId === propertyId || app.property?._id === propertyId
          );
        }
        
        const transformedApplications = applicationsData.map(transformBackendApplication);
        setApplications(transformedApplications);
      } else {
        setApplications([]);
      }
      
      console.log('✅ Applications fetched successfully');
    } catch (error: any) {
      console.error('❌ Error fetching applications:', error);
      Alert.alert('Error', error.message || 'Failed to fetch tenant applications');
      setApplications([]);
    } finally {
      setLoading(false);
    }
  }, [propertyId]);

  useEffect(() => {
    fetchApplications();
  }, [fetchApplications]);

  const handleSearch = (text: string) => {
    setSearchTerm(text);
  };

  const handleFilterChange = (status: 'all' | 'pending' | 'approved' | 'rejected') => {
    setStatusFilter(status);
  };

  const handleApplicationPress = (application: TenantApplication) => {
    setSelectedApplication(application);
    setShowDetails(true);
  };

  const handleApprove = (applicationId: string) => {
    Alert.alert(
      'Approve Application',
      'Are you sure you want to approve this tenant application?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Approve', 
          onPress: async () => {
            try {
              console.log('🟢 Approving application:', applicationId);
              
              const response = await propertyOwnerService.updateApplicationStatus(applicationId, 'approved');
              
              if (response.success || response.status === 'success') {
                // Update local state
                const updatedApplications = applications.map(app => 
                  app.id === applicationId ? { ...app, status: 'approved' as const } : app
                );
                setApplications(updatedApplications);
                
                if (selectedApplication?.id === applicationId) {
                  setSelectedApplication({ ...selectedApplication, status: 'approved' as const });
                }
                
                Alert.alert('Success', 'Application has been approved');
                console.log('✅ Application approved successfully');
              } else {
                throw new Error(response.message || 'Failed to approve application');
              }
            } catch (error: any) {
              console.error('❌ Error approving application:', error);
              Alert.alert('Error', error.message || 'Failed to approve application. Please try again.');
            }
          }
        }
      ]
    );
  };

  const handleReject = (applicationId: string) => {
    Alert.alert(
      'Reject Application',
      'Are you sure you want to reject this tenant application?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Reject', 
          onPress: async () => {
            try {
              console.log('🔴 Rejecting application:', applicationId);
              
              const response = await propertyOwnerService.updateApplicationStatus(applicationId, 'rejected');
              
              if (response.success || response.status === 'success') {
                // Update local state
                const updatedApplications = applications.map(app => 
                  app.id === applicationId ? { ...app, status: 'rejected' as const } : app
                );
                setApplications(updatedApplications);
                
                if (selectedApplication?.id === applicationId) {
                  setSelectedApplication({ ...selectedApplication, status: 'rejected' as const });
                }
                
                Alert.alert('Success', 'Application has been rejected');
                console.log('✅ Application rejected successfully');
              } else {
                throw new Error(response.message || 'Failed to reject application');
              }
            } catch (error: any) {
              console.error('❌ Error rejecting application:', error);
              Alert.alert('Error', error.message || 'Failed to reject application. Please try again.');
            }
          }
        }
      ]
    );
  };

  const handleViewDocument = (document: { name: string, url: string }) => {
    // In a real implementation, this would open the document in a viewer or browser
    Alert.alert(
      'View Document', 
      `Opening ${document.name}...`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Open', 
          onPress: () => {
            // Here you would typically use Linking.openURL(document.url) or a document viewer
            console.log('📄 Opening document:', document.url);
            Alert.alert('Document Viewer', 'Document viewer would open here in a real implementation.');
          }
        }
      ]
    );
  };

  const handleCloseDetails = () => {
    setShowDetails(false);
    setSelectedApplication(null);
  };

  const filteredApplications = applications.filter(app => {
    // Filter by search term
    const matchesSearch = app.applicantName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.applicantEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.propertyAddress.toLowerCase().includes(searchTerm.toLowerCase());
    
    // Filter by status
    const matchesStatus = statusFilter === 'all' || app.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#FFA500'; // Orange
      case 'approved': return '#4CAF50'; // Green
      case 'rejected': return '#F44336'; // Red
      default: return '#757575'; // Gray
    }
  };

  const renderApplicationItem = (application: TenantApplication) => {
    return (
      <TouchableOpacity 
        key={application.id} 
        style={styles.applicationItem}
        onPress={() => handleApplicationPress(application)}
      >
        <View style={styles.applicationHeader}>
          <Text style={styles.applicantName}>{application.applicantName}</Text>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(application.status) }]}>
            <Text style={styles.statusText}>{application.status.charAt(0).toUpperCase() + application.status.slice(1)}</Text>
          </View>
        </View>
        
        <Text style={styles.propertyAddress}>{application.propertyAddress}</Text>
        <Text style={styles.applicationDate}>Applied on: {application.applicationDate}</Text>
        
        <View style={styles.applicationFooter}>
          <Text style={styles.documentCount}>
            <Ionicons name="document-text-outline" size={14} /> {application.documents.length} document{application.documents.length !== 1 ? 's' : ''}
          </Text>
          <Text style={styles.viewDetails}>View Details <Ionicons name="chevron-forward" size={14} /></Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderApplicationDetails = () => {
    if (!selectedApplication) return null;
    
    return (
      <View style={styles.detailsContainer}>
        <View style={styles.detailsHeader}>
          <TouchableOpacity onPress={handleCloseDetails} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.detailsTitle}>Application Details</Text>
        </View>
        
        <ScrollView style={styles.detailsContent}>
          <View style={styles.detailsSection}>
            <Text style={styles.sectionTitle}>Applicant Information</Text>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Name:</Text>
              <Text style={styles.detailValue}>{selectedApplication.applicantName}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Email:</Text>
              <Text style={styles.detailValue}>{selectedApplication.applicantEmail}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Phone:</Text>
              <Text style={styles.detailValue}>{selectedApplication.applicantPhone}</Text>
            </View>
          </View>
          
          <View style={styles.detailsSection}>
            <Text style={styles.sectionTitle}>Property Information</Text>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Address:</Text>
              <Text style={styles.detailValue}>{selectedApplication.propertyAddress}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Application Date:</Text>
              <Text style={styles.detailValue}>{selectedApplication.applicationDate}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Status:</Text>
              <View style={[styles.statusBadge, { backgroundColor: getStatusColor(selectedApplication.status) }]}>
                <Text style={styles.statusText}>
                  {selectedApplication.status.charAt(0).toUpperCase() + selectedApplication.status.slice(1)}
                </Text>
              </View>
            </View>
          </View>
          
          <View style={styles.detailsSection}>
            <Text style={styles.sectionTitle}>Screening Results</Text>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Credit Score:</Text>
              <Text style={[
                styles.detailValue, 
                selectedApplication.creditScore && selectedApplication.creditScore < 650 ? styles.negativeValue : styles.positiveValue
              ]}>
                {selectedApplication.creditScore || 'Not available'}
              </Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Income Verified:</Text>
              <Text style={[
                styles.detailValue, 
                selectedApplication.incomeVerified ? styles.positiveValue : styles.negativeValue
              ]}>
                {selectedApplication.incomeVerified ? 'Yes' : 'No'}
              </Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Background Check:</Text>
              <Text style={[
                styles.detailValue, 
                selectedApplication.backgroundCheckPassed === undefined ? styles.neutralValue :
                selectedApplication.backgroundCheckPassed ? styles.positiveValue : styles.negativeValue
              ]}>
                {selectedApplication.backgroundCheckPassed === undefined ? 'Pending' : 
                 selectedApplication.backgroundCheckPassed ? 'Passed' : 'Failed'}
              </Text>
            </View>
          </View>
          
          <View style={styles.detailsSection}>
            <Text style={styles.sectionTitle}>Documents</Text>
            {selectedApplication.documents.map(doc => (
              <TouchableOpacity 
                key={doc.id} 
                style={styles.documentItem}
                onPress={() => handleViewDocument(doc)}
              >
                <Ionicons name="document-text-outline" size={24} color="#007AFF" />
                <View style={styles.documentInfo}>
                  <Text style={styles.documentName}>{doc.name}</Text>
                  <Text style={styles.documentType}>{doc.type}</Text>
                </View>
                <Ionicons name="eye-outline" size={20} color="#007AFF" />
              </TouchableOpacity>
            ))}
          </View>
          
          {selectedApplication.status === 'pending' && (
            <View style={styles.actionButtons}>
              <TouchableOpacity 
                style={[styles.actionButton, styles.rejectButton]}
                onPress={() => handleReject(selectedApplication.id)}
              >
                <Text style={styles.rejectButtonText}>Reject</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.actionButton, styles.approveButton]}
                onPress={() => handleApprove(selectedApplication.id)}
              >
                <Text style={styles.approveButtonText}>Approve</Text>
              </TouchableOpacity>
            </View>
          )}
        </ScrollView>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <SimpleFormInput
          label="Search applications"
          icon="search"
          value={searchTerm}
          onChangeText={handleSearch}
          containerStyle={styles.searchInput}
        />
      </View>
      
      <View style={styles.filterContainer}>
        <TouchableOpacity 
          style={[styles.filterButton, statusFilter === 'all' && styles.activeFilter]}
          onPress={() => handleFilterChange('all')}
        >
          <Text style={[styles.filterText, statusFilter === 'all' && styles.activeFilterText]}>All</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterButton, statusFilter === 'pending' && styles.activeFilter]}
          onPress={() => handleFilterChange('pending')}
        >
          <Text style={[styles.filterText, statusFilter === 'pending' && styles.activeFilterText]}>Pending</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterButton, statusFilter === 'approved' && styles.activeFilter]}
          onPress={() => handleFilterChange('approved')}
        >
          <Text style={[styles.filterText, statusFilter === 'approved' && styles.activeFilterText]}>Approved</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterButton, statusFilter === 'rejected' && styles.activeFilter]}
          onPress={() => handleFilterChange('rejected')}
        >
          <Text style={[styles.filterText, statusFilter === 'rejected' && styles.activeFilterText]}>Rejected</Text>
        </TouchableOpacity>
      </View>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading applications...</Text>
        </View>
      ) : filteredApplications.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="document-text-outline" size={64} color="#CCCCCC" />
          <Text style={styles.emptyText}>No applications found</Text>
          <Text style={styles.emptySubtext}>
            {searchTerm || statusFilter !== 'all' 
              ? 'Try adjusting your search or filters'
              : 'When tenants apply for your properties, they will appear here'}
          </Text>
        </View>
      ) : (
        <ScrollView style={styles.applicationsList}>
          {filteredApplications.map(renderApplicationItem)}
        </ScrollView>
      )}
      
      {showDetails && renderApplicationDetails()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  searchContainer: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  searchInput: {
    marginBottom: 0,
  },
  filterContainer: {
    flexDirection: 'row',
    padding: 8,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  filterButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginHorizontal: 4,
    borderRadius: 16,
    backgroundColor: '#F0F0F0',
  },
  activeFilter: {
    backgroundColor: '#007AFF',
  },
  filterText: {
    fontSize: 14,
    color: '#666666',
  },
  activeFilterText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666666',
    marginTop: 8,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  applicationsList: {
    flex: 1,
  },
  applicationItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginHorizontal: 16,
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  applicationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  applicantName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  propertyAddress: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  applicationDate: {
    fontSize: 12,
    color: '#999999',
    marginBottom: 12,
  },
  applicationFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
    paddingTop: 12,
  },
  documentCount: {
    fontSize: 12,
    color: '#666666',
  },
  viewDetails: {
    fontSize: 12,
    color: '#007AFF',
  },
  detailsContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#FFFFFF',
    zIndex: 1000,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  detailsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  closeButton: {
    padding: 4,
  },
  detailsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginLeft: 12,
  },
  detailsContent: {
    flex: 1,
    padding: 16,
  },
  detailsSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    paddingBottom: 8,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 8,
    alignItems: 'center',
  },
  detailLabel: {
    width: 120,
    fontSize: 14,
    color: '#666666',
  },
  detailValue: {
    flex: 1,
    fontSize: 14,
    color: '#333333',
  },
  positiveValue: {
    color: '#4CAF50',
  },
  negativeValue: {
    color: '#F44336',
  },
  neutralValue: {
    color: '#FFA500',
  },
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    marginBottom: 8,
  },
  documentInfo: {
    flex: 1,
    marginLeft: 12,
  },
  documentName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
  },
  documentType: {
    fontSize: 12,
    color: '#666666',
    textTransform: 'capitalize',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    marginBottom: 32,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  approveButton: {
    backgroundColor: '#4CAF50',
    marginLeft: 8,
  },
  rejectButton: {
    backgroundColor: '#F44336',
    marginRight: 8,
  },
  approveButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 16,
  },
  rejectButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 16,
  },
});
