const { loggers } = require("./logger");

const autoApplicationNotificationService = () => {
  try {
    return require("./autoApplicationNotificationService");
  } catch (error) {
    loggers.app.debug("Auto-application notification service unavailable", {
      error: error.message,
    });
    return null;
  }
};

const websocketService = () => {
  try {
    return require("./websocketService");
  } catch (error) {
    loggers.app.debug("WebSocket service unavailable", {
      error: error.message,
    });
    return null;
  }
};

const User = require("../models/User");

const parseList = (value) =>
  value
    .split(",")
    .map((item) => item.trim())
    .filter(Boolean);

const configuredRecipients = process.env.OBSERVABILITY_ALERT_USER_IDS
  ? parseList(process.env.OBSERVABILITY_ALERT_USER_IDS)
  : null;

const recipientCache = {
  ids: null,
  expiresAt: 0,
};

const CACHE_TTL =
  parseInt(process.env.OBSERVABILITY_ALERT_RECIPIENT_CACHE_SECONDS, 10) || 900;

const buildAlertPayload = (title, message, context = {}) => ({
  title,
  message,
  timestamp: new Date().toISOString(),
  severity: context.severity || "warning",
  context,
});

const fetchAdminRecipients = async () => {
  if (configuredRecipients && configuredRecipients.length > 0) {
    return configuredRecipients;
  }

  const now = Date.now();
  if (recipientCache.ids && recipientCache.expiresAt > now) {
    return recipientCache.ids;
  }

  const admins = await User.find({ role: "admin" })
    .select("_id")
    .lean()
    .exec();

  const adminIds = admins.map((admin) => admin._id.toString());

  recipientCache.ids = adminIds;
  recipientCache.expiresAt = now + CACHE_TTL * 1000;

  return adminIds;
};

const dispatchAlert = async (payload) => {
  const recipients = await fetchAdminRecipients();
  if (!recipients || recipients.length === 0) {
    loggers.app.warn("No recipients available for observability alert", payload);
    return;
  }

  const websocket = websocketService();
  if (websocket && typeof websocket.sendSystemAlert === "function") {
    recipients.forEach((userId) => {
      try {
        websocket.sendSystemAlert(userId, payload);
      } catch (error) {
        loggers.app.error("Failed to dispatch WebSocket observability alert", {
          error: error.message,
        });
      }
    });
  }

  const notificationService = autoApplicationNotificationService();
  if (notificationService && notificationService.sendUrgentAlert) {
    await Promise.allSettled(
      recipients.map((userId) =>
        notificationService.sendUrgentAlert(userId, "system_error", payload)
      )
    );
  }
};

const notifyScraperDegradation = async ({ site, error }) => {
  const payload = buildAlertPayload(
    "Scraper degradation detected",
    `Scraper for ${site} is reporting failures.`,
    {
      type: "scraper_degradation",
      site,
      error,
      severity: "warning",
    }
  );

  loggers.app.warn("Scraper degradation alert raised", payload);
  await dispatchAlert(payload);
};

const notifyAiDegradation = async ({ operation, status, error }) => {
  const payload = buildAlertPayload(
    "AI degradation detected",
    `AI operation ${operation} reported status ${status}.`,
    {
      type: "ai_degradation",
      operation,
      status,
      error,
      severity: status === "error" ? "critical" : "warning",
    }
  );

  loggers.app.warn("AI degradation alert raised", payload);
  await dispatchAlert(payload);
};

module.exports = {
  notifyScraperDegradation,
  notifyAiDegradation,
};
