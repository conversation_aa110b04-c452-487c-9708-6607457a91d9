import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Dimensions,
  Animated,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useAuthStore } from '../../store/authStore';
import { propertyOwnerService } from '../../services/propertyOwnerService';
import { PropertyCard } from './PropertyCard';
import { Avatar } from '../ui/Avatar';
import { FloatingActionButton } from '../ui/FloatingActionButton';
import { Theme } from '../../constants/Theme';

const { width } = Dimensions.get('window');

interface PropertyOwnerDashboardData {
  owner: {
    id: string;
    name: string;
    email: string;
    isVerified: boolean;
    totalProperties: number;
    totalApplications: number;
    occupancyRate: number;
  };
  properties: any[];
  applications: any[];
  recentActivity: any[];
}

export const PropertyOwnerDashboardScreen: React.FC = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dashboardData, setDashboardData] = useState<PropertyOwnerDashboardData | null>(null);
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [filteredProperties, setFilteredProperties] = useState<any[]>([]);
  
  // Animation values
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const slideAnim = React.useRef(new Animated.Value(50)).current;

  const loadDashboardData = useCallback(async () => {
    try {
      const data = await propertyOwnerService.getDashboardData();
      setDashboardData(data);
      setFilteredProperties(data.properties || []);
      
      // Trigger entrance animations
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: Theme.animation.slow,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: Theme.animation.slow,
          useNativeDriver: true,
        }),
      ]).start();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  }, [fadeAnim, slideAnim]);

  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  }, [loadDashboardData]);

  // Filter properties based on selected filter
  const filterProperties = useCallback((filter: string, properties: any[]) => {
    switch (filter) {
      case 'active':
        return properties.filter(p => p.status === 'active');
      case 'draft':
        return properties.filter(p => p.status === 'draft');
      case 'rented':
        return properties.filter(p => p.status === 'rented');
      case 'applications':
        return properties.filter(p => p.applicationsCount > 0);
      default:
        return properties;
    }
  }, []);

  // Handle filter selection
  const handleFilterSelect = useCallback((filter: string) => {
    setSelectedFilter(filter);
    if (dashboardData?.properties) {
      const filtered = filterProperties(filter, dashboardData.properties);
      setFilteredProperties(filtered);
    }
  }, [dashboardData, filterProperties]);

  const handleAddProperty = () => {
    router.push('/property-owner/add-property' as any);
  };

  const handlePropertyPress = (property: any) => {
    router.push(`/property-owner/property/${property.id}` as any);
  };

  const handleApplicationPress = (application: any) => {
    router.push(`/property-owner/application/${application.id}` as any);
  };


  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <View style={styles.loadingContent}>
          <Ionicons name="home" size={48} color={Theme.colors.primary} />
          <Text style={styles.loadingTitle}>Loading Dashboard</Text>
          <Text style={styles.loadingSubtitle}>Please wait...</Text>
        </View>
      </View>
    );
  }

  if (!dashboardData) {
    return (
      <View style={styles.errorContainer}>
        <View style={styles.errorContent}>
          <Ionicons name="alert-circle" size={64} color={Theme.colors.error} />
          <Text style={styles.errorTitle}>Dashboard Unavailable</Text>
          <Text style={styles.errorSubtitle}>Unable to load your property owner dashboard</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadDashboardData}>
            <Ionicons name="refresh" size={20} color={Theme.colors.textInverse} />
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  const { owner, properties = [] } = dashboardData;
  
  // Debug logging
  console.log('🔍 Dashboard Debug:', {
    selectedFilter,
    propertiesLength: properties.length,
    filteredPropertiesLength: filteredProperties.length,
    dashboardDataExists: !!dashboardData,
    ownerName: owner?.name,
  });
  console.log('🔥 FILTER BAR SHOULD BE RENDERING NOW!');
  console.log('🔥 Properties array:', properties);
  
  // Get current time greeting
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  return (
    <View style={styles.container}>
      {/* Modern Header with Avatar */}
      <Animated.View
        style={[
          styles.header,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        <View style={styles.headerContent}>
          <Avatar
            name={user?.name || owner?.name || 'Property Owner'}
            size="large"
            showBadge={owner?.isVerified}
            badgeColor={Theme.colors.success}
          />
          <View style={styles.headerText}>
            <Text style={styles.greeting}>
              {getGreeting()},
            </Text>
            <Text style={styles.ownerName}>
              {user?.name || owner?.name || 'Property Owner'}
            </Text>
          </View>
        </View>
      </Animated.View>

      {/* SIMPLE FILTER BAR TEST */}
      <View style={{
        height: 100,
        backgroundColor: 'red',
        margin: 20,
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <Text style={{ color: 'white', fontSize: 20, fontWeight: 'bold' }}>
          FILTER BAR TEST - Current: {selectedFilter}
        </Text>
        <View style={{ flexDirection: 'row', gap: 10, marginTop: 10 }}>
          <TouchableOpacity
            style={{
              padding: 10,
              backgroundColor: selectedFilter === 'all' ? 'white' : 'transparent',
              borderRadius: 5,
            }}
            onPress={() => {
              console.log('🔥 ALL pressed');
              setSelectedFilter('all');
              setFilteredProperties(properties);
            }}
          >
            <Text style={{ color: selectedFilter === 'all' ? 'red' : 'white' }}>ALL</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={{
              padding: 10,
              backgroundColor: selectedFilter === 'draft' ? 'white' : 'transparent',
              borderRadius: 5,
            }}
            onPress={() => {
              console.log('🔥 DRAFT pressed');
              setSelectedFilter('draft');
              setFilteredProperties(properties.filter(p => p.status === 'draft'));
            }}
          >
            <Text style={{ color: selectedFilter === 'draft' ? 'red' : 'white' }}>DRAFT</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={{
              padding: 10,
              backgroundColor: selectedFilter === 'active' ? 'white' : 'transparent',
              borderRadius: 5,
            }}
            onPress={() => {
              console.log('🔥 ACTIVE pressed');
              setSelectedFilter('active');
              setFilteredProperties(properties.filter(p => p.status === 'active'));
            }}
          >
            <Text style={{ color: selectedFilter === 'active' ? 'red' : 'white' }}>ACTIVE</Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={Theme.colors.primary}
            colors={[Theme.colors.primary]}
          />
        }
      >

        {/* Properties Section */}
        <View style={styles.propertiesSection}>
          {/* Section Header */}
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>
              {selectedFilter === 'all' 
                ? 'All Properties' 
                : `${selectedFilter.charAt(0).toUpperCase() + selectedFilter.slice(1)} Properties`
              } ({filteredProperties.length})
            </Text>
            {selectedFilter !== 'all' && (
              <TouchableOpacity 
                style={styles.clearFilterButton}
                onPress={() => handleFilterSelect('all')}
              >
                <Text style={styles.clearFilterText}>Clear Filter</Text>
              </TouchableOpacity>
            )}
          </View>
          {filteredProperties.length === 0 ? (
            <View style={styles.emptyState}>
              <View style={styles.emptyStateContent}>
                <Ionicons 
                  name={selectedFilter === 'all' ? 'home-outline' : 'filter-outline'} 
                  size={64} 
                  color={Theme.colors.neutral[400]} 
                />
                <Text style={styles.emptyStateTitle}>
                  {selectedFilter === 'all' 
                    ? 'No Properties Yet' 
                    : `No ${selectedFilter} properties`
                  }
                </Text>
                <Text style={styles.emptyStateSubtitle}>
                  {selectedFilter === 'all'
                    ? 'Start by adding your first property'
                    : `You don't have any ${selectedFilter} properties at the moment`
                  }
                </Text>
                
                <View style={styles.emptyStateActions}>
                  {selectedFilter !== 'all' && (
                    <TouchableOpacity 
                      style={[styles.emptyStateButton, styles.secondaryButton]} 
                      onPress={() => handleFilterSelect('all')}
                    >
                      <Ionicons name="apps" size={20} color={Theme.colors.primary} />
                      <Text style={[styles.emptyStateButtonText, styles.secondaryButtonText]}>View All Properties</Text>
                    </TouchableOpacity>
                  )}
                  
                  {selectedFilter === 'all' && (
                    <TouchableOpacity style={styles.emptyStateButton} onPress={handleAddProperty}>
                      <Ionicons name="add" size={20} color={Theme.colors.textInverse} />
                      <Text style={styles.emptyStateButtonText}>Add Property</Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            </View>
          ) : (
            <View style={styles.propertiesList}>
              {filteredProperties.map((property, index) => (
                <Animated.View
                  key={property.id}
                  style={{
                    opacity: fadeAnim,
                    transform: [{
                      translateY: Animated.add(
                        slideAnim,
                        new Animated.Value(index * 10)
                      )
                    }],
                  }}
                >
                  <PropertyCard
                    property={property}
                    onPress={() => handlePropertyPress(property)}
                    onEdit={() => router.push(`/property-owner/edit-property/${property.id}` as any)}
                    onDelete={() => {
                      Alert.alert(
                        'Delete Property',
                        'Are you sure you want to delete this property?',
                        [
                          { text: 'Cancel', style: 'cancel' },
                          { 
                            text: 'Delete', 
                            style: 'destructive',
                            onPress: () => {
                              // Handle delete
                            }
                          },
                        ]
                      );
                    }}
                  />
                </Animated.View>
              ))}
            </View>
          )}
        </View>

      </ScrollView>
      
      {/* Floating Action Button */}
      <FloatingActionButton
        icon="add"
        label="Add Property"
        onPress={handleAddProperty}
        position="bottom-right"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  
  // Loading State
  loadingContainer: {
    flex: 1,
    backgroundColor: Theme.colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContent: {
    alignItems: 'center',
    padding: Theme.spacing['2xl'],
  },
  loadingTitle: {
    fontSize: Theme.typography.fontSize['2xl'],
    fontWeight: Theme.typography.fontWeight.bold,
    color: Theme.colors.textPrimary,
    marginTop: Theme.spacing.base,
    marginBottom: Theme.spacing.xs,
  },
  loadingSubtitle: {
    fontSize: Theme.typography.fontSize.base,
    color: Theme.colors.textSecondary,
  },
  
  // Error State
  errorContainer: {
    flex: 1,
    backgroundColor: Theme.colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContent: {
    alignItems: 'center',
    padding: Theme.spacing['2xl'],
    maxWidth: 300,
  },
  errorTitle: {
    fontSize: Theme.typography.fontSize['2xl'],
    fontWeight: Theme.typography.fontWeight.bold,
    color: Theme.colors.textPrimary,
    marginTop: Theme.spacing.lg,
    marginBottom: Theme.spacing.sm,
    textAlign: 'center',
  },
  errorSubtitle: {
    fontSize: Theme.typography.fontSize.base,
    color: Theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: Theme.spacing.xl,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Theme.colors.primary,
    paddingHorizontal: Theme.spacing.xl,
    paddingVertical: Theme.spacing.md,
    borderRadius: Theme.borderRadius.base,
    gap: Theme.spacing.sm,
  },
  retryButtonText: {
    fontSize: Theme.typography.fontSize.base,
    fontWeight: Theme.typography.fontWeight.semiBold,
    color: Theme.colors.textInverse,
  },
  
  // Header
  header: {
    backgroundColor: Theme.colors.surface,
    paddingHorizontal: Theme.spacing.xl,
    paddingTop: Theme.spacing['2xl'],
    paddingBottom: Theme.spacing['2xl'],
    borderBottomWidth: 1,
    borderBottomColor: Theme.colors.borderLight,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Theme.spacing.base,
  },
  headerText: {
    flex: 1,
  },
  greeting: {
    fontSize: Theme.typography.fontSize.lg,
    color: Theme.colors.textSecondary,
    marginBottom: Theme.spacing.xs,
  },
  ownerName: {
    fontSize: Theme.typography.fontSize['2xl'],
    fontWeight: Theme.typography.fontWeight.bold,
    color: Theme.colors.textPrimary,
  },
  
  
  // Filters
  filtersContainer: {
    backgroundColor: Theme.colors.surface,
    paddingHorizontal: Theme.spacing.xl,
    paddingVertical: Theme.spacing.xl,
    marginBottom: Theme.spacing.base,
    borderBottomWidth: 2,
    borderBottomColor: Theme.colors.primary,
    minHeight: 120, // Force large height
    borderTopWidth: 2,
    borderTopColor: Theme.colors.primary,
  },
  filtersLabel: {
    fontSize: Theme.typography.fontSize.sm,
    fontWeight: Theme.typography.fontWeight.medium,
    color: Theme.colors.textSecondary,
    marginBottom: Theme.spacing.sm,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  simpleFiltersRow: {
    flexDirection: 'row',
    gap: Theme.spacing.sm,
  },
  simpleFilterButton: {
    flex: 1,
    paddingVertical: Theme.spacing.sm,
    paddingHorizontal: Theme.spacing.sm,
    borderRadius: Theme.borderRadius.base,
    backgroundColor: Theme.colors.surface,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Theme.colors.borderLight,
    minHeight: 50,
    justifyContent: 'center',
  },
  simpleFilterText: {
    fontSize: Theme.typography.fontSize.sm,
    fontWeight: Theme.typography.fontWeight.semiBold,
    marginBottom: 2,
  },
  simpleFilterCount: {
    fontSize: Theme.typography.fontSize.xs,
    fontWeight: Theme.typography.fontWeight.bold,
  },
  
  // Properties
  propertiesSection: {
    flex: 1,
    paddingHorizontal: Theme.spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.base,
  },
  sectionTitle: {
    fontSize: Theme.typography.fontSize.lg,
    fontWeight: Theme.typography.fontWeight.bold,
    color: Theme.colors.textPrimary,
  },
  clearFilterButton: {
    paddingHorizontal: Theme.spacing.sm,
    paddingVertical: Theme.spacing.xs,
  },
  clearFilterText: {
    fontSize: Theme.typography.fontSize.sm,
    color: Theme.colors.primary,
    fontWeight: Theme.typography.fontWeight.medium,
  },
  propertiesList: {
    gap: Theme.spacing.base,
    paddingBottom: 100, // Space for FAB
  },
  
  // Empty State
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 300,
  },
  emptyStateContent: {
    alignItems: 'center',
    padding: Theme.spacing.xl,
    maxWidth: 280,
  },
  emptyStateTitle: {
    fontSize: Theme.typography.fontSize.xl,
    fontWeight: Theme.typography.fontWeight.bold,
    color: Theme.colors.textPrimary,
    marginTop: Theme.spacing.lg,
    marginBottom: Theme.spacing.sm,
    textAlign: 'center',
  },
  emptyStateSubtitle: {
    fontSize: Theme.typography.fontSize.base,
    color: Theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: Theme.typography.lineHeight.relaxed,
    marginBottom: Theme.spacing.xl,
  },
  emptyStateActions: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: Theme.spacing.sm,
  },
  emptyStateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Theme.colors.primary,
    paddingHorizontal: Theme.spacing.xl,
    paddingVertical: Theme.spacing.md,
    borderRadius: Theme.borderRadius.base,
    gap: Theme.spacing.sm,
  },
  secondaryButton: {
    backgroundColor: Theme.colors.surface,
    borderWidth: 1,
    borderColor: Theme.colors.primary,
  },
  emptyStateButtonText: {
    fontSize: Theme.typography.fontSize.base,
    fontWeight: Theme.typography.fontWeight.semiBold,
    color: Theme.colors.textInverse,
  },
  secondaryButtonText: {
    color: Theme.colors.primary,
  },
});
