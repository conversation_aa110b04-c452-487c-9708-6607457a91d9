const {
  scrapePararius,
  scrapeFunda,
  scrapeHuurwoningen,
  cleanup,
} = require("./scraper");
const { loggers } = require("./logger");

class ScraperManager {
  constructor() {
    this.isRunning = false;
    this.currentTask = null;
    this.scrapers = {
      pararius: scrapePararius,
      funda: scrapeFunda,
      huurwoningen: scrapeHuurwoningen,
    };
    this.config = {
      activeScrapers: ["funda"],
      scrapeInterval: 3600000, // 1 hour
      maxRetries: 3,
      timeout: 1200000, // 20 minutes
    };
    this.intervalId = null;
  }

  async start() {
    if (this.isRunning) {
      throw new Error("Scraper is already running");
    }

    this.isRunning = true;
    await this.runScrapers();

    // Set up interval for periodic scraping
    this.intervalId = setInterval(async () => {
      if (!this.isRunning) return;
      await this.runScrapers();
    }, this.config.scrapeInterval);

    loggers.info("Scraper manager started");
  }

  async stop() {
    if (!this.isRunning) {
      throw new Error("<PERSON>raper is not running");
    }

    this.isRunning = false;

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    // Clean up any running browser instances
    await cleanup();

    loggers.info("Scraper manager stopped");
  }

  async updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };

    // Restart the scraper if interval changed
    if (newConfig.scrapeInterval && this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = setInterval(async () => {
        if (!this.isRunning) return;
        await this.runScrapers();
      }, this.config.scrapeInterval);
    }

    loggers.info("Scraper configuration updated", { config: this.config });
  }

  async runScrapers() {
    if (this.currentTask) {
      loggers.warn("Scraping already in progress, skipping this cycle");
      return;
    }

    try {
      this.currentTask = "scraping";
      loggers.info("Starting scraping cycle");

      // Run all active scrapers in parallel
      const scraperPromises = this.config.activeScrapers
        .filter((name) => this.scrapers[name])
        .map((name) =>
          this.scrapers[name]().catch((error) => {
            loggers.error(`Error in ${name} scraper`, { error });
            return { success: false, scraper: name, error: error.message };
          })
        );

      const results = await Promise.all(scraperPromises);
      loggers.info("Scraping cycle completed", { results });

      return results;
    } catch (error) {
      loggers.error("Error in scraping cycle", { error });
      throw error;
    } finally {
      this.currentTask = null;
    }
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      currentTask: this.currentTask,
      config: this.config,
      lastRun: this.lastRun,
    };
  }
}

// Singleton instance
const scraperManager = new ScraperManager();

// Export functions that use the singleton
const startScraping = async () => {
  await scraperManager.start();
  return scraperManager.getStatus();
};

const stopScraping = async () => {
  await scraperManager.stop();
  return { isRunning: false };
};

const updateScraperConfig = (config) => {
  return scraperManager.updateConfig(config);
};

const getScraperStatus = () => {
  return scraperManager.getStatus();
};

// Handle process termination
process.on("SIGINT", async () => {
  if (scraperManager.isRunning) {
    await scraperManager.stop();
  }
  process.exit(0);
});

module.exports = {
  startScraping,
  stopScraping,
  updateScraperConfig,
  getScraperStatus,
  scraperManager, // Export for testing
};
