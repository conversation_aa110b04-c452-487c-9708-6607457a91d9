import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";

interface MetricData {
  label: string;
  value: string;
  trend: "up" | "down" | "stable";
  status: "good" | "warning" | "critical";
}

interface SystemHealth {
  api: "healthy" | "degraded" | "down";
  database: "healthy" | "degraded" | "down";
  automation: "healthy" | "degraded" | "down";
  notifications: "healthy" | "degraded" | "down";
}

export const AdvancedMonitoringDashboard: React.FC = () => {
  const [realTimeMetrics, setRealTimeMetrics] = useState<MetricData[]>([
    { label: "Success Rate", value: "87%", trend: "up", status: "good" },
    {
      label: "Avg Response Time",
      value: "2.3s",
      trend: "down",
      status: "good",
    },
    { label: "Daily Applications", value: "24", trend: "up", status: "good" },
    { label: "Queue Size", value: "3", trend: "stable", status: "good" },
  ]);

  const [systemHealth, setSystemHealth] = useState<SystemHealth>({
    api: "healthy",
    database: "healthy",
    automation: "healthy",
    notifications: "degraded",
  });

  const [debugMode, setDebugMode] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        // Simulate real-time updates
        setRealTimeMetrics((prev) =>
          prev.map((metric) => ({
            ...metric,
            value:
              Math.random() > 0.5 ? metric.value : updateMetricValue(metric),
          }))
        );
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const updateMetricValue = (metric: MetricData): string => {
    switch (metric.label) {
      case "Success Rate":
        return `${Math.floor(Math.random() * 10 + 85)}%`;
      case "Avg Response Time":
        return `${(Math.random() * 2 + 1.5).toFixed(1)}s`;
      case "Daily Applications":
        return `${Math.floor(Math.random() * 10 + 20)}`;
      case "Queue Size":
        return `${Math.floor(Math.random() * 5)}`;
      default:
        return metric.value;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "healthy":
      case "good":
        return "#10B981";
      case "degraded":
      case "warning":
        return "#F59E0B";
      case "down":
      case "critical":
        return "#EF4444";
      default:
        return "#6B7280";
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return "trending-up";
      case "down":
        return "trending-down";
      default:
        return "remove";
    }
  };

  return (
    <ScrollView style={styles.container}>
      {/* Header Controls */}
      <View style={styles.header}>
        <Text style={styles.title}>Advanced Monitoring</Text>
        <View style={styles.controls}>
          <View style={styles.controlItem}>
            <Text style={styles.controlLabel}>Auto Refresh</Text>
            <Switch
              value={autoRefresh}
              onValueChange={setAutoRefresh}
              trackColor={{ false: "#767577", true: "#4F46E5" }}
            />
          </View>
          <View style={styles.controlItem}>
            <Text style={styles.controlLabel}>Debug Mode</Text>
            <Switch
              value={debugMode}
              onValueChange={setDebugMode}
              trackColor={{ false: "#767577", true: "#DC2626" }}
            />
          </View>
        </View>
      </View>

      {/* Real-time Metrics */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Real-time Metrics</Text>
        <View style={styles.metricsGrid}>
          {realTimeMetrics.map((metric, index) => (
            <LinearGradient
              key={index}
              colors={["#F8FAFC", "#F1F5F9"]}
              style={styles.metricCard}
            >
              <View style={styles.metricHeader}>
                <Text style={styles.metricLabel}>{metric.label}</Text>
                <Ionicons
                  name={getTrendIcon(metric.trend)}
                  size={16}
                  color={getStatusColor(metric.status)}
                />
              </View>
              <Text
                style={[
                  styles.metricValue,
                  { color: getStatusColor(metric.status) },
                ]}
              >
                {metric.value}
              </Text>
            </LinearGradient>
          ))}
        </View>
      </View>

      {/* System Health */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>System Health</Text>
        <View style={styles.healthGrid}>
          {Object.entries(systemHealth).map(([service, status]) => (
            <View key={service} style={styles.healthItem}>
              <View style={styles.healthIndicator}>
                <View
                  style={[
                    styles.statusDot,
                    { backgroundColor: getStatusColor(status) },
                  ]}
                />
                <Text style={styles.healthLabel}>
                  {service.charAt(0).toUpperCase() + service.slice(1)}
                </Text>
              </View>
              <Text
                style={[styles.healthStatus, { color: getStatusColor(status) }]}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </Text>
            </View>
          ))}
        </View>
      </View>

      {/* AI Insights */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>AI Insights</Text>
        <LinearGradient
          colors={["#EEF2FF", "#E0E7FF"]}
          style={styles.insightCard}
        >
          <View style={styles.insightHeader}>
            <Ionicons name="bulb" size={20} color="#4338CA" />
            <Text style={styles.insightTitle}>Performance Optimization</Text>
          </View>
          <Text style={styles.insightText}>
            Your automation is performing 23% better than last week. Consider
            increasing the daily application limit from 3 to 5 based on current
            success rates.
          </Text>
          <TouchableOpacity style={styles.insightAction}>
            <Text style={styles.insightActionText}>Apply Suggestion</Text>
          </TouchableOpacity>
        </LinearGradient>
      </View>

      {/* Debug Tools (only visible in debug mode) */}
      {debugMode && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Debug Tools</Text>
          <View style={styles.debugTools}>
            <TouchableOpacity style={styles.debugButton}>
              <Ionicons name="bug" size={16} color="#DC2626" />
              <Text style={styles.debugButtonText}>View Logs</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.debugButton}>
              <Ionicons name="refresh" size={16} color="#DC2626" />
              <Text style={styles.debugButtonText}>Force Refresh</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.debugButton}>
              <Ionicons name="analytics" size={16} color="#DC2626" />
              <Text style={styles.debugButtonText}>Raw Data</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#E5E7EB",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1F2937",
  },
  controls: {
    flexDirection: "row",
    gap: 15,
  },
  controlItem: {
    alignItems: "center",
    gap: 5,
  },
  controlLabel: {
    fontSize: 12,
    color: "#6B7280",
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1F2937",
    marginBottom: 15,
  },
  metricsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 10,
  },
  metricCard: {
    flex: 1,
    minWidth: "45%",
    padding: 15,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  metricHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  metricLabel: {
    fontSize: 14,
    color: "#6B7280",
  },
  metricValue: {
    fontSize: 20,
    fontWeight: "bold",
  },
  healthGrid: {
    gap: 12,
  },
  healthItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 12,
    backgroundColor: "#F9FAFB",
    borderRadius: 8,
  },
  healthIndicator: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  healthLabel: {
    fontSize: 16,
    color: "#1F2937",
  },
  healthStatus: {
    fontSize: 14,
    fontWeight: "600",
  },
  insightCard: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#C7D2FE",
  },
  insightHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    marginBottom: 8,
  },
  insightTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#4338CA",
  },
  insightText: {
    fontSize: 14,
    color: "#4338CA",
    lineHeight: 20,
    marginBottom: 12,
  },
  insightAction: {
    backgroundColor: "#4338CA",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignSelf: "flex-start",
  },
  insightActionText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "600",
  },
  debugTools: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 10,
  },
  debugButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    backgroundColor: "#FEE2E2",
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: "#FECACA",
  },
  debugButtonText: {
    fontSize: 14,
    color: "#DC2626",
    fontWeight: "500",
  },
});
