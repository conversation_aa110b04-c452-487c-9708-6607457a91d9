import { ApiResponse, apiService } from "./api";
import { debugLogger } from "./debugLogger";

// Auth types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  isPropertyOwner?: boolean;
}

export interface User {
  _id: string;
  id?: string; // Alias for _id to fix TypeScript errors in navigation service
  email: string;
  firstName?: string;
  lastName?: string;
  role: "user" | "admin" | "owner";
  preferences?: UserPreferences;
  propertyOwner?: {
    isPropertyOwner: boolean;
    properties: any[];
    verificationStatus: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface UserPreferences {
  maxPrice: number;
  minPrice: number;
  maxRent?: number;
  minRooms?: number;
  maxRooms?: number;
  preferredLocations?: string[];
  propertyTypes?: string[];
  amenities?: string[];
  notifications?: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken?: string;
}

export interface UpdatePreferencesData {
  minPrice?: number;
  maxPrice?: number;
  maxRent?: number;
  minRooms?: number;
  maxRooms?: number;
  preferredLocations?: string[];
  propertyTypes?: string[];
  amenities?: string[];
  notifications?: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
}

class AuthService {
  /**
   * Login user with email and password
   */
  async login(
    credentials: LoginCredentials
  ): Promise<ApiResponse<AuthResponse>> {
    try {
      const response = await apiService.post<AuthResponse>(
        "/auth/login",
        credentials
      );

      if (response.success && response.data) {
        // Save auth data to secure storage
        await apiService.saveAuthData(
          response.data.token,
          response.data.refreshToken,
          response.data.user
        );
      }

      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Register new user
   */
  async register(userData: RegisterData): Promise<ApiResponse<AuthResponse>> {
    try {
      console.log("AuthService - Register API call with:", {
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        isPropertyOwner: userData.isPropertyOwner,
      });

      const response = await apiService.post<AuthResponse>(
        "/auth/register",
        userData
      );

      console.log("AuthService - Register API response:", {
        success: response.success,
        message: response.message,
        status: response.status,
        error: response.error,
        data: response.data ? "Has data" : "No data",
      });

      if (response.success && response.data) {
        // Save auth data to secure storage
        await apiService.saveAuthData(
          response.data.token,
          response.data.refreshToken,
          response.data.user
        );
        console.log("AuthService - Auth data saved successfully");
      } else {
        console.log(
          "AuthService - Registration failed:",
          response.message || response.error || "Unknown error"
        );
      }

      return response;
    } catch (error: any) {
      console.error(
        "AuthService - Register API error:",
        error.message || error
      );
      if (error.response) {
        console.error("AuthService - API error details:", {
          status: error.response.status,
          data: error.response.data,
        });
      }
      throw error;
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      // Call logout endpoint to invalidate token on server
      await apiService.post("/auth/logout");
    } catch (error) {
      // Continue with logout even if server call fails
      console.warn("Logout API call failed:", error);
    } finally {
      // Always clear local auth data
      await apiService.clearAuthData();
    }
  }

  /**
   * Get current user profile
   */
  async getCurrentUser(): Promise<ApiResponse<User>> {
    try {
      return await apiService.get<User>("/auth/me");
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update user preferences
   */
  async updatePreferences(
    preferences: UpdatePreferencesData
  ): Promise<ApiResponse<User>> {
    try {
      // Get current user data to extract userId
      const userData = await apiService.getUserData();

      // Extract user ID from userData structure (may be wrapped in user property)
      const userId = userData?._id || userData?.user?._id;
      if (!userData || !userId) {
        throw new Error("User not authenticated or user ID not available");
      }

      const endpoint = `/auth/users/${userId}/preferences`;

      // Minimal logging: userId only at debug level
      debugLogger.log("AUTH", "Updating preferences", { userId });

      // Ensure we're sending a valid preferences object
      // Make sure all required fields are present
      const completePreferences: UserPreferences = {
        minPrice: preferences.minPrice || 0,
        maxPrice: preferences.maxPrice || 2000,
        preferredLocations: preferences.preferredLocations || [],
        propertyTypes: preferences.propertyTypes || ["apartment"],
        minRooms: preferences.minRooms || 1,
        maxRooms: preferences.maxRooms || 3,
        amenities: preferences.amenities || [],
        notifications: {
          email: preferences.notifications?.email ?? true,
          push: preferences.notifications?.push ?? true,
          sms: preferences.notifications?.sms ?? false,
        },
      };

      // Send the preferences with proper wrapping
      // The backend expects { preferences: { ... } }
      const payload = {
        preferences: completePreferences,
      };

      console.log(
        "AuthService - Full preferences payload:",
        JSON.stringify(payload, null, 2)
      );

      const response = await apiService.put<User>(endpoint, payload);

      console.log("AuthService - Preferences update response:", response);

      if (response.success && response.data) {
        // Make sure the user data has the preferences properly set
        const updatedUserData = response.data;
        if (!updatedUserData.preferences) {
          updatedUserData.preferences = completePreferences;
        } else {
          // Replace the preferences completely
          updatedUserData.preferences = completePreferences;
        }

        // Update cached user data
        await apiService.saveAuthData(
          (await apiService.getAuthToken()) || "",
          undefined,
          updatedUserData
        );

        // Return the updated user data
        return {
          ...response,
          data: updatedUserData,
        };
      }

      return response;
    } catch (error) {
      console.error("AuthService - Error updating preferences:", error);
      throw error;
    }
  }

  /**
   * Get user preferences
   */
  async getPreferences(): Promise<ApiResponse<UserPreferences>> {
    try {
      return await apiService.get<UserPreferences>("/auth/preferences");
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(profileData: Partial<User>): Promise<ApiResponse<User>> {
    try {
      const response = await apiService.put<User>("/auth/profile", profileData);

      if (response.success && response.data) {
        // Update cached user data
        await apiService.saveAuthData(
          (await apiService.getAuthToken()) || "",
          undefined,
          response.data
        );
      }

      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Change password
   */
  async changePassword(
    currentPassword: string,
    newPassword: string
  ): Promise<ApiResponse> {
    try {
      return await apiService.put("/auth/change-password", {
        currentPassword,
        newPassword,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email: string): Promise<ApiResponse> {
    try {
      return await apiService.post("/auth/forgot-password", { email });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(
    token: string,
    newPassword: string
  ): Promise<ApiResponse> {
    try {
      return await apiService.post("/auth/reset-password", {
        token,
        newPassword,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check if user is authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    return await apiService.isAuthenticated();
  }

  /**
   * Get cached user data
   */
  async getCachedUser(): Promise<User | null> {
    return await apiService.getUserData();
  }

  /**
   * Get auth token
   */
  async getAuthToken(): Promise<string | null> {
    return await apiService.getAuthToken();
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<boolean> {
    try {
      // This is handled automatically by the API service interceptors
      // Just check if we're still authenticated after any refresh attempts
      return await this.isAuthenticated();
    } catch (error) {
      console.error("Token refresh failed:", error);
      return false;
    }
  }

  /**
   * Validate email format
   */
  validateEmail(email: string): boolean {
    // More permissive email validation - just check for @ and .
    return email.includes("@") && email.includes(".");
  }

  /**
   * Validate password strength
   */
  validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (password.length < 6) {
      errors.push("Password must be at least 6 characters long");
    }

    if (!/[A-Z]/.test(password)) {
      errors.push("Password must contain at least one uppercase letter");
    }

    if (!/[a-z]/.test(password)) {
      errors.push("Password must contain at least one lowercase letter");
    }

    if (!/\d/.test(password)) {
      errors.push("Password must contain at least one number");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;
