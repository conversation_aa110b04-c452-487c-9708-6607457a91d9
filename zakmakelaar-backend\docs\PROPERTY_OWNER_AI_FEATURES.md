# Property Owner AI Features

This document describes the comprehensive AI-powered features for property owners in the ZakMakelaar platform.

## Overview

The Property Owner AI Features provide three major enhancements to help property owners efficiently manage applications, schedule viewings, and track applicant progress:

1. **AI-Based Applicant Filtering and Ranking**
2. **Smart Viewing Scheduler**
3. **Applicant Presentation Checklist**

## 1. AI-Based Applicant Filtering and Ranking

### Purpose
Automatically filters and ranks applicants using AI analysis to help property owners make better decisions faster.

### Features
- **Automated Filtering**: Filters applications based on income requirements, tenant scores, and other criteria
- **AI Analysis**: Uses Google Generative AI to analyze applications and provide recommendations
- **Smart Ranking**: Combines heuristic and AI scores with configurable weights
- **Detailed Insights**: Provides reasoning, strengths, concerns, and specific advice
- **Quick Insights**: Get instant AI analysis for individual applications

### API Endpoints

#### Filter and Rank Applicants
```
POST /api/property-owner/properties/{propertyId}/filter-applicants
```

**Request Body:**
```json
{
  "minimumIncomeRatio": 3.0,
  "minimumTenantScore": 60,
  "weights": {
    "income": 30,
    "employment": 20,
    "tenantScore": 25,
    "completeness": 15,
    "personalFit": 10
  },
  "preferences": {
    "preferStudents": false,
    "preferExpats": true,
    "maximumOccupants": 2
  }
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "filteredApplications": [
      {
        "_id": "app_id",
        "finalRanking": {
          "rank": 1,
          "score": 87,
          "factors": {
            "income": 90,
            "employment": 85,
            "tenantScore": 82,
            "completeness": 95,
            "personalFit": 80
          },
          "recommendation": "approve",
          "passesMinimum": true
        },
        "aiAnalysis": {
          "recommendation": "approve",
          "confidence": 85,
          "reasoning": "Strong candidate with excellent financial profile",
          "strengths": ["High income ratio", "Stable employment"],
          "concerns": [],
          "score": 87,
          "specificAdvice": "Schedule interview soon"
        }
      }
    ],
    "totalApplications": 15,
    "filteredCount": 8,
    "summary": "8 qualified applications found. Top candidate scored 87/100.",
    "filteringCriteria": { /* applied criteria */ }
  }
}
```

#### Get Application Insights
```
GET /api/property-owner/applications/{applicationId}/insights
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "applicationId": "app_id",
    "quickInsights": {
      "recommendation": "approve",
      "confidence": 85,
      "reasoning": "Strong financial profile with stable employment",
      "strengths": ["High income", "Complete documentation"],
      "concerns": []
    },
    "scores": {
      "income": 90,
      "employment": 85,
      "completeness": 95,
      "tenantScore": 82
    },
    "recommendation": "approve"
  }
}
```

## 2. Smart Viewing Scheduler

### Purpose
Manages viewing appointments with intelligent scheduling, conflict resolution, and optimization.

### Features
- **Flexible Scheduling**: Configure availability by day of the week
- **Automated Slot Generation**: Creates available time slots based on preferences
- **Request Management**: Handle viewing requests with approval workflows
- **Optimization**: AI-powered suggestions for optimal scheduling
- **Statistics**: Track viewing performance and analytics
- **Buffer Management**: Automatic time buffers between appointments

### API Endpoints

#### Initialize Viewing Schedule
```
POST /api/property-owner/properties/{propertyId}/viewing-schedule
```

**Request Body:**
```json
{
  "weekdays": {
    "monday": { "available": true, "startTime": "09:00", "endTime": "18:00" },
    "tuesday": { "available": true, "startTime": "09:00", "endTime": "18:00" },
    "saturday": { "available": true, "startTime": "10:00", "endTime": "16:00" },
    "sunday": { "available": false, "startTime": "", "endTime": "" }
  },
  "preferredDuration": 30,
  "bufferTime": 15,
  "maxViewingsPerDay": 8,
  "autoApproval": false
}
```

#### Get Viewing Schedule
```
GET /api/property-owner/properties/{propertyId}/viewing-schedule
```

#### Update Owner Availability
```
PUT /api/property-owner/properties/{propertyId}/availability
```

#### Approve Viewing Request
```
POST /api/property-owner/viewing-schedule/{scheduleId}/approve/{slotId}
```

**Request Body:**
```json
{
  "applicationId": "app_id"
}
```

#### Get Viewing Suggestions
```
GET /api/property-owner/properties/{propertyId}/viewing-suggestions?daysAhead=7
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "propertyId": "prop_id",
    "totalRequests": 5,
    "suggestions": [
      {
        "slotId": "slot_id",
        "startTime": "2024-01-25T10:00:00Z",
        "endTime": "2024-01-25T10:30:00Z",
        "requestCount": 3,
        "recommendations": [
          {
            "action": "approve",
            "applicationId": "app_id",
            "reason": "Highest priority applicant",
            "confidence": 85
          }
        ]
      }
    ]
  }
}
```

#### Get Viewing Statistics
```
GET /api/property-owner/properties/{propertyId}/viewing-stats
```

## 3. Applicant Presentation Checklist

### Purpose
Comprehensive tracking system for managing the applicant evaluation process from initial review to final decision.

### Features
- **Multi-Category Tracking**: Documents, background checks, interviews, financial assessment, references
- **Progress Monitoring**: Real-time completion percentages and stage tracking
- **Interview Management**: Schedule, conduct, and assess interviews
- **Reference Checking**: Track and manage reference verifications
- **Automated Insights**: Generate recommendations and risk assessments
- **Activity Logging**: Complete audit trail of all activities

### API Endpoints

#### Create Applicant Checklist
```
POST /api/property-owner/applications/{applicationId}/checklist
```

**Request Body (optional custom template):**
```json
{
  "documents": [
    { "name": "ID Document", "type": "id_document", "required": true },
    { "name": "Income Proof", "type": "income_proof", "required": true }
  ]
}
```

#### Get Applicant Checklist
```
GET /api/property-owner/checklists/{checklistId}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "checklist": {
      "application": {
        "applicationId": "app_id",
        "applicantName": "John Doe",
        "propertyTitle": "Modern Apartment"
      },
      "checklist": {
        "documents": {
          "status": "in_progress",
          "completionPercentage": 75,
          "items": [
            {
              "name": "ID Document",
              "type": "id_document",
              "status": "verified",
              "verifiedAt": "2024-01-20T10:00:00Z"
            }
          ]
        },
        "interview": {
          "status": "completed",
          "scheduledDate": "2024-01-22T14:00:00Z",
          "assessment": {
            "overall": 4,
            "communication": 5,
            "professionalism": 4,
            "recommendation": "recommend"
          }
        }
      },
      "summary": {
        "overallProgress": 80,
        "currentStage": "final_review",
        "recommendedAction": "approve",
        "confidence": 85
      }
    },
    "insights": {
      "strengths": ["Complete documentation", "Excellent interview"],
      "concerns": [],
      "progress": {
        "overall": 80,
        "stage": "final_review"
      }
    },
    "riskAssessment": {
      "level": "low",
      "score": 15,
      "factors": []
    },
    "recommendation": {
      "action": "approve",
      "confidence": 85,
      "reasoning": ["Low risk profile", "Complete evaluation"]
    }
  }
}
```

#### Update Checklist Item
```
PUT /api/property-owner/checklists/{checklistId}/{category}/{itemId}
```

**Request Body:**
```json
{
  "status": "verified",
  "notes": "Document verified successfully",
  "verifiedAt": "2024-01-20T10:00:00Z"
}
```

#### Schedule Interview
```
POST /api/property-owner/checklists/{checklistId}/interview
```

**Request Body:**
```json
{
  "scheduledDate": "2024-01-25T14:00:00Z",
  "type": "in_person",
  "duration": 45
}
```

#### Complete Interview
```
POST /api/property-owner/checklists/{checklistId}/interview/complete
```

**Request Body:**
```json
{
  "assessment": {
    "overall": 4,
    "communication": 5,
    "professionalism": 4,
    "reliability": 4,
    "compatibility": 3,
    "recommendation": "recommend",
    "notes": "Professional and well-prepared candidate",
    "positives": ["Clear communication", "Punctual"],
    "concerns": ["Slightly concerned about pets"]
  }
}
```

#### Add Reference
```
POST /api/property-owner/checklists/{checklistId}/references
```

**Request Body:**
```json
{
  "type": "landlord",
  "name": "Previous Landlord",
  "relationship": "Former landlord",
  "contactInfo": {
    "email": "<EMAIL>",
    "phone": "+31612345678"
  }
}
```

#### Get All Checklists
```
GET /api/property-owner/checklists?stage=interview_scheduled&priority=high&limit=20
```

## Enhanced Dashboard

### Get Enhanced Dashboard Data
```
GET /api/property-owner/dashboard-data
```

**Response includes:**
- Standard dashboard metrics
- AI insights and quick stats
- Recent checklists summary
- Pending interviews and decisions
- Next recommended actions

## Service Architecture

### 1. ApplicantFilteringService
- **File**: `src/services/applicantFilteringService.js`
- **Dependencies**: Google Generative AI, Application/Property models
- **Key Methods**:
  - `filterAndRankApplicants(propertyId, preferences)`
  - `getQuickInsights(applicationId)`
  - `generateFilteringSummary(applications, criteria)`

### 2. ViewingSchedulerService
- **File**: `src/services/viewingSchedulerService.js`
- **Dependencies**: ViewingSchedule model (defined in service)
- **Key Methods**:
  - `initializeSchedule(propertyId, availability)`
  - `generateTimeSlots(scheduleId, daysAhead)`
  - `approveViewingRequest(scheduleId, slotId, applicationId)`
  - `getScheduleSuggestions(propertyId)`

### 3. ApplicantChecklistService
- **File**: `src/services/applicantChecklistService.js`
- **Dependencies**: ApplicantChecklist model (defined in service)
- **Key Methods**:
  - `createChecklist(applicationId, ownerUserId)`
  - `updateChecklistItem(checklistId, category, itemId, updates)`
  - `scheduleInterview(checklistId, details)`
  - `getChecklistSummary(checklistId)`

## Configuration

### Environment Variables
```env
# Google AI Configuration
GOOGLE_AI_API_KEY=your_google_ai_api_key
GOOGLE_AI_MODEL_ANALYSIS=gemini-1.5-flash
```

### AI Model Configuration
```javascript
// config/config.js
googleAI: {
  apiKey: process.env.GOOGLE_AI_API_KEY,
  models: {
    analysis: process.env.GOOGLE_AI_MODEL_ANALYSIS || 'gemini-1.5-flash'
  }
}
```

## Error Handling

All endpoints include comprehensive error handling:
- **400**: Bad Request - Invalid input data
- **401**: Unauthorized - Authentication required
- **403**: Forbidden - Access denied (not property owner)
- **404**: Not Found - Resource doesn't exist
- **500**: Internal Server Error - System error

## Testing

### Integration Tests
- **File**: `src/tests/propertyOwnerAI.integration.test.js`
- **Coverage**: All major endpoints and error scenarios
- **Mocking**: Google AI API calls are mocked for testing

### Run Tests
```bash
npm test -- --testPathPattern=propertyOwnerAI
```

## Usage Examples

### Complete Workflow Example

1. **Filter Applicants**:
   ```bash
   curl -X POST "/api/property-owner/properties/123/filter-applicants" \
   -H "Authorization: Bearer token" \
   -d '{"minimumIncomeRatio": 3.0}'
   ```

2. **Create Checklist for Top Candidate**:
   ```bash
   curl -X POST "/api/property-owner/applications/456/checklist" \
   -H "Authorization: Bearer token"
   ```

3. **Schedule Interview**:
   ```bash
   curl -X POST "/api/property-owner/checklists/789/interview" \
   -H "Authorization: Bearer token" \
   -d '{"scheduledDate": "2024-01-25T14:00:00Z"}'
   ```

4. **Initialize Viewing Schedule**:
   ```bash
   curl -X POST "/api/property-owner/properties/123/viewing-schedule" \
   -H "Authorization: Bearer token" \
   -d '{"weekdays": {...}}'
   ```

## Performance Considerations

- **AI API Calls**: Batched processing with rate limiting
- **Database Queries**: Optimized with proper indexing
- **Caching**: Results cached where appropriate
- **Pagination**: Large result sets are paginated
- **Error Recovery**: Graceful fallbacks when AI services are unavailable

## Future Enhancements

1. **Machine Learning**: Train custom models on historical data
2. **Advanced Analytics**: Predictive analytics for tenant success
3. **Integration**: Connect with external verification services
4. **Mobile App**: Dedicated mobile interface for property owners
5. **Automation**: Fully automated decision-making workflows

## Support

For technical support or questions about the AI features, please refer to:
- API Documentation: `/api/docs`
- Integration Tests: `src/tests/propertyOwnerAI.integration.test.js`
- Service Code: `src/services/applicant*Service.js`