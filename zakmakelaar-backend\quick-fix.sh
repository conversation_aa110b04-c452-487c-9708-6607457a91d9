#!/bin/bash
# Simple permission fix command - no special characters

echo "Waiting for container to be ready..."
sleep 10

echo "Fixing permissions..."
docker exec zakmakelaar-backend bash -c 'mkdir -p /app/logs /app/uploads /app/screenshots'
docker exec zakmakelaar-backend bash -c 'chmod 755 /app/logs /app/uploads /app/screenshots'
docker exec zakmakelaar-backend bash -c 'touch /app/logs/error-$(date +%Y-%m-%d).log'
docker exec zakmakelaar-backend bash -c 'touch /app/logs/combined-$(date +%Y-%m-%d).log'
docker exec zakmakelaar-backend bash -c 'chmod 644 /app/logs/*.log'

echo "Permissions fixed. Checking container status..."
docker ps --filter "name=zakmakelaar-backend"