/**
 * Core Auto Application System Tests
 *
 * Tests the core functionality without browser automation
 * to ensure basic components work correctly.
 */

const AdaptiveRateLimiter = require("../services/adaptiveRateLimiter");
const EnhancedFormDetector = require("../services/enhancedFormDetector");

describe("Auto Application Core Tests", () => {
  describe("AdaptiveRateLimiter", () => {
    let rateLimiter;
    const testUserId = "test-user-123";
    const testPlatform = "funda.nl";
    const testUrl = "https://www.funda.nl/test-property";

    beforeEach(() => {
      rateLimiter = new AdaptiveRateLimiter();
    });

    test("should allow applications within limits", async () => {
      const canSubmit = await rateLimiter.canSubmit(
        testUserId,
        testPlatform,
        testUrl
      );
      expect(canSubmit.allowed).toBe(true);
      expect(typeof canSubmit.recommendedDelay).toBe("number");
    });

    test("should record successful applications", () => {
      rateLimiter.recordSuccess(testUserId, testPlatform, testUrl, 2500);
      const limits = rateLimiter.getCurrentLimits(testPlatform, testUrl);
      expect(limits.platform).toBe(testPlatform);
      expect(limits.successRate).toBeGreaterThan(0);
    });

    test("should record failed applications", () => {
      rateLimiter.recordFailure(
        testUserId,
        testPlatform,
        testUrl,
        new Error("Test failure"),
        3000
      );
      const limits = rateLimiter.getCurrentLimits(testPlatform, testUrl);
      expect(limits.platform).toBe(testPlatform);
    });

    test("should categorize errors correctly", () => {
      expect(rateLimiter._categorizeError(new Error("blocked by server"))).toBe(
        "blocked"
      );
      expect(rateLimiter._categorizeError(new Error("captcha required"))).toBe(
        "detection"
      );
      expect(rateLimiter._categorizeError(new Error("network timeout"))).toBe(
        "network"
      );
      expect(rateLimiter._categorizeError(new Error("form not found"))).toBe(
        "form"
      );
      expect(rateLimiter._categorizeError(new Error("unknown error"))).toBe(
        "unknown"
      );
    });

    test("should get platform key correctly", () => {
      expect(
        rateLimiter._getPlatformKey("funda.nl", "https://www.funda.nl/test")
      ).toBe("funda.nl");
      expect(
        rateLimiter._getPlatformKey(null, "https://www.pararius.nl/test")
      ).toBe("pararius.nl");
      expect(
        rateLimiter._getPlatformKey(null, "https://unknown-site.com/test")
      ).toBe("generic");
    });

    test("should enforce platform limits after multiple applications", async () => {
      // Record many applications quickly
      for (let i = 0; i < 15; i++) {
        rateLimiter.recordSuccess(testUserId, testPlatform, testUrl);
      }

      const canSubmit = await rateLimiter.canSubmit(
        testUserId,
        testPlatform,
        testUrl
      );
      expect(canSubmit.allowed).toBe(false);
      expect(canSubmit.reason).toBe("platform_limit_exceeded");
    });

    test("should adapt to poor success rates", () => {
      // Record several failures
      for (let i = 0; i < 8; i++) {
        rateLimiter.recordFailure(
          testUserId,
          testPlatform,
          testUrl,
          new Error("Test failure")
        );
      }
      // Record few successes
      for (let i = 0; i < 2; i++) {
        rateLimiter.recordSuccess(testUserId, testPlatform, testUrl);
      }

      const limits = rateLimiter.getCurrentLimits(testPlatform, testUrl);
      expect(["poor", "critical", "failing"]).toContain(limits.adaptationLevel);
    });
  });

  describe("EnhancedFormDetector", () => {
    let formDetector;

    beforeEach(() => {
      formDetector = new EnhancedFormDetector();
    });

    test("should extract platform from URL correctly", () => {
      expect(formDetector._extractPlatform("https://www.funda.nl/test")).toBe(
        "funda.nl"
      );
      expect(formDetector._extractPlatform("https://pararius.nl/test")).toBe(
        "pararius.nl"
      );
      expect(
        formDetector._extractPlatform("https://www.kamernet.nl/test")
      ).toBe("kamernet.nl");
      expect(
        formDetector._extractPlatform("https://unknown-site.com/test")
      ).toBe("generic");
    });

    test("should have platform patterns configured", () => {
      expect(formDetector.platformPatterns).toBeDefined();
      expect(typeof formDetector.platformPatterns).toBe("object");
      expect(Object.keys(formDetector.platformPatterns)).toContain("funda.nl");
      expect(Object.keys(formDetector.platformPatterns)).toContain(
        "pararius.nl"
      );
      expect(Object.keys(formDetector.platformPatterns)).toContain(
        "kamernet.nl"
      );

      const fundaPatterns = formDetector.platformPatterns["funda.nl"];
      expect(fundaPatterns).toHaveProperty("contactButton");
      expect(fundaPatterns).toHaveProperty("formSelectors");
      expect(fundaPatterns).toHaveProperty("fieldPatterns");
      expect(Array.isArray(fundaPatterns.contactButton)).toBe(true);
    });

    test("should have generic patterns as fallback", () => {
      expect(formDetector.genericPatterns).toHaveProperty("contactButtons");
      expect(formDetector.genericPatterns).toHaveProperty("formSelectors");
      expect(formDetector.genericPatterns).toHaveProperty("fieldPatterns");

      const fieldPatterns = formDetector.genericPatterns.fieldPatterns;
      expect(fieldPatterns).toHaveProperty("name");
      expect(fieldPatterns).toHaveProperty("email");
      expect(fieldPatterns).toHaveProperty("phone");
      expect(fieldPatterns).toHaveProperty("message");
    });

    test("should calculate form complexity correctly", () => {
      const simpleForm = {
        fields: [
          {
            type: "name",
            required: false,
            tagName: "input",
            inputType: "text",
          },
          {
            type: "email",
            required: false,
            tagName: "input",
            inputType: "email",
          },
          {
            type: "message",
            required: false,
            tagName: "textarea",
            inputType: "",
          },
        ],
        requiresLogin: false,
        platform: "funda.nl",
      };

      const complexForm = {
        fields: [
          { type: "name", required: true, tagName: "input", inputType: "text" },
          {
            type: "email",
            required: true,
            tagName: "input",
            inputType: "email",
          },
          { type: "phone", required: true, tagName: "input", inputType: "tel" },
          {
            type: "message",
            required: true,
            tagName: "textarea",
            inputType: "",
          },
          {
            type: "document",
            required: false,
            tagName: "input",
            inputType: "file",
          },
          {
            type: "country",
            required: false,
            tagName: "select",
            inputType: "",
          },
        ],
        requiresLogin: true,
        platform: "generic",
      };

      const simpleComplexity = formDetector.calculateComplexity(simpleForm);
      const complexComplexity = formDetector.calculateComplexity(complexForm);

      expect(simpleComplexity).toBeLessThan(complexComplexity);
      expect(simpleComplexity).toBeGreaterThanOrEqual(1);
      expect(complexComplexity).toBeGreaterThan(3);
      expect(complexComplexity).toBeLessThanOrEqual(5);
    });
  });

  describe("System Integration", () => {
    test("should have all required components available", () => {
      expect(AdaptiveRateLimiter).toBeDefined();
      expect(EnhancedFormDetector).toBeDefined();

      const rateLimiter = new AdaptiveRateLimiter();
      const formDetector = new EnhancedFormDetector();

      expect(rateLimiter).toBeInstanceOf(AdaptiveRateLimiter);
      expect(formDetector).toBeInstanceOf(EnhancedFormDetector);
    });

    test("should handle platform-specific configurations", () => {
      const rateLimiter = new AdaptiveRateLimiter();
      const formDetector = new EnhancedFormDetector();

      const platforms = ["funda.nl", "pararius.nl", "kamernet.nl"];

      platforms.forEach((platform) => {
        const limits = rateLimiter.getCurrentLimits(
          platform,
          `https://${platform}`
        );
        expect(limits.platform).toBe(platform);
        expect(limits.limits).toBeDefined();
        expect(limits.limits).toHaveProperty("maxPerHour");
        expect(limits.limits).toHaveProperty("minDelay");
        expect(limits.limits.maxPerHour).toBeGreaterThan(0);
        expect(limits.limits.minDelay).toBeGreaterThan(0);

        const platformKey = formDetector._extractPlatform(
          `https://${platform}`
        );
        expect(platformKey).toBe(platform);
      });
    });

    test("should provide consistent error handling", () => {
      const rateLimiter = new AdaptiveRateLimiter();

      const errorTypes = [
        { message: "Access blocked", expected: "blocked" },
        { message: "CAPTCHA detected", expected: "detection" },
        { message: "Connection timeout", expected: "network" },
        { message: "Form element not found", expected: "form" },
        { message: "Unexpected error", expected: "unknown" },
      ];

      errorTypes.forEach(({ message, expected }) => {
        const category = rateLimiter._categorizeError(new Error(message));
        expect(category).toBe(expected);
      });
    });
  });

  describe("Configuration Validation", () => {
    test("should have valid rate limiting configurations", () => {
      const rateLimiter = new AdaptiveRateLimiter();

      Object.entries(rateLimiter.defaultLimits).forEach(
        ([platform, limits]) => {
          expect(limits).toHaveProperty("maxPerHour");
          expect(limits).toHaveProperty("minDelay");
          expect(limits).toHaveProperty("maxDelay");
          expect(limits).toHaveProperty("burstLimit");
          expect(limits).toHaveProperty("cooldownPeriod");

          expect(limits.maxPerHour).toBeGreaterThan(0);
          expect(limits.minDelay).toBeGreaterThan(0);
          expect(limits.maxDelay).toBeGreaterThan(limits.minDelay);
          expect(limits.burstLimit).toBeGreaterThan(0);
          expect(limits.cooldownPeriod).toBeGreaterThan(0);
        }
      );
    });

    test("should have valid adaptation thresholds", () => {
      const rateLimiter = new AdaptiveRateLimiter();
      const thresholds = rateLimiter.adaptationThresholds;

      expect(thresholds.excellent).toBeGreaterThan(thresholds.good);
      expect(thresholds.good).toBeGreaterThan(thresholds.poor);
      expect(thresholds.poor).toBeGreaterThan(thresholds.critical);
      expect(thresholds.critical).toBeGreaterThan(0);
      expect(thresholds.excellent).toBeLessThanOrEqual(1);
    });
  });
});

module.exports = {
  AdaptiveRateLimiter,
  EnhancedFormDetector,
};
