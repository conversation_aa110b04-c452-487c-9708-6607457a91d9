#!/bin/bash
# Quick fix for Docker container permission issues

echo "🔧 Fixing Docker container permissions..."

# Check if container exists and is running
if ! docker ps --filter "name=zakmakelaar-backend" --format "table {{.Names}}\t{{.Status}}" | grep -q "zakmakelaar-backend"; then
    echo "❌ Container 'zakmakelaar-backend' is not running"
    echo "💡 Run './restart-docker-containers.sh' to start the containers"
    exit 1
fi

echo "📦 Container status:"
docker ps --filter "name=zakmakelaar-backend" --format "table {{.Names}}\t{{.Status}}"

echo ""
echo "🛠️ Fixing permissions inside container..."

# Create directories and fix permissions inside the running container
if docker exec zakmakelaar-backend bash -c "
    mkdir -p /app/logs /app/uploads /app/screenshots && 
    chmod 755 /app/logs /app/uploads /app/screenshots &&
    touch /app/logs/error-\$(date +%Y-%m-%d).log /app/logs/combined-\$(date +%Y-%m-%d).log &&
    chmod 644 /app/logs/*.log &&
    echo 'Permissions fixed successfully'
"; then
    echo "✅ Permissions fixed successfully!"
    
    # Restart the Node.js process inside the container
    echo "🔄 Restarting Node.js process..."
    docker exec zakmakelaar-backend pkill -f "node" || true
    
    sleep 3
    
    # Check if the process restarted (Docker should auto-restart it)
    echo "📊 Checking container status..."
    docker ps --filter "name=zakmakelaar-backend" --format "table {{.Names}}\t{{.Status}}"
    
    echo ""
    echo "📝 Recent logs after fix:"
    docker-compose logs --tail=20 zakmakelaar-backend
    
else
    echo "❌ Failed to fix permissions"
    exit 1
fi

echo ""
echo "✅ Permission fix complete!"
echo "🌐 Backend should be available at: http://localhost:3000"
echo "📊 Monitor logs with: docker-compose logs -f zakmakelaar-backend"