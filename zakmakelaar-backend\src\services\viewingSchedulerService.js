const mongoose = require('mongoose');
const Application = require('../models/Application');
const Property = require('../models/Property');
const User = require('../models/User');
const { loggers } = require('./logger');

// Create a new model for viewing schedules
const viewingScheduleSchema = new mongoose.Schema({
  property: {
    propertyId: { type: mongoose.Schema.Types.ObjectId, ref: 'Property', required: true },
    title: { type: String, required: true },
    address: { type: String, required: true }
  },
  
  owner: {
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    name: { type: String, required: true }
  },
  
  // Time slots for viewings
  timeSlots: [{
    startTime: { type: Date, required: true },
    endTime: { type: Date, required: true },
    duration: { type: Number, default: 30 }, // in minutes
    type: { 
      type: String, 
      enum: ['individual', 'group', 'virtual', 'open_house'], 
      default: 'individual' 
    },
    maxAttendees: { type: Number, default: 1 },
    status: { 
      type: String, 
      enum: ['available', 'booked', 'cancelled', 'completed'], 
      default: 'available' 
    },
    
    // Booking details
    bookedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'Application' },
    bookedAt: { type: Date },
    attendees: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
    
    // Queue for multiple requests
    requestQueue: [{
      applicationId: { type: mongoose.Schema.Types.ObjectId, ref: 'Application' },
      requestedAt: { type: Date, default: Date.now },
      priority: { type: Number, default: 0 },
      notes: { type: String }
    }],
    
    notes: { type: String },
    specialRequirements: { type: String }
  }],
  
  // Owner availability preferences
  availability: {
    weekdays: {
      monday: { available: Boolean, startTime: String, endTime: String },
      tuesday: { available: Boolean, startTime: String, endTime: String },
      wednesday: { available: Boolean, startTime: String, endTime: String },
      thursday: { available: Boolean, startTime: String, endTime: String },
      friday: { available: Boolean, startTime: String, endTime: String },
      saturday: { available: Boolean, startTime: String, endTime: String },
      sunday: { available: Boolean, startTime: String, endTime: String }
    },
    
    // Special dates (holidays, vacations, etc.)
    unavailableDates: [{
      startDate: { type: Date },
      endDate: { type: Date },
      reason: { type: String }
    }],
    
    // Preferred viewing preferences
    preferredDuration: { type: Number, default: 30 },
    bufferTime: { type: Number, default: 15 }, // Minutes between viewings
    maxViewingsPerDay: { type: Number, default: 8 },
    advanceBookingDays: { type: Number, default: 14 }, // How far in advance to allow bookings
    
    // Automatic scheduling preferences
    autoApproval: { type: Boolean, default: false },
    requireApproval: { type: Boolean, default: true }
  },
  
  // Notifications settings
  notifications: {
    newRequest: { type: Boolean, default: true },
    bookingConfirmed: { type: Boolean, default: true },
    reminderHours: { type: Number, default: 24 },
    cancellationNotice: { type: Boolean, default: true }
  },
  
  // Statistics
  stats: {
    totalViewings: { type: Number, default: 0 },
    completedViewings: { type: Number, default: 0 },
    noShows: { type: Number, default: 0 },
    averageRating: { type: Number, default: 0 },
    totalRequests: { type: Number, default: 0 }
  },
  
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Indexes
viewingScheduleSchema.index({ 'property.propertyId': 1 });
viewingScheduleSchema.index({ 'owner.userId': 1 });
viewingScheduleSchema.index({ 'timeSlots.startTime': 1 });
viewingScheduleSchema.index({ 'timeSlots.status': 1 });

const ViewingSchedule = mongoose.model('ViewingSchedule', viewingScheduleSchema);

class ViewingSchedulerService {
  constructor() {
    this.logger = loggers.schedulerLogger;
  }

  /**
   * Initialize viewing schedule for a property
   */
  async initializeSchedule(propertyId, ownerAvailability = {}) {
    try {
      const property = await Property.findById(propertyId).populate('owner.userId');
      if (!property) {
        throw new Error('Property not found');
      }

      // Check if schedule already exists
      const existingSchedule = await ViewingSchedule.findOne({ 'property.propertyId': propertyId });
      if (existingSchedule) {
        return existingSchedule;
      }

      // Create default availability
      const defaultAvailability = {
        weekdays: {
          monday: { available: true, startTime: '09:00', endTime: '18:00' },
          tuesday: { available: true, startTime: '09:00', endTime: '18:00' },
          wednesday: { available: true, startTime: '09:00', endTime: '18:00' },
          thursday: { available: true, startTime: '09:00', endTime: '18:00' },
          friday: { available: true, startTime: '09:00', endTime: '18:00' },
          saturday: { available: true, startTime: '10:00', endTime: '16:00' },
          sunday: { available: false, startTime: '', endTime: '' }
        },
        unavailableDates: [],
        preferredDuration: 30,
        bufferTime: 15,
        maxViewingsPerDay: 8,
        advanceBookingDays: 14,
        autoApproval: false,
        requireApproval: true,
        ...ownerAvailability
      };

      const schedule = new ViewingSchedule({
        property: {
          propertyId: property._id,
          title: property.title,
          address: property.fullAddress
        },
        owner: {
          userId: property.owner.userId._id,
          name: `${property.owner.userId.profile?.firstName || ''} ${property.owner.userId.profile?.lastName || ''}`.trim()
        },
        availability: defaultAvailability,
        timeSlots: []
      });

      await schedule.save();
      
      // Generate initial time slots for the next 2 weeks
      await this.generateTimeSlots(schedule._id, 14);

      this.logger.info('Viewing schedule initialized', { propertyId, scheduleId: schedule._id });
      return schedule;
    } catch (error) {
      this.logger.error('Error initializing schedule', { error: error.message, propertyId });
      throw error;
    }
  }

  /**
   * Generate time slots based on availability
   */
  async generateTimeSlots(scheduleId, daysAhead = 14) {
    try {
      const schedule = await ViewingSchedule.findById(scheduleId);
      if (!schedule) {
        throw new Error('Schedule not found');
      }

      const startDate = new Date();
      const endDate = new Date(startDate.getTime() + (daysAhead * 24 * 60 * 60 * 1000));
      
      const newSlots = [];
      const currentDate = new Date(startDate);

      while (currentDate <= endDate) {
        const dayOfWeek = this.getDayName(currentDate.getDay());
        const dayAvailability = schedule.availability.weekdays[dayOfWeek];

        if (dayAvailability?.available && !this.isUnavailableDate(currentDate, schedule.availability.unavailableDates)) {
          const daySlots = this.generateDaySlots(currentDate, dayAvailability, schedule.availability);
          newSlots.push(...daySlots);
        }

        currentDate.setDate(currentDate.getDate() + 1);
      }

      // Remove existing future slots and add new ones
      const now = new Date();
      schedule.timeSlots = schedule.timeSlots.filter(slot => 
        slot.startTime <= now || slot.status === 'booked'
      );
      
      schedule.timeSlots.push(...newSlots);
      schedule.updatedAt = new Date();
      
      await schedule.save();

      this.logger.info('Time slots generated', { 
        scheduleId, 
        slotsGenerated: newSlots.length, 
        daysAhead 
      });
      
      return newSlots.length;
    } catch (error) {
      this.logger.error('Error generating time slots', { error: error.message, scheduleId });
      throw error;
    }
  }

  /**
   * Generate slots for a specific day
   */
  generateDaySlots(date, dayAvailability, generalAvailability) {
    const slots = [];
    const startTime = this.parseTime(dayAvailability.startTime);
    const endTime = this.parseTime(dayAvailability.endTime);
    const duration = generalAvailability.preferredDuration;
    const bufferTime = generalAvailability.bufferTime;
    
    const slotDate = new Date(date);
    slotDate.setHours(startTime.hours, startTime.minutes, 0, 0);
    
    const endDateTime = new Date(date);
    endDateTime.setHours(endTime.hours, endTime.minutes, 0, 0);
    
    let slotCount = 0;
    
    while (slotDate < endDateTime && slotCount < generalAvailability.maxViewingsPerDay) {
      const slotEndTime = new Date(slotDate.getTime() + (duration * 60 * 1000));
      
      if (slotEndTime <= endDateTime) {
        slots.push({
          startTime: new Date(slotDate),
          endTime: new Date(slotEndTime),
          duration,
          status: 'available',
          requestQueue: []
        });
        
        // Add buffer time for next slot
        slotDate.setTime(slotDate.getTime() + ((duration + bufferTime) * 60 * 1000));
        slotCount++;
      } else {
        break;
      }
    }
    
    return slots;
  }

  /**
   * Request a viewing slot
   */
  async requestViewing(applicationId, preferredSlots, notes = '') {
    try {
      const application = await Application.findById(applicationId)
        .populate('property.propertyId')
        .populate('applicant.userId');
        
      if (!application) {
        throw new Error('Application not found');
      }

      const schedule = await ViewingSchedule.findOne({
        'property.propertyId': application.property.propertyId
      });

      if (!schedule) {
        throw new Error('No viewing schedule found for this property');
      }

      const results = [];
      
      for (const slotRequest of preferredSlots) {
        const slot = schedule.timeSlots.id(slotRequest.slotId);
        if (!slot) {
          results.push({
            slotId: slotRequest.slotId,
            status: 'error',
            message: 'Slot not found'
          });
          continue;
        }

        // Check if slot is available or can queue
        if (slot.status === 'available') {
          if (schedule.availability.autoApproval) {
            // Auto-approve the booking
            slot.status = 'booked';
            slot.bookedBy = applicationId;
            slot.bookedAt = new Date();
            slot.attendees = [application.applicant.userId._id];
            slot.notes = notes;
            
            results.push({
              slotId: slotRequest.slotId,
              status: 'booked',
              message: 'Automatically approved'
            });
          } else {
            // Add to queue for manual approval
            slot.requestQueue.push({
              applicationId: applicationId,
              requestedAt: new Date(),
              priority: slotRequest.priority || 0,
              notes: notes
            });
            
            results.push({
              slotId: slotRequest.slotId,
              status: 'queued',
              message: 'Added to queue for approval'
            });
          }
        } else if (slot.status === 'booked' && slot.type === 'group' && slot.attendees.length < slot.maxAttendees) {
          // Can join group viewing
          slot.requestQueue.push({
            applicationId: applicationId,
            requestedAt: new Date(),
            priority: slotRequest.priority || 0,
            notes: notes
          });
          
          results.push({
            slotId: slotRequest.slotId,
            status: 'queued',
            message: 'Added to queue for group viewing'
          });
        } else {
          results.push({
            slotId: slotRequest.slotId,
            status: 'unavailable',
            message: 'Slot is not available'
          });
        }
      }

      schedule.stats.totalRequests += 1;
      schedule.updatedAt = new Date();
      await schedule.save();

      // Update application with viewing requests
      application.viewings.push({
        scheduledDate: null, // Will be set when approved
        duration: schedule.availability.preferredDuration,
        type: 'individual',
        status: 'requested',
        notes: notes
      });
      await application.save();

      this.logger.info('Viewing requested', { 
        applicationId, 
        propertyId: application.property.propertyId,
        results 
      });

      return {
        success: true,
        results,
        message: 'Viewing request processed'
      };

    } catch (error) {
      this.logger.error('Error requesting viewing', { error: error.message, applicationId });
      throw error;
    }
  }

  /**
   * Approve viewing request
   */
  async approveViewingRequest(scheduleId, slotId, applicationId, ownerUserId) {
    try {
      const schedule = await ViewingSchedule.findById(scheduleId);
      if (!schedule) {
        throw new Error('Schedule not found');
      }

      // Verify owner permission
      if (schedule.owner.userId.toString() !== ownerUserId.toString()) {
        throw new Error('Unauthorized: Not the property owner');
      }

      const slot = schedule.timeSlots.id(slotId);
      if (!slot) {
        throw new Error('Slot not found');
      }

      // Find the request in queue
      const requestIndex = slot.requestQueue.findIndex(
        req => req.applicationId.toString() === applicationId.toString()
      );

      if (requestIndex === -1) {
        throw new Error('Request not found in queue');
      }

      const request = slot.requestQueue[requestIndex];

      // Approve the request
      if (slot.status === 'available') {
        slot.status = 'booked';
        slot.bookedBy = applicationId;
        slot.bookedAt = new Date();
        slot.notes = request.notes;
      } else if (slot.type === 'group' && slot.attendees.length < slot.maxAttendees) {
        // Add to group viewing
        slot.attendees.push(applicationId);
      } else {
        throw new Error('Slot is not available for booking');
      }

      // Remove from queue
      slot.requestQueue.splice(requestIndex, 1);

      // Update application
      const application = await Application.findById(applicationId);
      if (application) {
        const viewing = application.viewings.find(v => v.status === 'requested');
        if (viewing) {
          viewing.status = 'scheduled';
          viewing.scheduledDate = slot.startTime;
          viewing.duration = slot.duration;
        }
        await application.save();
      }

      schedule.stats.totalViewings += 1;
      schedule.updatedAt = new Date();
      await schedule.save();

      this.logger.info('Viewing request approved', { 
        scheduleId, 
        slotId, 
        applicationId,
        approvedBy: ownerUserId 
      });

      return {
        success: true,
        message: 'Viewing request approved',
        viewingDetails: {
          startTime: slot.startTime,
          endTime: slot.endTime,
          duration: slot.duration,
          type: slot.type
        }
      };

    } catch (error) {
      this.logger.error('Error approving viewing request', { 
        error: error.message, 
        scheduleId, 
        slotId, 
        applicationId 
      });
      throw error;
    }
  }

  /**
   * Get optimized viewing schedule suggestions
   */
  async getScheduleSuggestions(propertyId, daysAhead = 7) {
    try {
      const schedule = await ViewingSchedule.findOne({ 'property.propertyId': propertyId });
      if (!schedule) {
        throw new Error('Schedule not found');
      }

      // Get all pending requests
      const pendingRequests = [];
      schedule.timeSlots.forEach(slot => {
        slot.requestQueue.forEach(request => {
          pendingRequests.push({
            slotId: slot._id,
            startTime: slot.startTime,
            endTime: slot.endTime,
            applicationId: request.applicationId,
            priority: request.priority,
            requestedAt: request.requestedAt
          });
        });
      });

      // Get applications with priority scores
      const applications = await Application.find({
        _id: { $in: pendingRequests.map(r => r.applicationId) }
      }).select('priority applicant.snapshot.name');

      // Create mapping of application priorities
      const appPriorities = {};
      applications.forEach(app => {
        appPriorities[app._id] = app.priority?.score || 50;
      });

      // Generate optimized suggestions
      const suggestions = this.generateOptimizedSuggestions(
        pendingRequests, 
        appPriorities, 
        schedule
      );

      return {
        propertyId,
        totalRequests: pendingRequests.length,
        suggestions,
        optimizationCriteria: {
          prioritizeHighScoreApplicants: true,
          minimizeConflicts: true,
          maximizeSlotUtilization: true,
          considerRequestTiming: true
        }
      };

    } catch (error) {
      this.logger.error('Error getting schedule suggestions', { error: error.message, propertyId });
      throw error;
    }
  }

  /**
   * Generate optimized scheduling suggestions
   */
  generateOptimizedSuggestions(requests, appPriorities, schedule) {
    const suggestions = [];
    
    // Group requests by time preference
    const timeGroups = {};
    requests.forEach(request => {
      const timeKey = request.startTime.toISOString();
      if (!timeGroups[timeKey]) {
        timeGroups[timeKey] = [];
      }
      timeGroups[timeKey].push(request);
    });

    // For each time slot, suggest the best candidate(s)
    Object.entries(timeGroups).forEach(([timeKey, slotRequests]) => {
      const slot = schedule.timeSlots.id(slotRequests[0].slotId);
      
      // Sort requests by combined priority score
      slotRequests.sort((a, b) => {
        const scoreA = (appPriorities[a.applicationId] || 50) + (a.priority || 0);
        const scoreB = (appPriorities[b.applicationId] || 50) + (b.priority || 0);
        
        // Also consider request timing (earlier requests get slight boost)
        const timingA = (Date.now() - a.requestedAt.getTime()) / (1000 * 60 * 60); // hours
        const timingB = (Date.now() - b.requestedAt.getTime()) / (1000 * 60 * 60);
        
        return (scoreB + timingB * 0.1) - (scoreA + timingA * 0.1);
      });

      const suggestion = {
        slotId: slot._id,
        startTime: slot.startTime,
        endTime: slot.endTime,
        currentStatus: slot.status,
        requestCount: slotRequests.length,
        recommendations: []
      };

      if (slot.type === 'individual' || slot.maxAttendees === 1) {
        // Individual viewing - recommend best candidate
        const bestRequest = slotRequests[0];
        suggestion.recommendations.push({
          action: 'approve',
          applicationId: bestRequest.applicationId,
          reason: `Highest priority applicant (score: ${appPriorities[bestRequest.applicationId] || 'N/A'})`,
          confidence: this.calculateConfidence(bestRequest, appPriorities)
        });
        
        // Suggest alternatives for rejected candidates
        if (slotRequests.length > 1) {
          suggestion.recommendations.push({
            action: 'suggest_alternative',
            count: slotRequests.length - 1,
            reason: 'Suggest alternative time slots for other applicants'
          });
        }
      } else if (slot.type === 'group') {
        // Group viewing - recommend multiple candidates
        const maxAttendees = slot.maxAttendees || 3;
        const topCandidates = slotRequests.slice(0, maxAttendees);
        
        topCandidates.forEach((request, index) => {
          suggestion.recommendations.push({
            action: 'approve',
            applicationId: request.applicationId,
            reason: `Group viewing candidate #${index + 1}`,
            confidence: this.calculateConfidence(request, appPriorities)
          });
        });
      }

      suggestions.push(suggestion);
    });

    // Sort suggestions by potential impact
    suggestions.sort((a, b) => {
      const impactA = a.requestCount * (a.recommendations[0]?.confidence || 0);
      const impactB = b.requestCount * (b.recommendations[0]?.confidence || 0);
      return impactB - impactA;
    });

    return suggestions.slice(0, 10); // Return top 10 suggestions
  }

  /**
   * Calculate confidence score for a recommendation
   */
  calculateConfidence(request, appPriorities) {
    const appScore = appPriorities[request.applicationId] || 50;
    const requestPriority = request.priority || 0;
    const timingFactor = Math.min(5, (Date.now() - request.requestedAt.getTime()) / (1000 * 60 * 60 * 24));
    
    // Confidence based on application quality and request characteristics
    const confidence = Math.min(100, 
      (appScore * 0.6) + 
      (requestPriority * 0.3) + 
      (timingFactor * 2) // Small boost for requests waiting longer
    );
    
    return Math.round(confidence);
  }

  /**
   * Update owner availability
   */
  async updateAvailability(scheduleId, newAvailability, ownerUserId) {
    try {
      const schedule = await ViewingSchedule.findById(scheduleId);
      if (!schedule) {
        throw new Error('Schedule not found');
      }

      // Verify owner permission
      if (schedule.owner.userId.toString() !== ownerUserId.toString()) {
        throw new Error('Unauthorized: Not the property owner');
      }

      schedule.availability = { ...schedule.availability, ...newAvailability };
      schedule.updatedAt = new Date();
      
      await schedule.save();

      // Regenerate slots based on new availability
      await this.generateTimeSlots(scheduleId, 14);

      this.logger.info('Availability updated', { scheduleId, ownerUserId });
      return schedule;

    } catch (error) {
      this.logger.error('Error updating availability', { error: error.message, scheduleId });
      throw error;
    }
  }

  /**
   * Get viewing statistics
   */
  async getViewingStatistics(propertyId, ownerUserId) {
    try {
      const schedule = await ViewingSchedule.findOne({ 'property.propertyId': propertyId });
      if (!schedule) {
        throw new Error('Schedule not found');
      }

      // Verify owner permission
      if (schedule.owner.userId.toString() !== ownerUserId.toString()) {
        throw new Error('Unauthorized: Not the property owner');
      }

      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));

      const stats = {
        overall: schedule.stats,
        recent: {
          totalSlots: 0,
          bookedSlots: 0,
          availableSlots: 0,
          pendingRequests: 0,
          upcomingViewings: 0
        },
        trends: {
          requestsPerDay: 0,
          bookingRate: 0,
          noShowRate: 0
        }
      };

      // Calculate recent statistics
      schedule.timeSlots.forEach(slot => {
        if (slot.startTime >= thirtyDaysAgo) {
          stats.recent.totalSlots++;
          
          if (slot.status === 'booked') {
            stats.recent.bookedSlots++;
            if (slot.startTime > now) {
              stats.recent.upcomingViewings++;
            }
          } else if (slot.status === 'available') {
            stats.recent.availableSlots++;
          }
          
          stats.recent.pendingRequests += slot.requestQueue.length;
        }
      });

      // Calculate trends
      if (stats.recent.totalSlots > 0) {
        stats.trends.bookingRate = Math.round((stats.recent.bookedSlots / stats.recent.totalSlots) * 100);
      }
      
      if (schedule.stats.completedViewings > 0) {
        stats.trends.noShowRate = Math.round((schedule.stats.noShows / schedule.stats.completedViewings) * 100);
      }

      stats.trends.requestsPerDay = Math.round(schedule.stats.totalRequests / 30);

      return stats;

    } catch (error) {
      this.logger.error('Error getting viewing statistics', { error: error.message, propertyId });
      throw error;
    }
  }

  // Helper methods
  getDayName(dayIndex) {
    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    return days[dayIndex];
  }

  parseTime(timeString) {
    const [hours, minutes] = timeString.split(':').map(Number);
    return { hours, minutes };
  }

  isUnavailableDate(date, unavailableDates) {
    return unavailableDates.some(unavailable => {
      const startDate = new Date(unavailable.startDate);
      const endDate = unavailable.endDate ? new Date(unavailable.endDate) : startDate;
      return date >= startDate && date <= endDate;
    });
  }
}

module.exports = { ViewingSchedulerService: new ViewingSchedulerService(), ViewingSchedule };