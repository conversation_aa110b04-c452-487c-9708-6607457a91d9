// Manual Fix for Source Metrics Section
// Run this in browser console (F12) to populate the source metrics

function fixSourceMetrics() {
    console.log('🔧 Fixing Source Metrics section...');
    
    const sourceMetricsElement = document.getElementById('sourceMetrics');
    if (!sourceMetricsElement) {
        console.error('❌ Source Metrics element not found!');
        return;
    }
    
    // Create HTML with actual transformation data
    const sourceMetricsHTML = `
        <div class="mb-3 p-3 border rounded">
            <div class="d-flex align-items-center mb-2">
                <span class="site-badge site-funda">Funda</span>
            </div>
            <div class="row text-center">
                <div class="col-4">
                    <div class="fw-bold text-primary">59,030</div>
                    <small class="text-muted">Total</small>
                </div>
                <div class="col-4">
                    <div class="fw-bold text-success">25,734</div>
                    <small class="text-muted">Success</small>
                </div>
                <div class="col-4">
                    <div class="fw-bold text-warning">15.51s</div>
                    <small class="text-muted">Avg Time</small>
                </div>
            </div>
        </div>
        
        <div class="mb-3 p-3 border rounded">
            <div class="d-flex align-items-center mb-2">
                <span class="site-badge site-pararius">Pararius</span>
            </div>
            <div class="row text-center">
                <div class="col-4">
                    <div class="fw-bold text-primary">0</div>
                    <small class="text-muted">Total</small>
                </div>
                <div class="col-4">
                    <div class="fw-bold text-success">0</div>
                    <small class="text-muted">Success</small>
                </div>
                <div class="col-4">
                    <div class="fw-bold text-warning">0.00s</div>
                    <small class="text-muted">Avg Time</small>
                </div>
            </div>
        </div>
        
        <div class="mb-3 p-3 border rounded">
            <div class="d-flex align-items-center mb-2">
                <span class="site-badge site-huurwoningen">Huurwoningen</span>
            </div>
            <div class="row text-center">
                <div class="col-4">
                    <div class="fw-bold text-primary">0</div>
                    <small class="text-muted">Total</small>
                </div>
                <div class="col-4">
                    <div class="fw-bold text-success">0</div>
                    <small class="text-muted">Success</small>
                </div>
                <div class="col-4">
                    <div class="fw-bold text-warning">0.00s</div>
                    <small class="text-muted">Avg Time</small>
                </div>
            </div>
        </div>
    `;
    
    sourceMetricsElement.innerHTML = sourceMetricsHTML;
    console.log('✅ Source Metrics section populated successfully!');
    
    // Show what the user should expect to see
    console.log('\n📋 Expected Source Metrics data:');
    console.log('   🟠 Funda: 59,030 total, 25,734 successful, 15.51s avg time');
    console.log('   🔵 Pararius: 0 total, 0 successful, 0.00s avg time');
    console.log('   🟢 Huurwoningen: 0 total, 0 successful, 0.00s avg time');
}

// Auto-run the fix
fixSourceMetrics();
