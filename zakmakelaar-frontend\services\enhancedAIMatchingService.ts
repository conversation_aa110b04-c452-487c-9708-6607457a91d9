import { UserPreferences, User } from './authService';
import { Listing } from './listingsService';
import { aiService, MatchingResult } from './aiService';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface EnhancedPropertyMatch extends MatchingResult {
  id: string;
  listing: Listing;
  matchPercentage: number;
  keyHighlights: string[];
  aiSummary: string;
  aiInsights: string[];
  priceAnalysis: {
    marketComparison: 'below' | 'at' | 'above';
    valueRating: number;
    pricePerSqm?: number;
  };
  locationScore: number;
  amenityScore: number;
  transportScore: number;
  timestamp: Date;
  viewed: boolean;
  applied: boolean;
  saved: boolean;
  userInteractions: UserInteraction[];
}

export interface UserInteraction {
  type: 'view' | 'save' | 'unsave' | 'apply' | 'skip' | 'like' | 'dislike';
  timestamp: Date;
  duration?: number; // for view interactions
  context?: string;
}

export interface MatchingPreferences {
  weights: {
    location: number;
    price: number;
    size: number;
    amenities: number;
    transport: number;
  };
  learningEnabled: boolean;
  adaptiveScoring: boolean;
}

export interface MarketInsight {
  type: 'price_trend' | 'availability' | 'competition' | 'recommendation';
  title: string;
  description: string;
  impact: 'positive' | 'negative' | 'neutral';
  actionable: boolean;
  action?: string;
}

class EnhancedAIMatchingService {
  private static instance: EnhancedAIMatchingService;
  private userInteractions: Map<string, UserInteraction[]> = new Map();
  private matchingPreferences: Map<string, MatchingPreferences> = new Map();
  private lastMatchingResults: Map<string, EnhancedPropertyMatch[]> = new Map();

  static getInstance(): EnhancedAIMatchingService {
    if (!EnhancedAIMatchingService.instance) {
      EnhancedAIMatchingService.instance = new EnhancedAIMatchingService();
    }
    return EnhancedAIMatchingService.instance;
  }

  private constructor() {
    this.loadUserData();
  }

  private async loadUserData(): Promise<void> {
    try {
      const interactionsData = await AsyncStorage.getItem('user_interactions');
      const preferencesData = await AsyncStorage.getItem('matching_preferences');
      
      if (interactionsData) {
        const parsed = JSON.parse(interactionsData);
        this.userInteractions = new Map(Object.entries(parsed));
      }
      
      if (preferencesData) {
        const parsed = JSON.parse(preferencesData);
        this.matchingPreferences = new Map(Object.entries(parsed));
      }
    } catch (error) {
      console.error('Failed to load user data:', error);
    }
  }

  private async saveUserData(): Promise<void> {
    try {
      await AsyncStorage.setItem(
        'user_interactions',
        JSON.stringify(Object.fromEntries(this.userInteractions))
      );
      await AsyncStorage.setItem(
        'matching_preferences',
        JSON.stringify(Object.fromEntries(this.matchingPreferences))
      );
    } catch (error) {
      console.error('Failed to save user data:', error);
    }
  }

  async findMatches(
    preferences: UserPreferences,
    user: User,
    listings: Listing[]
  ): Promise<EnhancedPropertyMatch[]> {
    try {
      // Get user's matching preferences or create defaults
      const matchingPrefs = this.getMatchingPreferences(user.email || 'default');
      
      // Apply learning-based adjustments to preferences
      const adjustedPreferences = this.applyLearning(preferences, user.email || 'default');
      
      // Score and rank properties
      const scoredMatches = await Promise.all(
        listings.map(listing => this.scoreProperty(listing, adjustedPreferences, user, matchingPrefs))
      );
      
      // Filter and sort by match percentage
      const filteredMatches = scoredMatches
        .filter(match => match.matchPercentage >= 30) // Minimum threshold
        .sort((a, b) => b.matchPercentage - a.matchPercentage);
      
      // Generate AI insights and summaries
      const enhancedMatches = await Promise.all(
        filteredMatches.map(match => this.enhanceMatchWithAI(match, adjustedPreferences, user))
      );
      
      // Store results for learning
      this.lastMatchingResults.set(user.email || 'default', enhancedMatches);
      
      return enhancedMatches;
    } catch (error) {
      console.error('Enhanced matching failed:', error);
      throw error;
    }
  }

  private async scoreProperty(
    listing: Listing,
    preferences: UserPreferences,
    user: User,
    matchingPrefs: MatchingPreferences
  ): Promise<EnhancedPropertyMatch> {
    // Location scoring
    const locationScore = this.calculateLocationScore(listing, preferences);
    
    // Price scoring
    const priceScore = this.calculatePriceScore(listing, preferences);
    
    // Size scoring
    const sizeScore = this.calculateSizeScore(listing, preferences);
    
    // Amenity scoring
    const amenityScore = this.calculateAmenityScore(listing, preferences);
    
    // Transport scoring
    const transportScore = this.calculateTransportScore(listing, preferences);
    
    // Calculate weighted match percentage
    const matchPercentage = Math.round(
      (locationScore * matchingPrefs.weights.location +
       priceScore * matchingPrefs.weights.price +
       sizeScore * matchingPrefs.weights.size +
       amenityScore * matchingPrefs.weights.amenities +
       transportScore * matchingPrefs.weights.transport) / 
      Object.values(matchingPrefs.weights).reduce((a, b) => a + b, 0)
    );
    
    // Generate key highlights
    const keyHighlights = this.generateKeyHighlights(listing, preferences, {
      locationScore,
      priceScore,
      sizeScore,
      amenityScore,
      transportScore
    });
    
    // Price analysis
    const priceAnalysis = this.analyzePricing(listing, preferences);
    
    return {
      id: `match_${listing._id || listing.title?.replace(/\s+/g, '_')}_${Date.now()}`,
      listing,
      matchPercentage,
      keyHighlights,
      aiSummary: '', // Will be filled by AI enhancement
      aiInsights: [], // Will be filled by AI enhancement
      priceAnalysis,
      locationScore,
      amenityScore,
      transportScore,
      timestamp: new Date(),
      viewed: false,
      applied: false,
      saved: false,
      userInteractions: [],
      // MatchingResult properties
      score: matchPercentage / 100,
      reasons: keyHighlights,
      pros: [],
      cons: [],
      // recommendation: matchPercentage >= 80 ? 'highly_recommended' : 
      //                matchPercentage >= 60 ? 'recommended' : 'consider'
    };
  }

  private async enhanceMatchWithAI(
    match: EnhancedPropertyMatch,
    preferences: UserPreferences,
    user: User
  ): Promise<EnhancedPropertyMatch> {
    try {
      // Generate AI summary
      const summaryPrompt = `Analyze this property match for a user:
Property: ${match.listing.title}
Location: ${match.listing.location}
Price: ${match.listing.price}
Size: ${match.listing.size}
Match Score: ${match.matchPercentage}%
User Preferences: ${JSON.stringify(preferences)}

Generate a concise, personalized summary highlighting why this property matches the user's needs.`;
      
      const aiSummary = await this.generateAIText(summaryPrompt);
      
      // Generate AI insights
      const insightsPrompt = `Based on this property match, provide 3-4 actionable insights:
Property: ${match.listing.title}
Match Score: ${match.matchPercentage}%
Key Highlights: ${match.keyHighlights.join(', ')}

Focus on practical advice, market context, and decision-making factors.`;
      
      const insightsText = await this.generateAIText(insightsPrompt);
      const aiInsights = insightsText.split('\n').filter(line => line.trim().length > 0);
      
      return {
        ...match,
        aiSummary,
        aiInsights
      };
    } catch (error) {
      console.error('AI enhancement failed:', error);
      return {
        ...match,
        aiSummary: 'AI analysis temporarily unavailable',
        aiInsights: ['Property analysis will be available shortly']
      };
    }
  }

  private async generateAIText(prompt: string): Promise<string> {
    try {
      // Use existing AI service for text generation
      const response = await aiService.translateText({
        text: prompt,
        targetLanguage: 'en'
      });
      
      return response.data?.translatedText || 'Analysis temporarily unavailable';
    } catch (error) {
      console.error('AI text generation failed:', error);
      return 'Analysis temporarily unavailable';
    }
  }

  private calculateLocationScore(listing: Listing, preferences: UserPreferences): number {
    if (!preferences.preferredLocations?.length) return 70;
    
    const locationStr = typeof listing.location === 'string' ? listing.location : 
      (listing.location?.city || listing.location?.address || '');
    const isPreferredLocation = preferences.preferredLocations.some(loc => 
      locationStr.toLowerCase().includes(loc.toLowerCase())
    );
    
    return isPreferredLocation ? 95 : 40;
  }

  private calculatePriceScore(listing: Listing, preferences: UserPreferences): number {
    const price = this.extractNumericPrice(listing.price?.toString() || '');
    if (!price || !preferences.maxRent) return 50;
    
    const ratio = price / preferences.maxRent;
    
    if (ratio <= 0.8) return 100; // Great value
    if (ratio <= 0.9) return 90;  // Good value
    if (ratio <= 1.0) return 80;  // Within budget
    if (ratio <= 1.1) return 60;  // Slightly over
    return 30; // Too expensive
  }

  private calculateSizeScore(listing: Listing, preferences: UserPreferences): number {
    const size = this.extractNumericSize(listing.size?.toString() || '');
    if (!size) return 70;
    
    // Assume minimum desired size based on room count
    const minDesiredSize = (preferences.minRooms || 1) * 25; // 25m² per room
    
    if (size >= minDesiredSize * 1.5) return 100;
    if (size >= minDesiredSize * 1.2) return 90;
    if (size >= minDesiredSize) return 80;
    if (size >= minDesiredSize * 0.8) return 60;
    return 40;
  }

  private calculateAmenityScore(listing: Listing, preferences: UserPreferences): number {
    const amenities = (listing as any).amenities || [];
    const desiredAmenities = preferences.amenities || [];
    
    if (desiredAmenities.length === 0) return 70;
    
    const matchedAmenities = desiredAmenities.filter(desired =>
      amenities.some((available: any) => 
        available.toLowerCase().includes(desired.toLowerCase())
      )
    );
    
    return Math.round((matchedAmenities.length / desiredAmenities.length) * 100);
  }

  private calculateTransportScore(listing: Listing, preferences: UserPreferences): number {
    // Simplified transport scoring - in real implementation, would use maps API
    const hasGoodTransport = listing.description?.toLowerCase().includes('station') ||
                           listing.description?.toLowerCase().includes('metro') ||
                           listing.description?.toLowerCase().includes('bus');
    
    return hasGoodTransport ? 85 : 60;
  }

  private generateKeyHighlights(
    listing: Listing,
    preferences: UserPreferences,
    scores: { [key: string]: number }
  ): string[] {
    const highlights: string[] = [];
    
    if (scores.priceScore >= 90) {
      highlights.push('💰 Excellent value for money');
    } else if (scores.priceScore >= 80) {
      highlights.push('💵 Within your budget');
    }
    
    if (scores.locationScore >= 90) {
      highlights.push('📍 Perfect location match');
    }
    
    if (scores.amenityScore >= 80) {
      highlights.push('✨ Has most desired amenities');
    }
    
    if (scores.transportScore >= 80) {
      highlights.push('🚇 Great public transport access');
    }
    
    // Add property-specific highlights
    if (listing.size) {
      const numericSize = this.extractNumericSize(listing.size);
      if (numericSize && numericSize > 80) {
        highlights.push('🏠 Spacious living space');
      }
    }
    
    if ((listing as any).amenities?.some((a: any) => a.toLowerCase().includes('balcony'))) {
      highlights.push('🌿 Private outdoor space');
    }
    
    return highlights.slice(0, 4); // Limit to 4 highlights
  }

  private analyzePricing(listing: Listing, preferences: UserPreferences): {
    marketComparison: 'below' | 'at' | 'above';
    valueRating: number;
    pricePerSqm?: number;
  } {
    const price = this.extractNumericPrice(listing.price?.toString() || '');
    const size = this.extractNumericSize(listing.size?.toString() || '');
    
    let pricePerSqm: number | undefined;
    if (price && size) {
      pricePerSqm = Math.round(price / size);
    }
    
    // Simplified market comparison
    const marketComparison: 'below' | 'at' | 'above' = 
      price && preferences.maxRent && price < preferences.maxRent * 0.9 ? 'below' :
      price && preferences.maxRent && price > preferences.maxRent * 1.1 ? 'above' : 'at';
    
    const valueRating = this.calculatePriceScore(listing, preferences);
    
    return {
      marketComparison,
      valueRating,
      pricePerSqm
    };
  }

  private extractNumericPrice(price: string): number | null {
    if (!price) return null;
    const match = price.match(/[\d,]+/);
    return match ? parseInt(match[0].replace(/,/g, '')) : null;
  }

  private extractNumericSize(size: string): number | null {
    if (!size) return null;
    const match = size.match(/(\d+)/);
    return match ? parseInt(match[1]) : null;
  }

  private getMatchingPreferences(userId: string): MatchingPreferences {
    return this.matchingPreferences.get(userId) || {
      weights: {
        location: 0.25,
        price: 0.25,
        size: 0.2,
        amenities: 0.15,
        transport: 0.15
      },
      learningEnabled: true,
      adaptiveScoring: true
    };
  }

  private applyLearning(preferences: UserPreferences, userId: string): UserPreferences {
    const interactions = this.userInteractions.get(userId) || [];
    const matchingPrefs = this.getMatchingPreferences(userId);
    
    if (!matchingPrefs.learningEnabled || interactions.length < 10) {
      return preferences;
    }
    
    // Analyze user behavior and adjust preferences
    const adjustedPreferences = { ...preferences };
    
    // Example: If user consistently saves expensive properties, increase budget tolerance
    const savedExpensiveCount = interactions.filter(i => 
      i.type === 'save' && i.context?.includes('expensive')
    ).length;
    
    if (savedExpensiveCount > 3 && adjustedPreferences.maxRent) {
      adjustedPreferences.maxRent *= 1.1; // Increase by 10%
    }
    
    return adjustedPreferences;
  }

  async recordInteraction(
    userId: string,
    matchId: string,
    interaction: Omit<UserInteraction, 'timestamp'>
  ): Promise<void> {
    const userInteractions = this.userInteractions.get(userId) || [];
    userInteractions.push({
      ...interaction,
      timestamp: new Date()
    });
    
    this.userInteractions.set(userId, userInteractions);
    await this.saveUserData();
    
    // Update matching preferences based on interaction
    if (interaction.type === 'like' || interaction.type === 'save') {
      this.updateMatchingWeights(userId, matchId, 'positive');
    } else if (interaction.type === 'dislike' || interaction.type === 'skip') {
      this.updateMatchingWeights(userId, matchId, 'negative');
    }
  }

  private updateMatchingWeights(userId: string, matchId: string, feedback: 'positive' | 'negative'): void {
    const matchingPrefs = this.getMatchingPreferences(userId);
    const lastResults = this.lastMatchingResults.get(userId) || [];
    const match = lastResults.find(m => m.id === matchId);
    
    if (!match || !matchingPrefs.adaptiveScoring) return;
    
    // Adjust weights based on which factors contributed to the match
    const adjustment = feedback === 'positive' ? 0.02 : -0.02;
    
    if (match.locationScore > 80) {
      matchingPrefs.weights.location += adjustment;
    }
    if (match.priceAnalysis.valueRating > 80) {
      matchingPrefs.weights.price += adjustment;
    }
    if (match.amenityScore > 80) {
      matchingPrefs.weights.amenities += adjustment;
    }
    
    // Normalize weights
    const totalWeight = Object.values(matchingPrefs.weights).reduce((a, b) => a + b, 0);
    Object.keys(matchingPrefs.weights).forEach(key => {
      matchingPrefs.weights[key as keyof typeof matchingPrefs.weights] /= totalWeight;
    });
    
    this.matchingPreferences.set(userId, matchingPrefs);
    this.saveUserData();
  }

  async generateMarketInsights(
    preferences: UserPreferences,
    currentMatches: EnhancedPropertyMatch[]
  ): Promise<MarketInsight[]> {
    const insights: MarketInsight[] = [];
    
    // No matches insight
    if (currentMatches.length === 0) {
      insights.push({
        type: 'recommendation',
        title: 'Expand Your Search',
        description: 'Consider increasing your budget by 10-15% or exploring nearby areas to find more options.',
        impact: 'positive',
        actionable: true,
        action: 'Adjust preferences'
      });
    }
    
    // Price insights
    const avgPrice = currentMatches.reduce((sum, match) => {
      const price = this.extractNumericPrice(match.listing.price?.toString() || '');
      return sum + (price || 0);
    }, 0) / currentMatches.length;
    
    if (preferences.maxRent && avgPrice > preferences.maxRent * 1.2) {
      insights.push({
        type: 'price_trend',
        title: 'Market Above Budget',
        description: 'Properties in your preferred areas are trending 20% above your budget.',
        impact: 'negative',
        actionable: true,
        action: 'Consider nearby areas'
      });
    }
    
    // Competition insights
    if (currentMatches.length > 0 && currentMatches.length < 5) {
      insights.push({
        type: 'competition',
        title: 'Limited Availability',
        description: 'Few properties match your criteria. Act quickly on promising options.',
        impact: 'neutral',
        actionable: true,
        action: 'Enable notifications'
      });
    }
    
    return insights;
  }
}

export const enhancedAIMatchingService = EnhancedAIMatchingService.getInstance();
