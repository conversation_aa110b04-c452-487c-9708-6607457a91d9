/**
 * Adaptive Rate Limiting System
 *
 * Intelligent rate limiting that adapts based on success rates,
 * platform behavior, and detection patterns.
 */

const { loggers } = require("./logger");

class AdaptiveRateLimiter {
  constructor() {
    this.platformLimits = new Map();
    this.userLimits = new Map();
    this.successRates = new Map();
    this.detectionEvents = new Map();

    // Default platform configurations
    this.defaultLimits = {
      "funda.nl": {
        maxPerHour: 8,
        minDelay: 45000, // 45 seconds
        maxDelay: 180000, // 3 minutes
        burstLimit: 2,
        cooldownPeriod: 300000, // 5 minutes
      },
      "pararius.nl": {
        maxPerHour: 12,
        minDelay: 30000, // 30 seconds
        maxDelay: 120000, // 2 minutes
        burstLimit: 3,
        cooldownPeriod: 240000, // 4 minutes
      },
      "kamernet.nl": {
        maxPerHour: 6,
        minDelay: 60000, // 1 minute
        maxDelay: 300000, // 5 minutes
        burstLimit: 1,
        cooldownPeriod: 600000, // 10 minutes
      },
      generic: {
        maxPerHour: 5,
        minDelay: 90000, // 1.5 minutes
        maxDelay: 360000, // 6 minutes
        burstLimit: 1,
        cooldownPeriod: 720000, // 12 minutes
      },
    };

    // Success rate thresholds for adaptation
    this.adaptationThresholds = {
      excellent: 0.9, // > 90% success rate
      good: 0.7, // > 70% success rate
      poor: 0.5, // > 50% success rate
      critical: 0.3, // > 30% success rate
    };

    // Cleanup old data every hour
    setInterval(() => this._cleanup(), 3600000);
  }

  /**
   * Check if an application can be submitted
   */
  async canSubmit(userId, platform, url) {
    const platformKey = this._getPlatformKey(platform, url);
    const userKey = `${userId}:${platformKey}`;

    const now = Date.now();

    // Check platform-wide limits
    const platformAllowed = this._checkPlatformLimits(platformKey, now);
    if (!platformAllowed.allowed) {
      return {
        allowed: false,
        reason: "platform_limit_exceeded",
        waitTime: platformAllowed.waitTime,
        details: platformAllowed.details,
      };
    }

    // Check user-specific limits
    const userAllowed = this._checkUserLimits(userKey, now);
    if (!userAllowed.allowed) {
      return {
        allowed: false,
        reason: "user_limit_exceeded",
        waitTime: userAllowed.waitTime,
        details: userAllowed.details,
      };
    }

    // Check for recent detection events
    const detectionCheck = this._checkDetectionCooldown(platformKey, now);
    if (!detectionCheck.allowed) {
      return {
        allowed: false,
        reason: "detection_cooldown",
        waitTime: detectionCheck.waitTime,
        details: detectionCheck.details,
      };
    }

    return {
      allowed: true,
      recommendedDelay: this._calculateOptimalDelay(platformKey, userKey, now),
    };
  }

  /**
   * Record a successful application
   */
  recordSuccess(userId, platform, url, responseTime = 0) {
    const platformKey = this._getPlatformKey(platform, url);
    const userKey = `${userId}:${platformKey}`;
    const now = Date.now();

    // Record platform activity
    this._recordPlatformActivity(platformKey, now, true, responseTime);

    // Record user activity
    this._recordUserActivity(userKey, now, true);

    // Update success rates
    this._updateSuccessRate(platformKey, true);

    loggers.app.debug("Recorded successful application", {
      userId,
      platform: platformKey,
      responseTime,
    });
  }

  /**
   * Record a failed application
   */
  recordFailure(userId, platform, url, error, responseTime = 0) {
    const platformKey = this._getPlatformKey(platform, url);
    const userKey = `${userId}:${platformKey}`;
    const now = Date.now();

    // Categorize the error
    const errorCategory = this._categorizeError(error);

    // Record platform activity
    this._recordPlatformActivity(
      platformKey,
      now,
      false,
      responseTime,
      errorCategory
    );

    // Record user activity
    this._recordUserActivity(userKey, now, false);

    // Update success rates
    this._updateSuccessRate(platformKey, false);

    // Handle detection-related failures
    if (errorCategory === "detection" || errorCategory === "blocked") {
      this._recordDetectionEvent(platformKey, now, errorCategory);
    }

    loggers.app.debug("Recorded failed application", {
      userId,
      platform: platformKey,
      errorCategory,
      responseTime,
    });
  }

  /**
   * Get current limits for a platform
   */
  getCurrentLimits(platform, url) {
    const platformKey = this._getPlatformKey(platform, url);
    const platformLimits = this.platformLimits.get(platformKey);
    const defaultLimits = this._getDefaultLimits(platformKey);
    const successRate = this._getSuccessRate(platformKey);

    return {
      platform: platformKey,
      limits: platformLimits ? platformLimits.current : defaultLimits,
      successRate,
      adaptationLevel: this._getAdaptationLevel(successRate),
      lastActivity: platformLimits ? platformLimits.lastActivity : null,
    };
  }

  /**
   * Check platform-wide limits
   */
  _checkPlatformLimits(platformKey, now) {
    const limits =
      this.platformLimits.get(platformKey) ||
      this._initializePlatformLimits(platformKey);

    // Clean old activities
    limits.activities = limits.activities.filter(
      (activity) => now - activity.timestamp < 3600000 // Keep last hour
    );

    // Check hourly limit
    if (limits.activities.length >= limits.current.maxPerHour) {
      const oldestActivity = Math.min(
        ...limits.activities.map((a) => a.timestamp)
      );
      const waitTime = 3600000 - (now - oldestActivity);

      return {
        allowed: false,
        waitTime,
        details: `Platform hourly limit exceeded (${limits.activities.length}/${limits.current.maxPerHour})`,
      };
    }

    // Check minimum delay since last activity
    if (
      limits.lastActivity &&
      now - limits.lastActivity < limits.current.minDelay
    ) {
      const waitTime = limits.current.minDelay - (now - limits.lastActivity);

      return {
        allowed: false,
        waitTime,
        details: `Minimum delay not met (${Math.round(
          waitTime / 1000
        )}s remaining)`,
      };
    }

    return { allowed: true };
  }

  /**
   * Check user-specific limits
   */
  _checkUserLimits(userKey, now) {
    const userLimits =
      this.userLimits.get(userKey) || this._initializeUserLimits(userKey);

    // Clean old activities
    userLimits.activities = userLimits.activities.filter(
      (activity) => now - activity.timestamp < 86400000 // Keep last 24 hours
    );

    // Check daily limit (user-specific)
    const dailyLimit = 20; // Max 20 applications per user per day
    if (userLimits.activities.length >= dailyLimit) {
      const oldestActivity = Math.min(
        ...userLimits.activities.map((a) => a.timestamp)
      );
      const waitTime = 86400000 - (now - oldestActivity);

      return {
        allowed: false,
        waitTime,
        details: `User daily limit exceeded (${userLimits.activities.length}/${dailyLimit})`,
      };
    }

    return { allowed: true };
  }

  /**
   * Check detection cooldown
   */
  _checkDetectionCooldown(platformKey, now) {
    const detectionData = this.detectionEvents.get(platformKey);

    if (!detectionData || detectionData.events.length === 0) {
      return { allowed: true };
    }

    // Clean old events
    detectionData.events = detectionData.events.filter(
      (event) => now - event.timestamp < 3600000 // Keep last hour
    );

    // Check for recent detection events
    const recentEvents = detectionData.events.filter(
      (event) => now - event.timestamp < detectionData.cooldownPeriod
    );

    if (recentEvents.length > 0) {
      const latestEvent = Math.max(...recentEvents.map((e) => e.timestamp));
      const waitTime = detectionData.cooldownPeriod - (now - latestEvent);

      return {
        allowed: false,
        waitTime,
        details: `Detection cooldown active (${Math.round(
          waitTime / 60000
        )}m remaining)`,
      };
    }

    return { allowed: true };
  }

  /**
   * Calculate optimal delay based on current conditions
   */
  _calculateOptimalDelay(platformKey, userKey, now) {
    const limits =
      this.platformLimits.get(platformKey) ||
      this._getDefaultLimits(platformKey);
    const successRate = this._getSuccessRate(platformKey);

    let baseDelay = limits.current.minDelay;

    // Adjust based on success rate
    if (successRate < this.adaptationThresholds.critical) {
      baseDelay *= 3; // Triple delay for critical success rates
    } else if (successRate < this.adaptationThresholds.poor) {
      baseDelay *= 2; // Double delay for poor success rates
    } else if (successRate < this.adaptationThresholds.good) {
      baseDelay *= 1.5; // 50% increase for below-good success rates
    }

    // Add randomization to avoid patterns
    const randomFactor = 0.8 + Math.random() * 0.4; // 80% to 120%
    baseDelay *= randomFactor;

    // Ensure within bounds
    return Math.min(
      Math.max(baseDelay, limits.current.minDelay),
      limits.current.maxDelay
    );
  }

  /**
   * Initialize platform limits
   */
  _initializePlatformLimits(platformKey) {
    const defaultLimits = this._getDefaultLimits(platformKey);
    const limits = {
      current: { ...defaultLimits },
      activities: [],
      lastActivity: null,
      adaptationHistory: [],
    };

    this.platformLimits.set(platformKey, limits);
    return limits;
  }

  /**
   * Initialize user limits
   */
  _initializeUserLimits(userKey) {
    const limits = {
      activities: [],
      lastActivity: null,
    };

    this.userLimits.set(userKey, limits);
    return limits;
  }

  /**
   * Get default limits for platform
   */
  _getDefaultLimits(platformKey) {
    return this.defaultLimits[platformKey] || this.defaultLimits.generic;
  }

  /**
   * Get platform key from URL
   */
  _getPlatformKey(platform, url) {
    if (platform && platform !== "generic") {
      return platform;
    }

    try {
      const hostname = new URL(url).hostname
        .toLowerCase()
        .replace(/^www\./, "");

      // Check if we have specific limits for this hostname
      if (this.defaultLimits[hostname]) {
        return hostname;
      }

      // Check for partial matches
      for (const knownPlatform of Object.keys(this.defaultLimits)) {
        if (hostname.includes(knownPlatform.split(".")[0])) {
          return knownPlatform;
        }
      }

      return "generic";
    } catch (error) {
      return "generic";
    }
  }

  /**
   * Record platform activity
   */
  _recordPlatformActivity(
    platformKey,
    timestamp,
    success,
    responseTime,
    errorCategory = null
  ) {
    const limits =
      this.platformLimits.get(platformKey) ||
      this._initializePlatformLimits(platformKey);

    limits.activities.push({
      timestamp,
      success,
      responseTime,
      errorCategory,
    });

    limits.lastActivity = timestamp;
    this.platformLimits.set(platformKey, limits);
  }

  /**
   * Record user activity
   */
  _recordUserActivity(userKey, timestamp, success) {
    const limits =
      this.userLimits.get(userKey) || this._initializeUserLimits(userKey);

    limits.activities.push({
      timestamp,
      success,
    });

    limits.lastActivity = timestamp;
    this.userLimits.set(userKey, limits);
  }

  /**
   * Update success rate
   */
  _updateSuccessRate(platformKey, success) {
    const current = this.successRates.get(platformKey) || {
      successes: 0,
      total: 0,
    };

    current.total++;
    if (success) {
      current.successes++;
    }

    // Keep rolling window of last 100 attempts
    if (current.total > 100) {
      current.successes = Math.round(current.successes * 0.9);
      current.total = 90;
    }

    this.successRates.set(platformKey, current);
  }

  /**
   * Get success rate
   */
  _getSuccessRate(platformKey) {
    const data = this.successRates.get(platformKey);
    if (!data || data.total === 0) {
      return 1.0; // Assume good until proven otherwise
    }

    return data.successes / data.total;
  }

  /**
   * Get adaptation level based on success rate
   */
  _getAdaptationLevel(successRate) {
    if (successRate >= this.adaptationThresholds.excellent) return "excellent";
    if (successRate >= this.adaptationThresholds.good) return "good";
    if (successRate >= this.adaptationThresholds.poor) return "poor";
    if (successRate >= this.adaptationThresholds.critical) return "critical";
    return "failing";
  }

  /**
   * Record detection event
   */
  _recordDetectionEvent(platformKey, timestamp, category) {
    const data = this.detectionEvents.get(platformKey) || {
      events: [],
      cooldownPeriod: this._getDefaultLimits(platformKey).cooldownPeriod,
    };

    data.events.push({ timestamp, category });

    // Increase cooldown period if multiple recent detections
    const recentEvents = data.events.filter(
      (e) => timestamp - e.timestamp < 3600000
    );
    if (recentEvents.length > 2) {
      data.cooldownPeriod = Math.min(data.cooldownPeriod * 1.5, 1800000); // Max 30 minutes
    }

    this.detectionEvents.set(platformKey, data);
  }

  /**
   * Categorize error for rate limiting purposes
   */
  _categorizeError(error) {
    const message = error.message?.toLowerCase() || "";

    if (
      message.includes("blocked") ||
      message.includes("403") ||
      message.includes("access denied")
    ) {
      return "blocked";
    }
    if (
      message.includes("captcha") ||
      message.includes("bot") ||
      message.includes("automated")
    ) {
      return "detection";
    }
    if (message.includes("timeout") || message.includes("network")) {
      return "network";
    }
    if (
      message.includes("form") ||
      message.includes("selector") ||
      message.includes("element")
    ) {
      return "form";
    }

    return "unknown";
  }

  /**
   * Cleanup old data
   */
  _cleanup() {
    const now = Date.now();
    const oneDay = 86400000;

    // Cleanup platform limits
    for (const [key, limits] of this.platformLimits.entries()) {
      limits.activities = limits.activities.filter(
        (a) => now - a.timestamp < oneDay
      );
      if (
        limits.activities.length === 0 &&
        (!limits.lastActivity || now - limits.lastActivity > oneDay)
      ) {
        this.platformLimits.delete(key);
      }
    }

    // Cleanup user limits
    for (const [key, limits] of this.userLimits.entries()) {
      limits.activities = limits.activities.filter(
        (a) => now - a.timestamp < oneDay
      );
      if (
        limits.activities.length === 0 &&
        (!limits.lastActivity || now - limits.lastActivity > oneDay)
      ) {
        this.userLimits.delete(key);
      }
    }

    // Cleanup detection events
    for (const [key, data] of this.detectionEvents.entries()) {
      data.events = data.events.filter((e) => now - e.timestamp < oneDay);
      if (data.events.length === 0) {
        this.detectionEvents.delete(key);
      }
    }

    loggers.app.debug("Rate limiter cleanup completed", {
      platformLimits: this.platformLimits.size,
      userLimits: this.userLimits.size,
      detectionEvents: this.detectionEvents.size,
    });
  }
}

module.exports = AdaptiveRateLimiter;
