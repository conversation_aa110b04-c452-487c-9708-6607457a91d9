// Manual Fix for Success vs Failure Chart
// Run this in browser console (F12) to populate the chart

function fixSuccessFailureChart() {
    console.log('🔧 Fixing Success vs Failure chart...');
    
    if (!window.dashboard || !window.dashboard.charts || !window.dashboard.charts.successFailure) {
        console.error('❌ Success vs Failure chart not found!');
        return;
    }
    
    const chart = window.dashboard.charts.successFailure;
    
    // Use transformation metrics data (25,734 successful, 33,296 failed)
    const successfulTransformations = 25734;
    const failedTransformations = 33296;
    
    // Update chart data
    chart.data.datasets[0].data = [successfulTransformations, failedTransformations];
    
    // Force chart update
    chart.update('active');
    
    console.log(`✅ Success vs Failure chart updated!`);
    console.log(`   Successful: ${successfulTransformations.toLocaleString()}`);
    console.log(`   Failed: ${failedTransformations.toLocaleString()}`);
    console.log(`   Total: ${(successfulTransformations + failedTransformations).toLocaleString()}`);
}

// Auto-run the fix
fixSuccessFailureChart();
