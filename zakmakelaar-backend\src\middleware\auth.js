const jwt = require("jsonwebtoken");
const User = require("../models/User");
const config = require("../config/config");

const auth = async (req, res, next) => {
  try {
    const authHeader = req.header("Authorization");
    //console.log("Authorization header:", authHeader);
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res
        .status(401)
        .send({ error: "No or invalid Authorization header" });
    }
    const token = authHeader.replace("Bearer ", "");
    const data = jwt.verify(token, config.jwtSecret);
    const user = await User.findOne({ _id: data._id });
    if (!user) {
      throw new Error();
    }
    req.user = user;
    req.token = token;
    next();
  } catch (error) {
    console.error("Auth middleware error:", error);
    res.status(401).send({ error: "Not authorized to access this resource" });
  }
};

const requireAdmin = (req, res, next) => {
  if (req.user && req.user.role === "admin") {
    next();
  } else {
    res.status(403).json({
      status: "error",
      message: "Admin access required",
    });
  }
};

const requireRole = (roles) => {
  return (req, res, next) => {
    if (req.user && roles.includes(req.user.role)) {
      next();
    } else {
      res.status(403).json({
        status: "error",
        message: `Access denied. Required roles: ${roles.join(", ")}`,
      });
    }
  };
};

module.exports = { auth, requireAdmin, requireRole };
