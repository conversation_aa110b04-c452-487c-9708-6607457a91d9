require("dotenv").config();

// AI Configuration
const googleAIConfig = {
  apiKey: process.env.GOOGLE_AI_API_KEY,
  model: process.env.GOOGLE_AI_MODEL || "gemini-2.0-flash",
  maxTokens: parseInt(process.env.GOOGLE_AI_MAX_TOKENS) || 4000,
  temperature: parseFloat(process.env.GOOGLE_AI_TEMPERATURE) || 0.7,
  models: {
    analysis: process.env.GOOGLE_AI_MODEL || "gemini-2.0-flash",
    matching: process.env.GOOGLE_AI_MODEL || "gemini-2.0-flash",
    summarization: process.env.GOOGLE_AI_MODEL || "gemini-2.0-flash",
    translation: process.env.GOOGLE_AI_MODEL || "gemini-2.0-flash",
    conversation: process.env.GOOGLE_AI_MODEL || "gemini-2.0-flash",
  },
};

const openRouterConfig = {
  apiKey: process.env.OPENROUTER_API_KEY,
  baseURL: process.env.OPENROUTER_BASE_URL || "https://openrouter.ai/api/v1",
  model: process.env.OPENROUTER_DEFAULT_MODEL || "openai/gpt-oss-20b:free",
  maxTokens: parseInt(process.env.OPENROUTER_MAX_TOKENS) || 4000,
  temperature: parseFloat(process.env.OPENROUTER_TEMPERATURE) || 0.7,
  models: {
    analysis:
      process.env.OPENROUTER_ANALYSIS_MODEL || "openai/gpt-oss-20b:free",
    matching:
      process.env.OPENROUTER_MATCHING_MODEL || "openai/gpt-oss-20b:free",
    summarization:
      process.env.OPENROUTER_SUMMARIZATION_MODEL || "openai/gpt-oss-20b:free",
    translation:
      process.env.OPENROUTER_TRANSLATION_MODEL || "openai/gpt-oss-20b:free",
    conversation:
      process.env.OPENROUTER_CONVERSATION_MODEL || "openai/gpt-oss-20b:free",
  },
};

const openAIConfig = {
  apiKey: process.env.OPENAI_API_KEY,
  baseURL: process.env.OPENAI_BASE_URL || "https://api.openai.com/v1",
  model: process.env.OPENAI_MODEL || "gpt-4o-mini",
  maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS) || 4000,
  temperature: parseFloat(process.env.OPENAI_TEMPERATURE) || 0.7,
  models: {
    analysis: process.env.OPENAI_ANALYSIS_MODEL || "gpt-4o-mini",
    matching: process.env.OPENAI_MATCHING_MODEL || "gpt-4o-mini",
    summarization: process.env.OPENAI_SUMMARIZATION_MODEL || "gpt-4o-mini",
    translation: process.env.OPENAI_TRANSLATION_MODEL || "gpt-4o-mini",
    conversation: process.env.OPENAI_CONVERSATION_MODEL || "gpt-4o-mini",
  },
};

module.exports = {
  // Server Configuration
  port: process.env.PORT || 3000,
  nodeEnv: process.env.NODE_ENV || "development",

  // Database Configuration
  mongoURI: process.env.MONGO_URI || "mongodb://localhost:27017/zakmakelaar",

  // JWT Configuration
  jwtSecret: process.env.JWT_SECRET || "fallback-secret-change-in-production",
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || "7d",

  // SendGrid Configuration
  sendgridApiKey: process.env.SENDGRID_API_KEY,
  sendgridFromEmail:
    process.env.SENDGRID_FROM_EMAIL || "<EMAIL>",

  // Twilio Configuration
  twilioAccountSid: process.env.TWILIO_ACCOUNT_SID,
  twilioAuthToken: process.env.TWILIO_AUTH_TOKEN,
  twilioWhatsAppFrom:
    process.env.TWILIO_WHATSAPP_FROM || "whatsapp:+***********",

  // Rate Limiting Configuration
  rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, // 15 minutes
  rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  sessionTokenTTLHours: parseInt(process.env.SESSION_TOKEN_TTL_HOURS) || 168,

  // Scraping Configuration
  scrapingIntervalMinutes: parseInt(process.env.SCRAPING_INTERVAL_MINUTES) || 5,
  scrapingTimeoutMs: parseInt(process.env.SCRAPING_TIMEOUT_MS) || 60000,

  // Scraper Queue Configuration
  scraperQueueLockDurationMs:
    parseInt(process.env.SCRAPER_QUEUE_LOCK_DURATION_MS, 10) ||
    5 * 60 * 1000, // 5 minutes
  scraperQueueMaxStalledCount:
    parseInt(process.env.SCRAPER_QUEUE_MAX_STALLED_COUNT, 10) || 2,
  scraperQueueStalledIntervalMs:
    parseInt(process.env.SCRAPER_QUEUE_STALLED_INTERVAL_MS, 10) || 30000,
  scraperQueueJobTimeoutMs:
    parseInt(process.env.SCRAPER_QUEUE_JOB_TIMEOUT_MS, 10) ||
    15 * 60 * 1000, // 15 minutes

  // CORS Configuration
  corsOrigin: process.env.CORS_ORIGIN
    ? process.env.CORS_ORIGIN.split(",")
    : ["http://localhost:3000"],

  // Redis Configuration
  redisHost: process.env.REDIS_HOST || "localhost",
  redisPort: parseInt(process.env.REDIS_PORT) || 6379,
  redisPassword: process.env.REDIS_PASSWORD || null,

  // Cache Configuration
  cacheDefaultTTL: parseInt(process.env.CACHE_DEFAULT_TTL) || 3600, // 1 hour
  cacheListingsTTL: parseInt(process.env.CACHE_LISTINGS_TTL) || 300, // 5 minutes
  cacheUserTTL: parseInt(process.env.CACHE_USER_TTL) || 1800, // 30 minutes

  // Auto-Application Configuration
  autoApplicationPeriodicProcessing:
    process.env.AUTO_APPLICATION_PERIODIC_PROCESSING !== "false", // Default enabled
  autoApplicationProcessingIntervalMinutes:
    parseInt(process.env.AUTO_APPLICATION_PROCESSING_INTERVAL_MINUTES) || 15, // Reduced from 30 to 15 minutes
  autoApplicationProcessOnStartup:
    process.env.AUTO_APPLICATION_PROCESS_ON_STARTUP !== "false", // Default enabled
  autoApplicationMaxListingsPerRun:
    parseInt(process.env.AUTO_APPLICATION_MAX_LISTINGS_PER_RUN) || 200, // Max listings to process per run
  autoApplicationDaysBack:
    parseInt(process.env.AUTO_APPLICATION_DAYS_BACK) || 7, // How far back to look for listings

  // Scraper Agent Configuration
  scraperAgentAutoStart: process.env.SCRAPER_AGENT_AUTO_START !== "false", // Default enabled

  // AI Configuration
  googleAI: googleAIConfig,
  openRouter: openRouterConfig,
  openAI: openAIConfig,
};
