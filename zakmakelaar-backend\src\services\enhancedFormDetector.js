/**
 * Enhanced Form Detection System
 * 
 * Improved form detection with better pattern matching,
 * confidence scoring, and platform-specific optimizations.
 */

const { loggers } = require('./logger');

class EnhancedFormDetector {
  constructor() {
    this.platformPatterns = {
      'funda.nl': {
        contactButton: [
          '[data-optimizely="contact-email"]',
          '.contact-button',
          'button:contains("Contact")',
          'a:contains("Contact")'
        ],
        formSelectors: [
          '#contact-form',
          '.contact-form',
          'form[action*="contact"]'
        ],
        fieldPatterns: {
          name: ['#firstName', '#lastName', 'input[name*="name"]'],
          email: ['#emailAddress', 'input[type="email"]', 'input[name*="email"]'],
          phone: ['#phoneNumber', 'input[type="tel"]', 'input[name*="phone"]'],
          message: ['#questionInput', 'textarea[name*="message"]', 'textarea[name*="question"]']
        },
        successIndicators: [
          'bedankt voor je bericht',
          'thank you for your message',
          'bericht verzonden',
          'message sent'
        ]
      },
      
      'pararius.nl': {
        contactButton: [
          '.contact-button',
          '.btn-contact',
          'button:contains("Contact")'
        ],
        formSelectors: [
          '#contact_form',
          '.contact-form'
        ],
        fieldPatterns: {
          name: ['#contact_name', 'input[name="name"]'],
          email: ['#contact_email', 'input[name="email"]'],
          phone: ['#contact_phone', 'input[name="phone"]'],
          message: ['#contact_message', 'textarea[name="message"]']
        }
      },
      
      'kamernet.nl': {
        contactButton: [
          '.react-button',
          'button:contains("Reageren")',
          '.response-button'
        ],
        formSelectors: [
          '.response-form',
          'form[class*="response"]'
        ],
        fieldPatterns: {
          name: ['input[name="name"]', '#name'],
          email: ['input[name="email"]', '#email'],
          phone: ['input[name="phone"]', '#phone'],
          message: ['textarea[name="message"]', '#message']
        },
        requiresLogin: true
      }
    };
    
    this.genericPatterns = {
      contactButtons: [
        'button:contains("Contact")',
        'button:contains("Apply")',
        'button:contains("Reageren")',
        'button:contains("Solliciteren")',
        '.contact-button',
        '.apply-button',
        '.btn-contact',
        '.btn-apply',
        'a[href*="contact"]',
        'a[href*="apply"]'
      ],
      
      formSelectors: [
        'form[action*="contact"]',
        'form[action*="apply"]',
        'form[class*="contact"]',
        'form[class*="apply"]',
        '.contact-form',
        '.application-form',
        '.apply-form'
      ],
      
      fieldPatterns: {
        name: [
          'input[name*="name"]',
          'input[placeholder*="name"]',
          'input[placeholder*="naam"]',
          '#name', '#full_name', '#fullname'
        ],
        email: [
          'input[type="email"]',
          'input[name*="email"]',
          'input[placeholder*="email"]',
          '#email'
        ],
        phone: [
          'input[type="tel"]',
          'input[name*="phone"]',
          'input[name*="telephone"]',
          'input[placeholder*="phone"]',
          'input[placeholder*="telefoon"]',
          '#phone', '#telephone'
        ],
        message: [
          'textarea[name*="message"]',
          'textarea[name*="comment"]',
          'textarea[name*="remarks"]',
          'textarea[placeholder*="message"]',
          'textarea[placeholder*="bericht"]',
          '#message', '#comment', '#remarks'
        ]
      }
    };
  }

  /**
   * Detect forms on a page with enhanced accuracy
   */
  async detectForms(page, url) {
    try {
      const platform = this._extractPlatform(url);
      loggers.app.debug(`Detecting forms for platform: ${platform}`);

      const formInfo = {
        platform,
        type: 'unknown',
        confidence: 0,
        forms: [],
        contactButton: null,
        fields: [],
        requiresLogin: false
      };

      // Platform-specific detection
      if (this.platformPatterns[platform]) {
        const platformResult = await this._detectPlatformSpecificForms(page, platform);
        if (platformResult.confidence > 0.7) {
          return { ...formInfo, ...platformResult };
        }
      }

      // Generic detection fallback
      const genericResult = await this._detectGenericForms(page);
      return { ...formInfo, ...genericResult };

    } catch (error) {
      loggers.app.error('Form detection error:', error);
      throw new Error(`Form detection failed: ${error.message}`);
    }
  }

  /**
   * Platform-specific form detection
   */
  async _detectPlatformSpecificForms(page, platform) {
    const patterns = this.platformPatterns[platform];
    const result = {
      type: 'platform_specific',
      confidence: 0,
      forms: [],
      contactButton: null,
      fields: [],
      requiresLogin: patterns.requiresLogin || false
    };

    try {
      // Find contact button
      for (const selector of patterns.contactButton) {
        const button = await page.$(selector);
        if (button) {
          result.contactButton = {
            selector,
            element: button,
            text: await this._getElementText(page, selector)
          };
          result.confidence += 0.3;
          break;
        }
      }

      // Find forms
      for (const selector of patterns.formSelectors) {
        const forms = await page.$$(selector);
        for (const form of forms) {
          const formData = await this._analyzeForm(page, form, patterns.fieldPatterns);
          result.forms.push(formData);
          result.confidence += 0.2;
        }
      }

      // Analyze fields
      result.fields = await this._detectFields(page, patterns.fieldPatterns);
      if (result.fields.length > 0) {
        result.confidence += 0.3;
      }

      // Check for success indicators
      const pageText = await page.evaluate(() => document.body.textContent.toLowerCase());
      const hasSuccessIndicators = patterns.successIndicators?.some(indicator => 
        pageText.includes(indicator.toLowerCase())
      );
      
      if (hasSuccessIndicators) {
        result.confidence += 0.2;
      }

      loggers.app.debug(`Platform-specific detection for ${platform}:`, {
        confidence: result.confidence,
        formsFound: result.forms.length,
        fieldsFound: result.fields.length,
        hasContactButton: !!result.contactButton
      });

      return result;

    } catch (error) {
      loggers.app.warn(`Platform-specific detection failed for ${platform}:`, error.message);
      return result;
    }
  }

  /**
   * Generic form detection
   */
  async _detectGenericForms(page) {
    const result = {
      type: 'generic',
      confidence: 0,
      forms: [],
      contactButton: null,
      fields: []
    };

    try {
      // Find contact buttons
      for (const selector of this.genericPatterns.contactButtons) {
        const button = await page.$(selector);
        if (button) {
          result.contactButton = {
            selector,
            element: button,
            text: await this._getElementText(page, selector)
          };
          result.confidence += 0.2;
          break;
        }
      }

      // Find forms
      for (const selector of this.genericPatterns.formSelectors) {
        const forms = await page.$$(selector);
        for (const form of forms) {
          const formData = await this._analyzeForm(page, form, this.genericPatterns.fieldPatterns);
          result.forms.push(formData);
          result.confidence += 0.1;
        }
      }

      // If no specific forms found, look for any forms
      if (result.forms.length === 0) {
        const allForms = await page.$$('form');
        for (const form of allForms) {
          const formData = await this._analyzeForm(page, form, this.genericPatterns.fieldPatterns);
          if (formData.fieldCount > 2) { // Only consider forms with multiple fields
            result.forms.push(formData);
            result.confidence += 0.05;
          }
        }
      }

      // Analyze fields
      result.fields = await this._detectFields(page, this.genericPatterns.fieldPatterns);
      if (result.fields.length > 0) {
        result.confidence += 0.2;
      }

      loggers.app.debug('Generic form detection:', {
        confidence: result.confidence,
        formsFound: result.forms.length,
        fieldsFound: result.fields.length,
        hasContactButton: !!result.contactButton
      });

      return result;

    } catch (error) {
      loggers.app.warn('Generic form detection failed:', error.message);
      return result;
    }
  }

  /**
   * Analyze a specific form element
   */
  async _analyzeForm(page, formElement, fieldPatterns) {
    try {
      const formData = await page.evaluate((form) => {
        return {
          action: form.action || '',
          method: form.method || 'GET',
          id: form.id || '',
          className: form.className || '',
          fieldCount: form.querySelectorAll('input, textarea, select').length
        };
      }, formElement);

      // Find specific fields within this form
      formData.fields = [];
      for (const [fieldType, selectors] of Object.entries(fieldPatterns)) {
        for (const selector of selectors) {
          const field = await formElement.$(selector);
          if (field) {
            formData.fields.push({
              type: fieldType,
              selector,
              element: field
            });
            break;
          }
        }
      }

      return formData;

    } catch (error) {
      loggers.app.warn('Form analysis error:', error.message);
      return { fieldCount: 0, fields: [] };
    }
  }

  /**
   * Detect fields on the page
   */
  async _detectFields(page, fieldPatterns) {
    const fields = [];

    for (const [fieldType, selectors] of Object.entries(fieldPatterns)) {
      for (const selector of selectors) {
        try {
          const element = await page.$(selector);
          if (element) {
            const fieldInfo = await page.evaluate((el, type) => {
              return {
                type,
                selector: el.tagName.toLowerCase() + (el.id ? '#' + el.id : '') + (el.className ? '.' + el.className.split(' ')[0] : ''),
                tagName: el.tagName.toLowerCase(),
                inputType: el.type || '',
                name: el.name || '',
                id: el.id || '',
                placeholder: el.placeholder || '',
                required: el.required || false,
                visible: el.offsetParent !== null
              };
            }, element, fieldType);

            fields.push(fieldInfo);
            break; // Found field of this type, move to next type
          }
        } catch (error) {
          // Continue to next selector
        }
      }
    }

    return fields;
  }

  /**
   * Get text content of an element
   */
  async _getElementText(page, selector) {
    try {
      return await page.evaluate((sel) => {
        const element = document.querySelector(sel);
        return element ? element.textContent.trim() : '';
      }, selector);
    } catch (error) {
      return '';
    }
  }

  /**
   * Extract platform from URL
   */
  _extractPlatform(url) {
    try {
      const hostname = new URL(url).hostname.toLowerCase();
      
      // Remove www. prefix
      const cleanHostname = hostname.replace(/^www\./, '');
      
      // Check if we have specific patterns for this platform
      if (this.platformPatterns[cleanHostname]) {
        return cleanHostname;
      }
      
      // Check for partial matches
      for (const platform of Object.keys(this.platformPatterns)) {
        if (cleanHostname.includes(platform.split('.')[0])) {
          return platform;
        }
      }
      
      return 'generic';
    } catch (error) {
      return 'generic';
    }
  }

  /**
   * Calculate form complexity score
   */
  calculateComplexity(formInfo) {
    let complexity = 0;
    
    // Base complexity from field count
    const totalFields = formInfo.fields.length;
    if (totalFields <= 3) complexity = 1;
    else if (totalFields <= 6) complexity = 2;
    else complexity = 3;
    
    // Increase complexity for required fields
    const requiredFields = formInfo.fields.filter(f => f.required).length;
    if (requiredFields > totalFields * 0.5) complexity += 1;
    
    // Increase complexity for special field types
    const hasFileUpload = formInfo.fields.some(f => f.inputType === 'file');
    const hasSelect = formInfo.fields.some(f => f.tagName === 'select');
    if (hasFileUpload || hasSelect) complexity += 1;
    
    // Platform-specific adjustments
    if (formInfo.requiresLogin) complexity += 1;
    if (formInfo.platform === 'generic') complexity += 1;
    
    return Math.min(complexity, 5); // Cap at 5
  }
}

module.exports = EnhancedFormDetector;
