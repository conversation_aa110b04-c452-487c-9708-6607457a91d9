#!/usr/bin/env pwsh
# PowerShell script to rebuild and restart Docker containers
# Usage: .\rebuild-docker.ps1

Write-Host "🐳 Zakmakelaar Docker Rebuild Script" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""

# Check if Docker is running
Write-Host "📋 Checking Docker status..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "✅ Docker is installed: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not installed or not running!" -ForegroundColor Red
    Write-Host "Please install Docker Desktop and make sure it's running." -ForegroundColor Red
    exit 1
}

# Check if docker-compose is available
try {
    $composeVersion = docker compose version
    Write-Host "✅ Docker Compose is available: $composeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose is not available!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🛑 Stopping existing containers..." -ForegroundColor Yellow
docker compose down

Write-Host ""
Write-Host "🗑️  Removing old images (optional - press Ctrl+C to skip)..." -ForegroundColor Yellow
Start-Sleep -Seconds 2
docker compose down --rmi local 2>$null

Write-Host ""
Write-Host "🔨 Building Docker images..." -ForegroundColor Yellow
docker compose build --no-cache

if ($LASTEXITCODE -ne 0) {
    Write-Host ""
    Write-Host "❌ Docker build failed!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🚀 Starting containers..." -ForegroundColor Yellow
docker compose up -d

if ($LASTEXITCODE -ne 0) {
    Write-Host ""
    Write-Host "❌ Failed to start containers!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "✅ Docker containers rebuilt and started successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📊 Container Status:" -ForegroundColor Cyan
docker compose ps

Write-Host ""
Write-Host "📝 Useful Commands:" -ForegroundColor Cyan
Write-Host "  View logs:           docker compose logs -f" -ForegroundColor White
Write-Host "  View backend logs:   docker compose logs -f backend" -ForegroundColor White
Write-Host "  Stop containers:     docker compose down" -ForegroundColor White
Write-Host "  Restart containers:  docker compose restart" -ForegroundColor White
Write-Host ""
Write-Host "🌐 Services should be available at:" -ForegroundColor Cyan
Write-Host "  Backend API:  http://localhost:3000" -ForegroundColor White
Write-Host "  MongoDB:      mongodb://localhost:27017" -ForegroundColor White
Write-Host "  Redis:        localhost:6379" -ForegroundColor White
Write-Host ""

