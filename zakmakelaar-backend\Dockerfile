# Use a pre-built image with Node.js and Chrome
FROM ghcr.io/puppeteer/puppeteer:21.5.2

# Skip Chromium download since it's already installed
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable

# Set working directory
WORKDIR /usr/src/app

# Switch to root to install dependencies
USER root

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Make shell scripts executable
RUN chmod +x *.sh 2>/dev/null || true

# Create necessary directories and set permissions
RUN mkdir -p /usr/src/app/logs /usr/src/app/uploads /usr/src/app/screenshots \
    && chown -R pptruser:pptruser /usr/src/app

# Switch back to non-root user
USER pptruser

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "console.log('Health check passed')" || exit 1

# Default command
CMD ["npm", "start"]