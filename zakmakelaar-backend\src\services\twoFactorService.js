const { authenticator } = require("otplib");
const crypto = require("crypto");

authenticator.options = {
  window: 1,
  digits: 6,
};

const BACKUP_CODE_COUNT = 8;
const BACKUP_CODE_BYTES = 5;

const issuer = process.env.TWO_FACTOR_ISSUER || "ZakMakelaar";

const generateSecret = () => authenticator.generateSecret();

const generateOtpAuthUrl = (secret, email) =>
  authenticator.keyuri(email, issuer, secret);

const verifyToken = (secret, token) => {
  if (!secret || !token) return false;
  return authenticator.check(token, secret);
};

const generateBackupCodes = () => {
  const codes = [];
  for (let i = 0; i < BACKUP_CODE_COUNT; i += 1) {
    const raw = crypto.randomBytes(BACKUP_CODE_BYTES).toString("hex");
    codes.push(`${raw.slice(0, 5)}-${raw.slice(5)}`.toUpperCase());
  }
  return codes;
};

const hashBackupCode = (code) =>
  crypto.createHash("sha256").update(code).digest("hex");

module.exports = {
  generateSecret,
  generateOtpAuthUrl,
  verifyToken,
  generateBackupCodes,
  hashBackupCode,
};
