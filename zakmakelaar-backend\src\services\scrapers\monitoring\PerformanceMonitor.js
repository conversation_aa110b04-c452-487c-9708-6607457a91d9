/**
 * Performance monitoring and metrics collection for scrapers
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.alerts = [];
    this.thresholds = {
      maxErrorRate: 0.3, // 30% error rate threshold
      minSuccessRate: 0.7, // 70% success rate threshold
      maxResponseTime: 30000, // 30 second response time threshold
      maxMemoryUsage: 500 * 1024 * 1024, // 500MB memory threshold
    };
  }

  /**
   * Start monitoring a scraping session
   */
  startSession(siteName, sessionId = null) {
    const id = sessionId || `${siteName}_${Date.now()}`;
    const session = {
      id,
      siteName,
      startTime: Date.now(),
      endTime: null,
      status: 'running',
      metrics: {
        pagesScraped: 0,
        listingsFound: 0,
        listingsSaved: 0,
        duplicatesSkipped: 0,
        errors: [],
        responseTimeMs: [],
        memoryUsageMB: [],
        networkRequests: 0,
        bytesTransferred: 0
      }
    };

    this.metrics.set(id, session);
    console.log(`📊 Started monitoring session: ${id}`);
    return id;
  }

  /**
   * Record page scraping metrics
   */
  recordPageMetrics(sessionId, pageMetrics) {
    const session = this.metrics.get(sessionId);
    if (!session) return;

    session.metrics.pagesScraped++;
    session.metrics.listingsFound += pageMetrics.listingsFound || 0;
    session.metrics.listingsSaved += pageMetrics.listingsSaved || 0;
    session.metrics.duplicatesSkipped += pageMetrics.duplicatesSkipped || 0;
    session.metrics.networkRequests += pageMetrics.networkRequests || 0;
    session.metrics.bytesTransferred += pageMetrics.bytesTransferred || 0;

    if (pageMetrics.responseTime) {
      session.metrics.responseTimeMs.push(pageMetrics.responseTime);
    }

    if (pageMetrics.memoryUsage) {
      session.metrics.memoryUsageMB.push(pageMetrics.memoryUsage);
    }
  }

  /**
   * Record error
   */
  recordError(sessionId, error) {
    const session = this.metrics.get(sessionId);
    if (!session) return;

    session.metrics.errors.push({
      timestamp: Date.now(),
      type: error.type || 'UNKNOWN',
      message: error.message,
      retryable: error.retryable || false
    });

    // Check if we should alert
    this.checkErrorThreshold(session);
  }

  /**
   * End monitoring session
   */
  endSession(sessionId, status = 'completed') {
    const session = this.metrics.get(sessionId);
    if (!session) return;

    session.endTime = Date.now();
    session.status = status;
    session.duration = session.endTime - session.startTime;

    // Calculate final metrics
    const finalMetrics = this.calculateSessionMetrics(session);
    session.finalMetrics = finalMetrics;

    console.log(`📊 Session ${sessionId} completed:`, finalMetrics);

    // Check performance thresholds
    this.checkPerformanceThresholds(session);

    return finalMetrics;
  }

  /**
   * Calculate session metrics
   */
  calculateSessionMetrics(session) {
    const { metrics } = session;
    const duration = session.duration || (Date.now() - session.startTime);

    return {
      duration,
      pagesScraped: metrics.pagesScraped,
      listingsFound: metrics.listingsFound,
      listingsSaved: metrics.listingsSaved,
      duplicatesSkipped: metrics.duplicatesSkipped,
      errorCount: metrics.errors.length,
      errorRate: metrics.pagesScraped > 0 ? metrics.errors.length / metrics.pagesScraped : 0,
      successRate: metrics.pagesScraped > 0 ? (metrics.pagesScraped - metrics.errors.length) / metrics.pagesScraped : 0,
      avgResponseTime: this.calculateAverage(metrics.responseTimeMs),
      maxResponseTime: Math.max(...metrics.responseTimeMs, 0),
      avgMemoryUsage: this.calculateAverage(metrics.memoryUsageMB),
      maxMemoryUsage: Math.max(...metrics.memoryUsageMB, 0),
      networkRequests: metrics.networkRequests,
      bytesTransferred: metrics.bytesTransferred,
      listingsPerMinute: duration > 0 ? (metrics.listingsFound / (duration / 60000)) : 0,
      pagesPerMinute: duration > 0 ? (metrics.pagesScraped / (duration / 60000)) : 0
    };
  }

  /**
   * Calculate average of array
   */
  calculateAverage(arr) {
    if (!arr || arr.length === 0) return 0;
    return arr.reduce((sum, val) => sum + val, 0) / arr.length;
  }

  /**
   * Check error rate threshold
   */
  checkErrorThreshold(session) {
    const errorRate = session.metrics.pagesScraped > 0 ? 
      session.metrics.errors.length / session.metrics.pagesScraped : 0;

    if (errorRate > this.thresholds.maxErrorRate) {
      this.createAlert({
        type: 'HIGH_ERROR_RATE',
        sessionId: session.id,
        siteName: session.siteName,
        value: errorRate,
        threshold: this.thresholds.maxErrorRate,
        message: `High error rate detected: ${(errorRate * 100).toFixed(1)}%`
      });
    }
  }

  /**
   * Check performance thresholds
   */
  checkPerformanceThresholds(session) {
    const metrics = session.finalMetrics;

    // Check success rate
    if (metrics.successRate < this.thresholds.minSuccessRate) {
      this.createAlert({
        type: 'LOW_SUCCESS_RATE',
        sessionId: session.id,
        siteName: session.siteName,
        value: metrics.successRate,
        threshold: this.thresholds.minSuccessRate,
        message: `Low success rate: ${(metrics.successRate * 100).toFixed(1)}%`
      });
    }

    // Check response time
    if (metrics.maxResponseTime > this.thresholds.maxResponseTime) {
      this.createAlert({
        type: 'HIGH_RESPONSE_TIME',
        sessionId: session.id,
        siteName: session.siteName,
        value: metrics.maxResponseTime,
        threshold: this.thresholds.maxResponseTime,
        message: `High response time: ${metrics.maxResponseTime}ms`
      });
    }

    // Check memory usage
    if (metrics.maxMemoryUsage > this.thresholds.maxMemoryUsage) {
      this.createAlert({
        type: 'HIGH_MEMORY_USAGE',
        sessionId: session.id,
        siteName: session.siteName,
        value: metrics.maxMemoryUsage,
        threshold: this.thresholds.maxMemoryUsage,
        message: `High memory usage: ${(metrics.maxMemoryUsage / 1024 / 1024).toFixed(1)}MB`
      });
    }
  }

  /**
   * Create performance alert
   */
  createAlert(alert) {
    alert.timestamp = Date.now();
    alert.id = `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.alerts.push(alert);
    console.warn(`🚨 Performance Alert [${alert.type}]: ${alert.message}`);

    // Keep only last 100 alerts
    if (this.alerts.length > 100) {
      this.alerts.splice(0, this.alerts.length - 100);
    }
  }

  /**
   * Get session metrics
   */
  getSessionMetrics(sessionId) {
    const session = this.metrics.get(sessionId);
    if (!session) return null;

    if (session.status === 'running') {
      // Calculate current metrics for running session
      return this.calculateSessionMetrics(session);
    }

    return session.finalMetrics;
  }

  /**
   * Get all sessions for a site
   */
  getSiteMetrics(siteName, limit = 10) {
    const siteSessions = Array.from(this.metrics.values())
      .filter(session => session.siteName === siteName)
      .sort((a, b) => b.startTime - a.startTime)
      .slice(0, limit);

    return siteSessions.map(session => ({
      id: session.id,
      startTime: session.startTime,
      endTime: session.endTime,
      status: session.status,
      metrics: session.finalMetrics || this.calculateSessionMetrics(session)
    }));
  }

  /**
   * Get performance summary across all sites
   */
  getPerformanceSummary(timeframe = 3600000) { // Default: last hour
    const cutoff = Date.now() - timeframe;
    const recentSessions = Array.from(this.metrics.values())
      .filter(session => session.startTime > cutoff);

    const summary = {
      totalSessions: recentSessions.length,
      completedSessions: recentSessions.filter(s => s.status === 'completed').length,
      failedSessions: recentSessions.filter(s => s.status === 'failed').length,
      runningSessions: recentSessions.filter(s => s.status === 'running').length,
      totalListingsFound: 0,
      totalListingsSaved: 0,
      totalErrors: 0,
      avgSuccessRate: 0,
      avgResponseTime: 0,
      sites: {}
    };

    // Calculate aggregated metrics
    let totalSuccessRate = 0;
    let totalResponseTime = 0;
    let sessionsWithMetrics = 0;

    recentSessions.forEach(session => {
      const metrics = session.finalMetrics || this.calculateSessionMetrics(session);
      
      summary.totalListingsFound += metrics.listingsFound;
      summary.totalListingsSaved += metrics.listingsSaved;
      summary.totalErrors += metrics.errorCount;

      if (metrics.successRate !== undefined) {
        totalSuccessRate += metrics.successRate;
        sessionsWithMetrics++;
      }

      if (metrics.avgResponseTime > 0) {
        totalResponseTime += metrics.avgResponseTime;
      }

      // Site-specific metrics
      if (!summary.sites[session.siteName]) {
        summary.sites[session.siteName] = {
          sessions: 0,
          listingsFound: 0,
          listingsSaved: 0,
          errors: 0,
          avgSuccessRate: 0
        };
      }

      const siteMetrics = summary.sites[session.siteName];
      siteMetrics.sessions++;
      siteMetrics.listingsFound += metrics.listingsFound;
      siteMetrics.listingsSaved += metrics.listingsSaved;
      siteMetrics.errors += metrics.errorCount;
    });

    // Calculate averages
    if (sessionsWithMetrics > 0) {
      summary.avgSuccessRate = totalSuccessRate / sessionsWithMetrics;
      summary.avgResponseTime = totalResponseTime / sessionsWithMetrics;
    }

    // Calculate site averages
    Object.keys(summary.sites).forEach(siteName => {
      const siteData = summary.sites[siteName];
      const siteSessions = recentSessions.filter(s => s.siteName === siteName);
      
      if (siteSessions.length > 0) {
        const siteSuccessRates = siteSessions
          .map(s => s.finalMetrics?.successRate || this.calculateSessionMetrics(s).successRate)
          .filter(rate => rate !== undefined);
        
        siteData.avgSuccessRate = siteSuccessRates.length > 0 ? 
          siteSuccessRates.reduce((sum, rate) => sum + rate, 0) / siteSuccessRates.length : 0;
      }
    });

    return summary;
  }

  /**
   * Get recent alerts
   */
  getAlerts(limit = 20) {
    return this.alerts
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }

  /**
   * Clear old sessions and alerts
   */
  cleanup(maxAge = 86400000) { // Default: 24 hours
    const cutoff = Date.now() - maxAge;
    
    // Remove old sessions
    for (const [sessionId, session] of this.metrics.entries()) {
      if (session.startTime < cutoff && session.status !== 'running') {
        this.metrics.delete(sessionId);
      }
    }

    // Remove old alerts
    this.alerts = this.alerts.filter(alert => alert.timestamp > cutoff);

    console.log(`🧹 Cleaned up old monitoring data (older than ${maxAge}ms)`);
  }

  /**
   * Export metrics for external monitoring systems
   */
  exportMetrics(format = 'json') {
    const summary = this.getPerformanceSummary();
    const alerts = this.getAlerts(10);

    const exportData = {
      timestamp: Date.now(),
      summary,
      alerts,
      thresholds: this.thresholds
    };

    if (format === 'json') {
      return JSON.stringify(exportData, null, 2);
    }

    // Add other formats as needed (CSV, Prometheus, etc.)
    return exportData;
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

module.exports = {
  PerformanceMonitor,
  performanceMonitor
};
