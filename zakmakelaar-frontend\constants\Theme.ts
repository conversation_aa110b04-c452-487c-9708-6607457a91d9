/**
 * Modern Theme Configuration for Property Owner Dashboard
 * Professional real estate application theme with balanced color palette
 */

export const Theme = {
  // Primary Colors
  colors: {
    // Primary palette - Professional Blue
    primary: '#2563EB',
    primaryLight: '#3B82F6',
    primaryDark: '#1D4ED8',
    primaryAlpha: 'rgba(37, 99, 235, 0.1)',
    
    // Secondary palette - Success Green
    secondary: '#10B981',
    secondaryLight: '#34D399',
    secondaryDark: '#059669',
    secondaryAlpha: 'rgba(16, 185, 129, 0.1)',
    
    // Accent colors
    accent: '#F59E0B',
    accentLight: '#FBBF24',
    accentDark: '#D97706',
    accentAlpha: 'rgba(245, 158, 11, 0.1)',
    
    // Status colors
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
    
    // Neutral palette - Soft and balanced
    neutral: {
      50: '#F8FAFC',
      100: '#F1F5F9',
      200: '#E2E8F0',
      300: '#CBD5E1',
      400: '#94A3B8',
      500: '#64748B',
      600: '#475569',
      700: '#334155',
      800: '#1E293B',
      900: '#0F172A',
    },
    
    // Surface colors
    background: '#FAFBFC',
    surface: '#FFFFFF',
    surfaceVariant: '#F8FAFC',
    overlay: 'rgba(0, 0, 0, 0.5)',
    
    // Text colors
    textPrimary: '#0F172A',
    textSecondary: '#475569',
    textTertiary: '#64748B',
    textInverse: '#FFFFFF',
    textDisabled: '#94A3B8',
    
    // Border colors
    border: '#E2E8F0',
    borderLight: '#F1F5F9',
    borderFocus: '#3B82F6',
    
    // Shadow colors
    shadow: 'rgba(0, 0, 0, 0.1)',
    shadowDark: 'rgba(0, 0, 0, 0.15)',
  },
  
  // Typography
  typography: {
    // Font families
    fontFamily: {
      regular: 'System',
      medium: 'System',
      semiBold: 'System',
      bold: 'System',
    },
    
    // Font sizes
    fontSize: {
      xs: 12,
      sm: 14,
      base: 16,
      lg: 18,
      xl: 20,
      '2xl': 24,
      '3xl': 30,
      '4xl': 36,
    },
    
    // Font weights
    fontWeight: {
      normal: '400' as const,
      medium: '500' as const,
      semiBold: '600' as const,
      bold: '700' as const,
      extraBold: '800' as const,
    },
    
    // Line heights
    lineHeight: {
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75,
    },
  },
  
  // Spacing scale (based on 4px grid)
  spacing: {
    xs: 4,
    sm: 8,
    md: 12,
    base: 16,
    lg: 20,
    xl: 24,
    '2xl': 32,
    '3xl': 48,
    '4xl': 64,
    '5xl': 80,
  },
  
  // Border radius
  borderRadius: {
    none: 0,
    sm: 4,
    base: 8,
    md: 12,
    lg: 16,
    xl: 20,
    full: 9999,
  },
  
  // Shadows
  shadows: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    base: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.12,
      shadowRadius: 6,
      elevation: 3,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.15,
      shadowRadius: 12,
      elevation: 5,
    },
    xl: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 12 },
      shadowOpacity: 0.18,
      shadowRadius: 16,
      elevation: 8,
    },
  },
  
  // Animation durations
  animation: {
    fast: 150,
    normal: 250,
    slow: 350,
  },
  
  // Breakpoints for responsive design
  breakpoints: {
    sm: 384,
    md: 448,
    lg: 512,
    xl: 640,
  },
};

// Status color mappings for property statuses
export const StatusColors = {
  active: {
    background: Theme.colors.secondaryAlpha,
    text: Theme.colors.secondary,
    border: Theme.colors.secondary,
  },
  rented: {
    background: Theme.colors.primaryAlpha,
    text: Theme.colors.primary,
    border: Theme.colors.primary,
  },
  draft: {
    background: 'rgba(148, 163, 184, 0.1)',
    text: Theme.colors.neutral[500],
    border: Theme.colors.neutral[500],
  },
  inactive: {
    background: 'rgba(239, 68, 68, 0.1)',
    text: Theme.colors.error,
    border: Theme.colors.error,
  },
  maintenance: {
    background: Theme.colors.accentAlpha,
    text: Theme.colors.accent,
    border: Theme.colors.accent,
  },
};

// Component variants
export const ComponentVariants = {
  button: {
    primary: {
      backgroundColor: Theme.colors.primary,
      textColor: Theme.colors.textInverse,
    },
    secondary: {
      backgroundColor: Theme.colors.surface,
      textColor: Theme.colors.textPrimary,
      borderColor: Theme.colors.border,
    },
    success: {
      backgroundColor: Theme.colors.success,
      textColor: Theme.colors.textInverse,
    },
    warning: {
      backgroundColor: Theme.colors.warning,
      textColor: Theme.colors.textInverse,
    },
    ghost: {
      backgroundColor: 'transparent',
      textColor: Theme.colors.textPrimary,
    },
  },
  
  card: {
    elevated: {
      backgroundColor: Theme.colors.surface,
      ...Theme.shadows.md,
    },
    flat: {
      backgroundColor: Theme.colors.surface,
      borderWidth: 1,
      borderColor: Theme.colors.border,
    },
    transparent: {
      backgroundColor: 'transparent',
    },
  },
};

export default Theme;