# Docker Environment Configuration
# Copy this file to .env and update the values as needed

# Server Configuration
PORT=3000
NODE_ENV=production

# Database Configuration (Docker MongoDB)
MONGO_URI=**********************************************************************

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-here-make-it-long-and-random-change-this-in-production
JWT_EXPIRES_IN=7d

# SendGrid Configuration (for email notifications)
SENDGRID_API_KEY=your-sendgrid-api-key-here
SENDGRID_FROM_EMAIL=<EMAIL>

# Twilio Configuration (for WhatsApp notifications)
TWILIO_ACCOUNT_SID=your-twilio-account-sid-here
TWILIO_AUTH_TOKEN=your-twilio-auth-token-here
TWILIO_WHATSAPP_FROM=whatsapp:+***********

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here

# OpenRouter Configuration (Referenced in docker-compose.yml)
OPENROUTER_API_KEY=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
OPENROUTER_DEFAULT_MODEL=openai/gpt-oss-20b:free
OPENROUTER_MAX_TOKENS=4000
OPENROUTER_TEMPERATURE=0.7
OPENROUTER_ANALYSIS_MODEL=openai/gpt-oss-20b:free
OPENROUTER_MATCHING_MODEL=openai/gpt-oss-20b:free
OPENROUTER_SUMMARIZATION_MODEL=openai/gpt-oss-20b:free
OPENROUTER_TRANSLATION_MODEL=openai/gpt-oss-20b:free
OPENROUTER_CONVERSATION_MODEL=openai/gpt-oss-20b:free

# Google AI Configuration (Gemini)
GOOGLE_AI_API_KEY=AIzaSyD8SFflUFFj-SRVDcs0J5hl_prxs9q2GkM
GOOGLE_AI_MODEL=gemini-2.0-flash
GOOGLE_AI_MAX_TOKENS=4000
GOOGLE_AI_TEMPERATURE=0.7

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4o-mini
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.7
OPENAI_ANALYSIS_MODEL=gpt-4o-mini
OPENAI_MATCHING_MODEL=gpt-4o-mini
OPENAI_SUMMARIZATION_MODEL=gpt-4o-mini
OPENAI_TRANSLATION_MODEL=gpt-4o-mini
OPENAI_CONVERSATION_MODEL=gpt-4o-mini

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Scraping Configuration
SCRAPING_INTERVAL_MINUTES=5
SCRAPING_TIMEOUT_MS=60000
# Enable/disable automatic scraper agent startup (true/false)
SCRAPER_AGENT_AUTO_START=true

# CORS Configuration
CORS_ORIGIN=http://localhost:3001,http://localhost:5173,http://localhost:3000

# Redis Configuration (Docker Redis)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# Cache Configuration
CACHE_DEFAULT_TTL=3600
CACHE_LISTINGS_TTL=300
CACHE_USER_TTL=1800

# Encryption Configuration
ENCRYPTION_MASTER_KEY=your-encryption-master-key-change-this-in-production-64-chars-long

# Debug Logging Configuration
# Enable debug logs for auto-application service
AUTO_APPLICATION_DEBUG=true
# Enable console logs for auto-application service
AUTO_APPLICATION_CONSOLE_LOGS=true

# Separate Headless Controls for Docker
# Scraping browser visibility (for web scraping operations)
SCRAPING_HEADLESS=true
SCRAPING_SLOW_MOTION=100
SCRAPING_SHOW_DEVTOOLS=false

# Auto-application browser visibility (for form automation)
AUTO_APPLICATION_HEADLESS=true
AUTO_APPLICATION_SLOW_MOTION=100
AUTO_APPLICATION_SHOW_DEVTOOLS=false

# Chrome/Chromium Configuration for Docker
CHROME_EXECUTABLE_PATH=/usr/bin/google-chrome-stable
PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable

# Demo Configuration Variables
# Enable demo mode for more verbose logging but force headless in Docker
DEMO_MODE=false
DEMO_BROWSER_HEADLESS=true
DEMO_SHOW_BROWSER=false
DEMO_SLOW_MOTION=100
DEMO_SCREENSHOT_ENABLED=true
FUNDA_AUTO_APPLICATION_HEADLESS=true
FUNDA_DEMO_EMAIL=<EMAIL>
FUNDA_DEMO_FIRST_NAME=Wellis
FUNDA_DEMO_LAST_NAME=Hant

# Docker Browser Configuration
# Force headless mode for all browser operations in Docker
BROWSER_HEADLESS=true
BROWSER_NO_SANDBOX=true
BROWSER_DISABLE_SETUID_SANDBOX=true
BROWSER_DISABLE_DEV_SHM_USAGE=true
BROWSER_DISABLE_GPU=true
BROWSER_DISABLE_WEB_SECURITY=true

# Additional Puppeteer arguments for Docker compatibility (updated based on testing)
PUPPETEER_ARGS=--no-sandbox,--disable-setuid-sandbox,--disable-dev-shm-usage,--disable-gpu,--disable-web-security,--disable-features=VizDisplayCompositor,--disable-background-timer-throttling,--disable-backgrounding-occluded-windows,--disable-renderer-backgrounding
