import { Alert } from "react-native";

import {
  AutoApplicationSettings,
} from "@/services/autoApplicationService";
import {
  userProfileService,
  AutoApplicationGuidance,
  AutoApplicationDocumentStatus,
} from "@/services/userProfileService";

type EnsureEligibilityParams = {
  user?: any;
  settings?: AutoApplicationSettings | null;
  navigateToProfile?: () => void;
};

const formatDocumentType = (docType: string) => {
  const mapping: Record<string, string> = {
    income_proof: "Proof of income",
    employment_contract: "Employment contract",
    bank_statement: "Bank statement",
    id_document: "Identification document",
    rental_reference: "Rental reference",
  };

  if (mapping[docType]) {
    return mapping[docType];
  }

  return docType
    .split("_")
    .map((segment) => segment.charAt(0).toUpperCase() + segment.slice(1))
    .join(" ");
};

const buildEligibilityMessage = (issues: string[]) => {
  const uniqueIssues = Array.from(
    new Set(
      issues
        .map((issue) => issue.trim())
        .filter((issue) => issue.length > 0)
    )
  );

  if (uniqueIssues.length === 0) {
    return (
      "Please review your personal information and upload the required documents before enabling autonomous applications."
    );
  }

  const list = uniqueIssues.map((issue) => `• ${issue}`).join("\n");

  return [
    "Before enabling autonomous applications, please complete the following:",
    list,
    "After finishing these steps you can return here to activate autonomous mode.",
  ].join("\n\n");
};

const isQuickReady = (
  user: any,
  settings?: AutoApplicationSettings | null
): boolean => {
  if (!settings) {
    return false;
  }

  return Boolean(
    user?.isAutoApplicationReady ||
      settings.canAutoApply ||
      settings.isProfileComplete ||
      settings.documentsComplete ||
      settings.status?.isActive
  );
};

const collectGuidanceIssues = (
  guidance?: AutoApplicationGuidance,
  fallbackScore?: number
): { issues: string[]; overallScore: number } => {
  const issues: string[] = [];
  let overallScore = fallbackScore ?? 0;

  if (!guidance) {
    if (typeof fallbackScore === "number" && fallbackScore < 80) {
      issues.push(
        `Increase your profile completeness to at least 80% (currently ${Math.round(
          fallbackScore
        )}%).`
      );
    }
    return { issues, overallScore: fallbackScore ?? 0 };
  }

  if (typeof guidance.completeness?.overall === "number") {
    overallScore = guidance.completeness.overall;
    if (guidance.completeness.overall < 80) {
      issues.push(
        `Increase your profile completeness to at least 80% (currently ${Math.round(
          guidance.completeness.overall
        )}%).`
      );
    }
  } else if (typeof fallbackScore === "number" && fallbackScore < 80) {
    issues.push(
      `Increase your profile completeness to at least 80% (currently ${Math.round(
        fallbackScore
      )}%).`
    );
  }

  guidance.nextSteps?.forEach((step) => {
    const summary =
      step.description ||
      (step.fields && step.fields.length
        ? `Provide: ${step.fields.join(", ")}`
        : undefined) ||
      (step.documents && step.documents.length
        ? `Upload: ${step.documents
            .map((doc) => formatDocumentType(doc))
            .join(", ")}`
        : undefined);

    if (step.title) {
      issues.push(summary ? `${step.title}: ${summary}` : `${step.title}.`);
    } else if (summary) {
      issues.push(summary);
    }
  });

  guidance.warnings?.forEach((warning) => {
    if (warning.description) {
      issues.push(warning.description);
    } else if (warning.title) {
      issues.push(warning.title);
    }
  });

  return { issues, overallScore };
};

const collectDocumentIssues = (
  documentStatus?: AutoApplicationDocumentStatus
): { issues: string[]; allRequiredUploaded: boolean } => {
  const issues: string[] = [];

  if (!documentStatus) {
    return { issues, allRequiredUploaded: true };
  }

  const {
    allRequiredUploaded,
    missingDocuments,
    documentCompleteness,
  } = documentStatus;

  if (!allRequiredUploaded) {
    if (missingDocuments?.length) {
      issues.push(
        `Upload the required documents: ${missingDocuments
          .map((doc) => formatDocumentType(doc))
          .join(", ")}.`
      );
    } else {
      issues.push(
        "Upload the required verification documents (proof of income, employment contract, bank statement, and ID)."
      );
    }
  }

  if (typeof documentCompleteness === "number" && documentCompleteness < 100) {
    issues.push(
      `Document completeness is ${Math.round(
        documentCompleteness
      )}%. Upload the remaining documents to reach 100%.`
    );
  }

  return { issues, allRequiredUploaded: Boolean(allRequiredUploaded) };
};

export const ensureAutoApplicationEligibility = async ({
  user,
  settings,
  navigateToProfile,
}: EnsureEligibilityParams): Promise<boolean> => {
  if (settings && isQuickReady(user, settings)) {
    return true;
  }

  const issues: string[] = [];
  let overallScore =
    (user as any)?.autoApplication?.profileCompleteness?.overall ?? 0;

  try {
    let guidance: AutoApplicationGuidance | undefined;
    try {
      const result = await userProfileService.getAutoApplicationGuidance();
      if (result.success && result.data) {
        guidance = result.data;
      }
    } catch (guidanceError) {
      console.warn(
        "Failed to load auto-application guidance:",
        (guidanceError as any)?.message || guidanceError
      );
    }

    const guidanceResult = collectGuidanceIssues(guidance, overallScore);
    overallScore = guidanceResult.overallScore;
    issues.push(...guidanceResult.issues);

    let documentStatus: AutoApplicationDocumentStatus | undefined;
    try {
      const result =
        await userProfileService.getAutoApplicationDocumentStatus();
      if (result.success && result.data) {
        documentStatus = result.data;
      }
    } catch (documentError) {
      console.warn(
        "Failed to load auto-application document status:",
        (documentError as any)?.message || documentError
      );
    }

    const docResult = collectDocumentIssues(documentStatus);
    issues.push(...docResult.issues);

    const readiness = overallScore >= 80 && docResult.allRequiredUploaded;

    if (!readiness) {
      Alert.alert(
        "Complete Your Setup",
        buildEligibilityMessage(issues),
        [
          navigateToProfile
            ? {
                text: "Review Checklist",
                onPress: navigateToProfile,
              }
            : undefined,
          { text: "OK", style: "cancel" },
        ].filter(Boolean) as any,
        { cancelable: true }
      );
      return false;
    }

    return true;
  } catch (error) {
    console.error(
      "Failed to verify auto-application readiness:",
      (error as any)?.message || error
    );
    Alert.alert(
      "Check Failed",
      "We couldn't verify that your profile is ready. Please review your profile and documents, then try again."
    );
    return false;
  }
};
