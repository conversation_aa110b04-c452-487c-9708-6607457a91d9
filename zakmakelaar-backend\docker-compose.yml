services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: zakmakelaar-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: zakmakelaar
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - zakmakelaar-network

  # Redis Cache
  redis:
    image: redis:7.2-alpine
    container_name: zakmakelaar-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - zakmakelaar-network

  # Backend Application
  backend:
    build: .
    container_name: zakmakelaar-backend
    restart: unless-stopped
    ports:
      - "3000:3000"
    env_file:
      - .env.docker
    environment:
      - NODE_ENV=production
      - PORT=3000
      - MONGO_URI=**********************************************************************
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - JWT_SECRET=${JWT_SECRET:-your-super-secure-jwt-secret-key-here-make-it-long-and-random}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-7d}
      - SENDGRID_API_KEY=${SENDGRID_API_KEY}
      - SENDGRID_FROM_EMAIL=${SENDGRID_FROM_EMAIL}
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - TWILIO_WHATSAPP_FROM=${TWILIO_WHATSAPP_FROM}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - OPENROUTER_DEFAULT_MODEL=${OPENROUTER_DEFAULT_MODEL:-deepseek/deepseek-r1-0528:free}
      - OPENROUTER_MAX_TOKENS=${OPENROUTER_MAX_TOKENS:-4000}
      - OPENROUTER_TEMPERATURE=${OPENROUTER_TEMPERATURE:-0.7}
      - OPENROUTER_ANALYSIS_MODEL=${OPENROUTER_ANALYSIS_MODEL:-deepseek/deepseek-r1-0528:free}
      - OPENROUTER_MATCHING_MODEL=${OPENROUTER_MATCHING_MODEL:-deepseek/deepseek-r1-0528:free}
      - OPENROUTER_SUMMARIZATION_MODEL=${OPENROUTER_SUMMARIZATION_MODEL:-deepseek/deepseek-r1-0528:free}
      - OPENROUTER_TRANSLATION_MODEL=${OPENROUTER_TRANSLATION_MODEL:-deepseek/deepseek-r1-0528:free}
      - OPENROUTER_CONVERSATION_MODEL=${OPENROUTER_CONVERSATION_MODEL:-deepseek/deepseek-r1-0528:free}
      - GOOGLE_AI_API_KEY=${GOOGLE_AI_API_KEY}
      - GOOGLE_AI_MODEL=${GOOGLE_AI_MODEL:-gemini-2.0-flash}
      - GOOGLE_AI_MAX_TOKENS=${GOOGLE_AI_MAX_TOKENS:-4000}
      - GOOGLE_AI_TEMPERATURE=${GOOGLE_AI_TEMPERATURE:-0.7}
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-4o-mini}
      - OPENAI_MAX_TOKENS=${OPENAI_MAX_TOKENS:-4000}
      - OPENAI_TEMPERATURE=${OPENAI_TEMPERATURE:-0.7}
      - OPENAI_ANALYSIS_MODEL=${OPENAI_ANALYSIS_MODEL:-gpt-4o-mini}
      - OPENAI_MATCHING_MODEL=${OPENAI_MATCHING_MODEL:-gpt-4o-mini}
      - OPENAI_SUMMARIZATION_MODEL=${OPENAI_SUMMARIZATION_MODEL:-gpt-4o-mini}
      - OPENAI_TRANSLATION_MODEL=${OPENAI_TRANSLATION_MODEL:-gpt-4o-mini}
      - OPENAI_CONVERSATION_MODEL=${OPENAI_CONVERSATION_MODEL:-gpt-4o-mini}
      - ENCRYPTION_MASTER_KEY=${ENCRYPTION_MASTER_KEY}
      - RATE_LIMIT_WINDOW_MS=${RATE_LIMIT_WINDOW_MS:-900000}
      - RATE_LIMIT_MAX_REQUESTS=${RATE_LIMIT_MAX_REQUESTS:-100}
      - SCRAPING_INTERVAL_MINUTES=${SCRAPING_INTERVAL_MINUTES:-5}
      - SCRAPING_TIMEOUT_MS=${SCRAPING_TIMEOUT_MS:-60000}
      - SCRAPER_AGENT_AUTO_START=${SCRAPER_AGENT_AUTO_START:-true}
      - CORS_ORIGIN=${CORS_ORIGIN:-http://localhost:3001}
      - CACHE_DEFAULT_TTL=${CACHE_DEFAULT_TTL:-3600}
      - CACHE_LISTINGS_TTL=${CACHE_LISTINGS_TTL:-300}
      - CACHE_USER_TTL=${CACHE_USER_TTL:-1800}
      - SCRAPING_HEADLESS=${SCRAPING_HEADLESS:-true}
      - SCRAPING_SLOW_MOTION=${SCRAPING_SLOW_MOTION:-0}
      - SCRAPING_SHOW_DEVTOOLS=${SCRAPING_SHOW_DEVTOOLS:-false}
      - AUTO_APPLICATION_HEADLESS=${AUTO_APPLICATION_HEADLESS:-true}
      - AUTO_APPLICATION_SLOW_MOTION=${AUTO_APPLICATION_SLOW_MOTION:-0}
      - AUTO_APPLICATION_SHOW_DEVTOOLS=${AUTO_APPLICATION_SHOW_DEVTOOLS:-false}
      - AUTO_APPLICATION_DEBUG=${AUTO_APPLICATION_DEBUG:-true}
      - AUTO_APPLICATION_CONSOLE_LOGS=${AUTO_APPLICATION_CONSOLE_LOGS:-true}
      - DEMO_MODE=${DEMO_MODE:-true}
      - DEMO_BROWSER_HEADLESS=${DEMO_BROWSER_HEADLESS:-true}
      - DEMO_SHOW_BROWSER=${DEMO_SHOW_BROWSER:-false}
      - DEMO_SLOW_MOTION=${DEMO_SLOW_MOTION:-0}
      - DEMO_SCREENSHOT_ENABLED=${DEMO_SCREENSHOT_ENABLED:-true}
      - FUNDA_AUTO_APPLICATION_HEADLESS=${FUNDA_AUTO_APPLICATION_HEADLESS:-true}
      - FUNDA_DEMO_EMAIL=${FUNDA_DEMO_EMAIL:-<EMAIL>}
      - FUNDA_DEMO_FIRST_NAME=${FUNDA_DEMO_FIRST_NAME:-Wellis}
      - FUNDA_DEMO_LAST_NAME=${FUNDA_DEMO_LAST_NAME:-Hant}
      - CHROME_EXECUTABLE_PATH=/usr/bin/google-chrome-stable
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable
      # Docker Browser Configuration
      - BROWSER_HEADLESS=true
      - BROWSER_NO_SANDBOX=true
      - BROWSER_DISABLE_SETUID_SANDBOX=true
      - BROWSER_DISABLE_DEV_SHM_USAGE=true
      - BROWSER_DISABLE_GPU=true
      - BROWSER_DISABLE_WEB_SECURITY=true
      - PUPPETEER_ARGS=--no-sandbox,--disable-setuid-sandbox,--disable-dev-shm-usage,--disable-gpu,--disable-web-security,--disable-features=VizDisplayCompositor,--disable-background-timer-throttling,--disable-backgrounding-occluded-windows,--disable-renderer-backgrounding
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    networks:
      - zakmakelaar-network
    healthcheck:
      test:
        [
          "CMD",
          "node",
          "-e",
          "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  zakmakelaar-network:
    driver: bridge
