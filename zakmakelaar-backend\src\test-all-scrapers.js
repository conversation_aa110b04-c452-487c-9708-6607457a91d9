const mongoose = require("mongoose");
const { scrapeFunda, scrapePararius, scrapeHuurwoningen } = require("./services/scraper");
const config = require("./config/config");

// Connect to MongoDB
mongoose
  .connect(config.mongoURI, {
    maxPoolSize: 10,
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000,
  })
  .then(() => {
    console.log("✅ MongoDB Connected for testing");
  })
  .catch((err) => {
    console.error("❌ MongoDB connection error:", err);
    process.exit(1);
  });

async function testScraper(scraperName, scraperFunction) {
  console.log(`\n🚀 Testing ${scraperName} scraper...`);
  console.log("=".repeat(50));

  try {
    const startTime = Date.now();
    const listings = await scraperFunction();
    const duration = Date.now() - startTime;

    console.log(`\n📊 ${scraperName} Results:`);
    console.log(`⏱️  Duration: ${duration}ms (${(duration / 1000).toFixed(2)}s)`);
    console.log(`📋 Listings found: ${listings.length}`);

    if (listings.length > 0) {
      console.log(`\n🏠 Sample ${scraperName} listings:`);
      listings.slice(0, 3).forEach((listing, index) => {
        console.log(`\n${index + 1}. ${listing.title}`);
        console.log(`   💰 Price: ${listing.price}`);
        console.log(`   📍 Location: ${listing.location}`);
        console.log(`   🏷️  Type: ${listing.propertyType}`);
        console.log(`   📐 Size: ${listing.size || 'N/A'}`);
        console.log(`   🚪 Rooms: ${listing.rooms || 'N/A'}`);
        console.log(`   📅 Year: ${listing.year || 'N/A'}`);
        console.log(`   🪑 Interior: ${listing.interior || 'N/A'}`);
        console.log(`   🌐 Source: ${listing.source || 'N/A'}`);
        console.log(`   🔗 URL: ${listing.url.substring(0, 80)}...`);
      });

      if (listings.length > 3) {
        console.log(`\n... and ${listings.length - 3} more listings`);
      }
    } else {
      console.log(`❌ No listings found from ${scraperName}`);
    }

    return { name: scraperName, count: listings.length, duration, success: true };
  } catch (error) {
    console.error(`❌ ${scraperName} scraper failed:`, error.message);
    return { name: scraperName, count: 0, duration: 0, success: false, error: error.message };
  }
}

async function testAllScrapers() {
  console.log("🚀 Starting comprehensive scraper test...");
  console.log("Testing all three scrapers: Funda, Pararius, and Huurwoningen");
  console.log("=".repeat(70));

  const results = [];

  // Test each scraper
  results.push(await testScraper("Funda", scrapeFunda));
  results.push(await testScraper("Pararius", scrapePararius));
  results.push(await testScraper("Huurwoningen", scrapeHuurwoningen));

  // Summary
  console.log("\n📊 COMPREHENSIVE TEST SUMMARY");
  console.log("=".repeat(70));

  let totalListings = 0;
  let totalDuration = 0;
  let successfulScrapers = 0;

  results.forEach((result) => {
    const status = result.success ? "✅ SUCCESS" : "❌ FAILED";
    const duration = result.success ? `${(result.duration / 1000).toFixed(2)}s` : "N/A";
    
    console.log(`${result.name.padEnd(15)} | ${status.padEnd(10)} | ${result.count.toString().padEnd(8)} listings | ${duration.padEnd(8)}`);
    
    if (result.success) {
      totalListings += result.count;
      totalDuration += result.duration;
      successfulScrapers++;
    } else {
      console.log(`   Error: ${result.error}`);
    }
  });

  console.log("-".repeat(70));
  console.log(`TOTAL RESULTS:`);
  console.log(`✅ Successful scrapers: ${successfulScrapers}/3`);
  console.log(`📋 Total listings found: ${totalListings}`);
  console.log(`⏱️  Total duration: ${(totalDuration / 1000).toFixed(2)}s`);
  console.log(`📈 Average listings per scraper: ${(totalListings / successfulScrapers).toFixed(1)}`);

  if (successfulScrapers === 3) {
    console.log("\n🎉 All scrapers working successfully!");
  } else if (successfulScrapers > 0) {
    console.log(`\n⚠️  ${3 - successfulScrapers} scraper(s) failed. Check the errors above.`);
  } else {
    console.log("\n❌ All scrapers failed. Check your configuration and network connection.");
  }

  console.log("\n✅ Comprehensive test completed!");
}

// Handle graceful shutdown
process.on("SIGINT", async () => {
  console.log("\n🛑 Test interrupted by user");
  await mongoose.connection.close();
  process.exit(0);
});

process.on("unhandledRejection", async (err) => {
  console.error("❌ Unhandled Promise Rejection:", err);
  await mongoose.connection.close();
  process.exit(1);
});

// Start the comprehensive test
testAllScrapers()
  .then(async () => {
    await mongoose.connection.close();
    console.log("🔌 Database connection closed");
    process.exit(0);
  })
  .catch(async (error) => {
    console.error("❌ Test suite failed:", error);
    await mongoose.connection.close();
    process.exit(1);
  });
