// Manual Fix for Source Distribution Chart
// Run this in browser console (F12) to populate the chart

function fixSourceDistributionChart() {
    console.log('🔧 Fixing Source Distribution chart...');
    
    if (!window.dashboard || !window.dashboard.charts || !window.dashboard.charts.source) {
        console.error('❌ Source Distribution chart not found!');
        return;
    }
    
    const chart = window.dashboard.charts.source;
    
    // Use database counts data (based on your actual data)
    const sourceLabels = ['Funda', 'Pararius', 'Huurwoningen'];
    const sourceData = [176, 0, 0]; // Funda has 176 listings, others have 0
    
    // Update chart data
    chart.data.labels = sourceLabels;
    chart.data.datasets[0].data = sourceData;
    
    // Update colors to match site colors
    chart.data.datasets[0].backgroundColor = [
        'rgba(255, 107, 53, 0.7)',    // Funda orange
        'rgba(0, 102, 204, 0.7)',     // Pararius blue
        'rgba(40, 167, 69, 0.7)'      // Huurwoningen green
    ];
    chart.data.datasets[0].borderColor = [
        'rgba(255, 107, 53, 1)',      // Funda orange
        'rgba(0, 102, 204, 1)',       // Pararius blue
        'rgba(40, 167, 69, 1)'        // Huurwoningen green
    ];
    
    // Force chart update
    chart.update('active');
    
    console.log(`✅ Source Distribution chart updated!`);
    console.log(`   Funda: ${sourceData[0].toLocaleString()} (100%)`);
    console.log(`   Pararius: ${sourceData[1].toLocaleString()} (0%)`);
    console.log(`   Huurwoningen: ${sourceData[2].toLocaleString()} (0%)`);
    console.log(`   Total: ${sourceData.reduce((a, b) => a + b, 0).toLocaleString()}`);
}

// Auto-run the fix
fixSourceDistributionChart();
