import React from "react";
import {
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useTranslation } from "@/hooks/useTranslation";
import LanguageSelector from "@/components/LanguageSelector";
import Animated, { FadeInUp, SlideInRight } from "react-native-reanimated";
import * as Haptics from "expo-haptics";

const THEME = {
  primary: "#4361ee",
  secondary: "#7209b7",
  accent: "#f72585",
  dark: "#0a0a18",
  light: "#ffffff",
  gray: "#6b7280",
  lightGray: "#f3f4f6",
  success: "#10b981",
  warning: "#f59e0b",
  danger: "#ef4444",
};

const Settings: React.FC = () => {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { t } = useTranslation();

  const Header = () => (
    <LinearGradient
      colors={[THEME.primary, THEME.secondary]}
      style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Animated.View
        style={styles.headerContent}
        entering={FadeInUp.duration(600)}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            router.back();
          }}
          activeOpacity={0.8}
        >
          <View style={styles.backButtonInner}>
            <Ionicons name="chevron-back" size={24} color={THEME.primary} />
          </View>
        </TouchableOpacity>

        <View style={styles.headerCenter}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>ZM</Text>
          </View>
          <Text style={styles.headerTitle}>{t("settings.title")}</Text>
        </View>

        <View style={styles.headerSpacer} />
      </Animated.View>
    </LinearGradient>
  );

  const SettingsItem = ({
    icon,
    title,
    onPress,
    showArrow = true,
    iconColor = THEME.primary,
    iconBackground = `${THEME.primary}15`,
  }: {
    icon: string;
    title: string;
    onPress?: () => void;
    showArrow?: boolean;
    iconColor?: string;
    iconBackground?: string;
  }) => (
    <TouchableOpacity
      style={styles.settingsItem}
      onPress={() => {
        if (onPress) {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          onPress();
        }
      }}
      activeOpacity={0.8}
    >
      <View style={styles.settingsItemLeft}>
        <View style={[styles.settingIcon, { backgroundColor: iconBackground }]}>
          <Ionicons name={icon as any} size={20} color={iconColor} />
        </View>
        <Text style={styles.settingsItemText}>{title}</Text>
      </View>
      {showArrow && (
        <Ionicons name="chevron-forward" size={20} color={THEME.gray} />
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Header />
      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Animated.View
          style={styles.section}
          entering={FadeInUp.duration(600).delay(200)}
        >
          <Text style={styles.sectionTitle}>{t("settings.language")}</Text>
          <View style={styles.languageSelectorContainer}>
            <LanguageSelector showLabel={false} />
          </View>
        </Animated.View>

        <Animated.View
          style={styles.section}
          entering={FadeInUp.duration(600).delay(300)}
        >
          <Text style={styles.sectionTitle}>Automation</Text>
          <Animated.View entering={SlideInRight.duration(600).delay(400)}>
            <SettingsItem
              icon="flash-outline"
              title="Smart Automation"
              onPress={() => router.push("/auto-application-settings")}
              iconColor={THEME.success}
              iconBackground={`${THEME.success}15`}
            />
          </Animated.View>
        </Animated.View>

        <Animated.View
          style={styles.section}
          entering={FadeInUp.duration(600).delay(400)}
        >
          <Text style={styles.sectionTitle}>Preferences</Text>
          <Animated.View entering={SlideInRight.duration(600).delay(500)}>
            <SettingsItem
              icon="options-outline"
              title={t("settings.searchPreferences")}
              onPress={() => router.push("/preferences")}
              iconColor={THEME.primary}
              iconBackground={`${THEME.primary}15`}
            />
          </Animated.View>
          <Animated.View entering={SlideInRight.duration(600).delay(600)}>
            <SettingsItem
              icon="notifications-outline"
              title={t("settings.notifications")}
              onPress={() => router.push("/notification-settings")}
              iconColor={THEME.warning}
              iconBackground={`${THEME.warning}15`}
            />
          </Animated.View>
        </Animated.View>

        <Animated.View
          style={styles.section}
          entering={FadeInUp.duration(600).delay(500)}
        >
          <Text style={styles.sectionTitle}>{t("settings.account")}</Text>
          <Animated.View entering={SlideInRight.duration(600).delay(700)}>
            <SettingsItem
              icon="person-outline"
              title={t("settings.editProfile")}
              onPress={() => router.push("/edit-profile")}
              iconColor={THEME.primary}
              iconBackground={`${THEME.primary}15`}
            />
          </Animated.View>
          <Animated.View entering={SlideInRight.duration(600).delay(800)}>
            <SettingsItem
              icon="key-outline"
              title={t("settings.changePassword")}
              onPress={() => router.push("/change-password")}
              iconColor={THEME.accent}
              iconBackground={`${THEME.accent}15`}
            />
          </Animated.View>
          <Animated.View entering={SlideInRight.duration(600).delay(900)}>
            <SettingsItem
              icon="shield-outline"
              title={t("settings.privacy")}
              onPress={() => {}}
              iconColor={THEME.secondary}
              iconBackground={`${THEME.secondary}15`}
            />
          </Animated.View>
        </Animated.View>

        <Animated.View
          style={styles.section}
          entering={FadeInUp.duration(600).delay(600)}
        >
          <Text style={styles.sectionTitle}>{t("settings.legal")}</Text>
          <Animated.View entering={SlideInRight.duration(600).delay(1000)}>
            <SettingsItem
              icon="document-text-outline"
              title={t("settings.terms")}
              onPress={() => {}}
              iconColor={THEME.gray}
              iconBackground={`${THEME.gray}15`}
            />
          </Animated.View>
          <Animated.View entering={SlideInRight.duration(600).delay(1100)}>
            <SettingsItem
              icon="help-circle-outline"
              title={t("settings.helpSupport")}
              onPress={() => {}}
              iconColor={THEME.warning}
              iconBackground={`${THEME.warning}15`}
            />
          </Animated.View>
        </Animated.View>

        <Animated.View
          style={styles.section}
          entering={FadeInUp.duration(600).delay(700)}
        >
          <Text style={styles.sectionTitle}>App</Text>
          <Animated.View entering={SlideInRight.duration(600).delay(1200)}>
            <SettingsItem
              icon="information-circle-outline"
              title={t("settings.about")}
              onPress={() => {}}
              iconColor={THEME.gray}
              iconBackground={`${THEME.gray}15`}
            />
          </Animated.View>
          <Animated.View entering={SlideInRight.duration(600).delay(1300)}>
            <View style={styles.settingsItem}>
              <View style={styles.settingsItemLeft}>
                <View
                  style={[
                    styles.settingIcon,
                    { backgroundColor: `${THEME.primary}15` },
                  ]}
                >
                  <Ionicons
                    name="code-outline"
                    size={20}
                    color={THEME.primary}
                  />
                </View>
                <Text style={styles.settingsItemText}>
                  {t("settings.version")}
                </Text>
              </View>
              <View style={styles.versionBadge}>
                <Text style={styles.versionText}>1.0.0</Text>
              </View>
            </View>
          </Animated.View>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  header: {
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  backButton: {
    marginRight: 16,
  },
  backButtonInner: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerCenter: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  logoContainer: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoText: {
    fontSize: 18,
    fontWeight: "bold",
    color: THEME.primary,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: THEME.light,
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  section: {
    marginTop: 20,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: THEME.dark,
    marginBottom: 15,
  },
  languageSelectorContainer: {
    backgroundColor: THEME.light,
    borderRadius: 16,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  settingsItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 20,
    backgroundColor: THEME.light,
    borderRadius: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  settingsItemLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  settingsItemText: {
    fontSize: 16,
    fontWeight: "500",
    color: THEME.dark,
  },
  versionBadge: {
    backgroundColor: THEME.lightGray,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  versionText: {
    fontSize: 14,
    fontWeight: "600",
    color: THEME.gray,
  },
});

export default Settings;
