import AsyncStorage from "@react-native-async-storage/async-storage";
import type { Mutation } from "@tanstack/react-query";

import {
  OfflineMutationRecord,
  useConnectivityStore,
} from "@/store/connectivityStore";

const STORAGE_KEY = "offline_mutation_queue_v1";

let hydrated = false;
let queue: OfflineMutationRecord[] = [];

const serializeMutationKey = (key: Mutation["options"]["mutationKey"]) => {
  if (!key) {
    return "[anonymous]";
  }

  try {
    return Array.isArray(key) ? JSON.stringify(key) : String(key);
  } catch {
    return "[unserializable-key]";
  }
};

const serializeVariables = (variables: unknown) => {
  if (variables === undefined) {
    return undefined;
  }

  try {
    return JSON.parse(JSON.stringify(variables));
  } catch {
    return "[unserializable-variables]";
  }
};

const persistQueue = async () => {
  try {
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(queue));
  } catch (error) {
    console.warn("offlineQueueService: failed to persist queue", error);
  }
};

const broadcast = () => {
  useConnectivityStore.getState().setQueuedMutations([...queue]);
};

const ensureHydrated = async () => {
  if (hydrated) {
    return;
  }

  try {
    const raw = await AsyncStorage.getItem(STORAGE_KEY);
    if (raw) {
      const parsed = JSON.parse(raw);
      if (Array.isArray(parsed)) {
        queue = parsed;
      }
    }
  } catch (error) {
    console.warn("offlineQueueService: failed to hydrate queue", error);
  } finally {
    hydrated = true;
    broadcast();
  }
};

const upsertRecord = async (
  mutation: Mutation<any, any, any, any>,
  status: OfflineMutationRecord["status"] = "queued"
) => {
  await ensureHydrated();

  const record: OfflineMutationRecord = {
    id: mutation.mutationId,
    mutationKey: serializeMutationKey(mutation.options.mutationKey),
    variablesSummary: serializeVariables(mutation.state.variables),
    timestamp: Date.now(),
    status,
  };

  const existingIndex = queue.findIndex((item) => item.id === record.id);

  if (existingIndex >= 0) {
    queue[existingIndex] = { ...queue[existingIndex], ...record };
  } else {
    queue.push(record);
  }

  broadcast();
  await persistQueue();
};

const removeRecord = async (mutationId: number) => {
  await ensureHydrated();

  const nextQueue = queue.filter((item) => item.id !== mutationId);
  if (nextQueue.length === queue.length) {
    return;
  }

  queue = nextQueue;
  broadcast();
  await persistQueue();
};

const markAll = async (status: OfflineMutationRecord["status"]) => {
  await ensureHydrated();
  if (!queue.length) {
    return;
  }

  queue = queue.map((record) => ({ ...record, status }));
  broadcast();
  await persistQueue();
};

export const offlineQueueService = {
  hydrate: ensureHydrated,
  addOrUpdateFromMutation: upsertRecord,
  removeByMutationId: removeRecord,
  markAll,
  clear: async () => {
    queue = [];
    try {
      await AsyncStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.warn("offlineQueueService: failed to clear queue", error);
    }
    broadcast();
  },
  getQueue: () => queue,
};

export default offlineQueueService;
