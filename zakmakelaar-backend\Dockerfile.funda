# Node base image with all deps needed by Puppeteer
FROM node:20-bullseye

# Install Chromium and fonts for Dutch sites
RUN apt-get update && apt-get install -y \
    chromium \
    chromium-common \
    chromium-driver \
    fonts-liberation \
    fonts-noto \
    fonts-noto-cjk \
    fonts-noto-color-emoji \
    libnss3 \
    libxss1 \
    libasound2 \
    libatk-bridge2.0-0 \
    libgtk-3-0 \
    libgbm1 \
    libxdamage1 \
    libxcomposite1 \
    libxrandr2 \
    libpango-1.0-0 \
    libcairo2 \
    libatspi2.0-0 \
    xdg-utils \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app/zakmakelaar-backend
COPY zakmakelaar-backend/package*.json ./
RUN npm ci --legacy-peer-deps || npm install --legacy-peer-deps

# Tell puppeteer where chromium is (used by scraperUtils.js)
ENV CHROME_EXECUTABLE_PATH=/usr/bin/chromium

COPY zakmakelaar-backend/ ./

# Create artifacts directory for screenshots
RUN mkdir -p artifacts

# Default command runs the docker runner
CMD ["node", "run-funda-apply-docker.js"]

