<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Properties - ZakMakelaar Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.4.3/echarts.min.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Inter', sans-serif;
        }
        .hero-title {
            font-family: 'Playfair Display', serif;
        }
        .property-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .property-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.15);
        }
        .filter-panel {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
        }
        .map-container {
            height: 400px;
            border-radius: 12px;
            overflow: hidden;
        }
        .comparison-table {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }
        .favorite-btn {
            transition: all 0.2s ease;
        }
        .favorite-btn.active {
            color: #ef4444;
            transform: scale(1.1);
        }
        .sort-dropdown {
            transform: translateY(-10px);
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
        }
        .sort-dropdown.show {
            transform: translateY(0);
            opacity: 1;
            visibility: visible;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-gray-900 hero-title">ZakMakelaar</h1>
                    </div>
                </div>
                <div class="flex items-center space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-blue-600 transition-colors">Dashboard</a>
                    <a href="properties.html" class="text-blue-600 font-medium border-b-2 border-blue-600 pb-1">Properties</a>
                    <a href="analytics.html" class="text-gray-700 hover:text-blue-600 transition-colors">Analytics</a>
                    <div class="flex items-center space-x-4">
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-medium">JD</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <section class="bg-white py-8 border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 hero-title">Property Management</h1>
                    <p class="text-gray-600 mt-2">Advanced search and management interface</p>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        Add Property
                    </button>
                    <button class="border border-gray-300 hover:bg-gray-50 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors">
                        Export Data
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Filter Panel -->
            <div class="lg:col-span-1">
                <div class="filter-panel rounded-xl p-6 sticky top-24">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Filters</h2>
                    
                    <!-- Search -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                        <input type="text" id="search-input" placeholder="Search properties..." 
                               class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>

                    <!-- Property Type -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">Property Type</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="property-type-filter rounded text-blue-600" value="all" checked>
                                <span class="ml-2 text-gray-700">All Types</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="property-type-filter rounded text-blue-600" value="Apartment">
                                <span class="ml-2 text-gray-700">Apartments</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="property-type-filter rounded text-blue-600" value="House">
                                <span class="ml-2 text-gray-700">Houses</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="property-type-filter rounded text-blue-600" value="Commercial">
                                <span class="ml-2 text-gray-700">Commercial</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="property-type-filter rounded text-blue-600" value="Studio">
                                <span class="ml-2 text-gray-700">Studios</span>
                            </label>
                        </div>
                    </div>

                    <!-- Price Range -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">Price Range</label>
                        <div class="space-y-3">
                            <input type="range" id="price-min" min="0" max="2000000" value="0" 
                                   class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                            <input type="range" id="price-max" min="0" max="2000000" value="2000000" 
                                   class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                            <div class="flex justify-between text-sm text-gray-600">
                                <span id="price-min-value">€0</span>
                                <span id="price-max-value">€2M</span>
                            </div>
                        </div>
                    </div>

                    <!-- Bedrooms -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">Bedrooms</label>
                        <div class="flex space-x-2">
                            <button class="bedroom-filter px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors" data-value="any">Any</button>
                            <button class="bedroom-filter px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors" data-value="1">1</button>
                            <button class="bedroom-filter px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors" data-value="2">2</button>
                            <button class="bedroom-filter px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors" data-value="3">3+</button>
                        </div>
                    </div>

                    <!-- Location -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">City</label>
                        <select id="city-filter" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="all">All Cities</option>
                            <option value="Amsterdam">Amsterdam</option>
                            <option value="Rotterdam">Rotterdam</option>
                            <option value="The Hague">The Hague</option>
                            <option value="Utrecht">Utrecht</option>
                            <option value="Eindhoven">Eindhoven</option>
                        </select>
                    </div>

                    <!-- Features -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">Features</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="feature-filter rounded text-blue-600" value="Furnished">
                                <span class="ml-2 text-gray-700">Furnished</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="feature-filter rounded text-blue-600" value="Parking">
                                <span class="ml-2 text-gray-700">Parking</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="feature-filter rounded text-blue-600" value="Balcony">
                                <span class="ml-2 text-gray-700">Balcony</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="feature-filter rounded text-blue-600" value="Garden">
                                <span class="ml-2 text-gray-700">Garden</span>
                            </label>
                        </div>
                    </div>

                    <button id="clear-filters" class="w-full bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-lg font-medium transition-colors">
                        Clear All Filters
                    </button>
                </div>
            </div>

            <!-- Properties Grid -->
            <div class="lg:col-span-3">
                <!-- Toolbar -->
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-600" id="results-count">Showing 6 properties</span>
                        <div class="relative">
                            <button id="sort-button" class="flex items-center space-x-2 bg-white border border-gray-300 rounded-lg px-4 py-2 hover:bg-gray-50 transition-colors">
                                <span>Sort by</span>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div id="sort-dropdown" class="sort-dropdown absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                                <div class="py-2">
                                    <button class="sort-option w-full text-left px-4 py-2 hover:bg-gray-50 transition-colors" data-sort="price-low">Price: Low to High</button>
                                    <button class="sort-option w-full text-left px-4 py-2 hover:bg-gray-50 transition-colors" data-sort="price-high">Price: High to Low</button>
                                    <button class="sort-option w-full text-left px-4 py-2 hover:bg-gray-50 transition-colors" data-sort="newest">Newest First</button>
                                    <button class="sort-option w-full text-left px-4 py-2 hover:bg-gray-50 transition-colors" data-sort="area-largest">Largest Area</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button id="grid-view" class="p-2 bg-blue-600 text-white rounded-lg">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                            </svg>
                        </button>
                        <button id="list-view" class="p-2 bg-gray-200 text-gray-600 rounded-lg hover:bg-gray-300 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Properties Grid -->
                <div id="properties-grid" class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
                    <!-- Properties will be dynamically generated -->
                </div>

                <!-- Map View -->
                <div id="map-view" class="hidden mb-8">
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Property Locations</h3>
                        <div id="property-map" class="map-container"></div>
                    </div>
                </div>

                <!-- Comparison Table -->
                <div id="comparison-section" class="hidden">
                    <div class="comparison-table rounded-xl shadow-lg p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-xl font-semibold text-gray-900">Property Comparison</h3>
                            <button id="clear-comparison" class="text-red-600 hover:text-red-700 font-medium">Clear All</button>
                        </div>
                        <div id="comparison-table" class="overflow-x-auto">
                            <!-- Comparison table will be dynamically generated -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Property Detail Modal -->
    <div id="property-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
        <div class="bg-white rounded-xl max-w-4xl w-full max-h-screen overflow-y-auto">
            <div id="modal-content">
                <!-- Modal content will be dynamically generated -->
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h3 class="text-2xl font-bold hero-title mb-4">ZakMakelaar</h3>
                <p class="text-gray-400 mb-6">Advanced real estate analytics and property management</p>
                <p class="text-gray-500 text-sm">© 2024 ZakMakelaar. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="main.js"></script>
    <script>
        // Properties page specific functionality
        let currentProperties = [];
        let filteredProperties = [];
        let favorites = new Set();
        let comparisonList = new Set();
        let currentSort = 'newest';
        let isGridView = true;

        document.addEventListener('DOMContentLoaded', function() {
            initializePropertiesPage();
        });

        function initializePropertiesPage() {
            currentProperties = window.ZakMakelaarDashboard.mockProperties;
            filteredProperties = [...currentProperties];
            
            initializeFilters();
            initializeSort();
            initializeViewToggle();
            initializeMap();
            renderProperties();
            updateResultsCount();
        }

        function initializeFilters() {
            // Search input
            document.getElementById('search-input').addEventListener('input', debounce(applyFilters, 300));

            // Property type filters
            document.querySelectorAll('.property-type-filter').forEach(checkbox => {
                checkbox.addEventListener('change', applyFilters);
            });

            // Price range sliders
            const priceMin = document.getElementById('price-min');
            const priceMax = document.getElementById('price-max');
            const priceMinValue = document.getElementById('price-min-value');
            const priceMaxValue = document.getElementById('price-max-value');

            priceMin.addEventListener('input', function() {
                priceMinValue.textContent = '€' + parseInt(this.value).toLocaleString();
                applyFilters();
            });

            priceMax.addEventListener('input', function() {
                priceMaxValue.textContent = '€' + parseInt(this.value).toLocaleString();
                applyFilters();
            });

            // Bedroom filters
            document.querySelectorAll('.bedroom-filter').forEach(button => {
                button.addEventListener('click', function() {
                    document.querySelectorAll('.bedroom-filter').forEach(b => b.classList.remove('bg-blue-600', 'text-white'));
                    this.classList.add('bg-blue-600', 'text-white');
                    applyFilters();
                });
            });

            // City filter
            document.getElementById('city-filter').addEventListener('change', applyFilters);

            // Feature filters
            document.querySelectorAll('.feature-filter').forEach(checkbox => {
                checkbox.addEventListener('change', applyFilters);
            });

            // Clear filters
            document.getElementById('clear-filters').addEventListener('click', clearAllFilters);
        }

        function initializeSort() {
            const sortButton = document.getElementById('sort-button');
            const sortDropdown = document.getElementById('sort-dropdown');

            sortButton.addEventListener('click', function() {
                sortDropdown.classList.toggle('show');
            });

            document.querySelectorAll('.sort-option').forEach(option => {
                option.addEventListener('click', function() {
                    currentSort = this.dataset.sort;
                    sortProperties();
                    renderProperties();
                    sortDropdown.classList.remove('show');
                });
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!sortButton.contains(e.target) && !sortDropdown.contains(e.target)) {
                    sortDropdown.classList.remove('show');
                }
            });
        }

        function initializeViewToggle() {
            document.getElementById('grid-view').addEventListener('click', function() {
                isGridView = true;
                this.classList.add('bg-blue-600', 'text-white');
                this.classList.remove('bg-gray-200', 'text-gray-600');
                document.getElementById('list-view').classList.add('bg-gray-200', 'text-gray-600');
                document.getElementById('list-view').classList.remove('bg-blue-600', 'text-white');
                renderProperties();
            });

            document.getElementById('list-view').addEventListener('click', function() {
                isGridView = false;
                this.classList.add('bg-blue-600', 'text-white');
                this.classList.remove('bg-gray-200', 'text-gray-600');
                document.getElementById('grid-view').classList.add('bg-gray-200', 'text-gray-600');
                document.getElementById('grid-view').classList.remove('bg-blue-600', 'text-white');
                renderProperties();
            });
        }

        function initializeMap() {
            const map = L.map('property-map').setView([52.3676, 4.9041], 7);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            // Add property markers
            const cities = {
                'Amsterdam': [52.3676, 4.9041],
                'Rotterdam': [51.9244, 4.4777],
                'The Hague': [52.0705, 4.3007],
                'Utrecht': [52.0907, 5.1214],
                'Eindhoven': [51.4416, 5.4697]
            };

            currentProperties.forEach(property => {
                if (cities[property.city]) {
                    const marker = L.marker(cities[property.city]).addTo(map);
                    marker.bindPopup(`
                        <div class="p-2">
                            <h3 class="font-semibold">${property.title}</h3>
                            <p class="text-blue-600 font-bold">€${property.price.toLocaleString()}</p>
                            <p class="text-sm text-gray-600">${property.type} in ${property.city}</p>
                        </div>
                    `);
                }
            });
        }

        function applyFilters() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            const selectedTypes = Array.from(document.querySelectorAll('.property-type-filter:checked')).map(cb => cb.value);
            const priceMin = parseInt(document.getElementById('price-min').value);
            const priceMax = parseInt(document.getElementById('price-max').value);
            const selectedCity = document.getElementById('city-filter').value;
            const selectedFeatures = Array.from(document.querySelectorAll('.feature-filter:checked')).map(cb => cb.value);
            const selectedBedrooms = document.querySelector('.bedroom-filter.bg-blue-600')?.dataset.value || 'any';

            filteredProperties = currentProperties.filter(property => {
                // Search filter
                if (searchTerm && !property.title.toLowerCase().includes(searchTerm)) {
                    return false;
                }

                // Type filter
                if (!selectedTypes.includes('all') && selectedTypes.length > 0 && !selectedTypes.includes(property.type)) {
                    return false;
                }

                // Price filter
                if (property.price < priceMin || property.price > priceMax) {
                    return false;
                }

                // City filter
                if (selectedCity !== 'all' && property.city !== selectedCity) {
                    return false;
                }

                // Bedroom filter
                if (selectedBedrooms !== 'any') {
                    if (selectedBedrooms === '3+' && property.bedrooms < 3) {
                        return false;
                    } else if (selectedBedrooms !== '3+' && property.bedrooms !== parseInt(selectedBedrooms)) {
                        return false;
                    }
                }

                // Feature filter
                if (selectedFeatures.length > 0 && !selectedFeatures.every(feature => property.features.includes(feature))) {
                    return false;
                }

                return true;
            });

            sortProperties();
            renderProperties();
            updateResultsCount();
        }

        function sortProperties() {
            switch (currentSort) {
                case 'price-low':
                    filteredProperties.sort((a, b) => a.price - b.price);
                    break;
                case 'price-high':
                    filteredProperties.sort((a, b) => b.price - a.price);
                    break;
                case 'newest':
                    filteredProperties.sort((a, b) => b.id - a.id);
                    break;
                case 'area-largest':
                    filteredProperties.sort((a, b) => b.area - a.area);
                    break;
            }
        }

        function renderProperties() {
            const grid = document.getElementById('properties-grid');
            grid.innerHTML = '';

            if (isGridView) {
                grid.className = 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8';
                filteredProperties.forEach((property, index) => {
                    const card = createPropertyCard(property, index);
                    grid.appendChild(card);
                });
            } else {
                grid.className = 'space-y-4 mb-8';
                filteredProperties.forEach((property, index) => {
                    const card = createPropertyListItem(property, index);
                    grid.appendChild(card);
                });
            }

            // Animate cards
            anime({
                targets: '.property-card',
                opacity: [0, 1],
                translateY: [20, 0],
                delay: anime.stagger(100),
                duration: 600,
                easing: 'easeOutQuart'
            });
        }

        function createPropertyCard(property, index) {
            const card = document.createElement('div');
            card.className = 'property-card bg-white rounded-xl shadow-lg overflow-hidden';
            
            const isFavorite = favorites.has(property.id);
            const isInComparison = comparisonList.has(property.id);
            
            card.innerHTML = `
                <div class="relative">
                    <img src="${property.image}" alt="${property.title}" class="w-full h-48 object-cover">
                    <div class="absolute top-4 right-4 flex space-x-2">
                        <button class="favorite-btn w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg ${isFavorite ? 'active' : ''}" 
                                data-property-id="${property.id}">
                            ♥
                        </button>
                        <button class="comparison-btn w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg ${isInComparison ? 'bg-blue-600 text-white' : ''}" 
                                data-property-id="${property.id}">
                            +
                        </button>
                    </div>
                    <div class="absolute bottom-4 left-4">
                        <span class="bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
                            ${property.area} m²
                        </span>
                    </div>
                </div>
                <div class="p-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">${property.title}</h3>
                    <p class="text-2xl font-bold text-blue-600 mb-4">€${property.price.toLocaleString()}</p>
                    <div class="flex items-center text-gray-600 mb-4">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        ${property.city}
                    </div>
                    <div class="flex items-center justify-between text-gray-600 mb-4">
                        <span>${property.bedrooms} beds</span>
                        <span>${property.bathrooms} baths</span>
                        <span>${property.type}</span>
                    </div>
                    <div class="flex flex-wrap gap-2 mb-4">
                        ${property.features.map(feature => 
                            `<span class="bg-gray-100 text-gray-700 px-2 py-1 rounded text-sm">${feature}</span>`
                        ).join('')}
                    </div>
                    <div class="flex space-x-3">
                        <button class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors view-details-btn" 
                                data-property-id="${property.id}">
                            View Details
                        </button>
                        <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-lg font-medium transition-colors">
                            Contact
                        </button>
                    </div>
                </div>
            `;

            // Add event listeners
            card.querySelector('.favorite-btn').addEventListener('click', toggleFavorite);
            card.querySelector('.comparison-btn').addEventListener('click', toggleComparison);
            card.querySelector('.view-details-btn').addEventListener('click', showPropertyModal);

            return card;
        }

        function createPropertyListItem(property, index) {
            const item = document.createElement('div');
            item.className = 'property-card bg-white rounded-xl shadow-lg p-6 flex items-center space-x-6';
            
            const isFavorite = favorites.has(property.id);
            const isInComparison = comparisonList.has(property.id);
            
            item.innerHTML = `
                <img src="${property.image}" alt="${property.title}" class="w-32 h-24 object-cover rounded-lg">
                <div class="flex-1">
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">${property.title}</h3>
                    <p class="text-2xl font-bold text-blue-600 mb-2">€${property.price.toLocaleString()}</p>
                    <div class="flex items-center space-x-4 text-gray-600 mb-2">
                        <span>${property.city}</span>
                        <span>•</span>
                        <span>${property.bedrooms} beds</span>
                        <span>•</span>
                        <span>${property.bathrooms} baths</span>
                        <span>•</span>
                        <span>${property.area} m²</span>
                    </div>
                    <div class="flex flex-wrap gap-2">
                        ${property.features.map(feature => 
                            `<span class="bg-gray-100 text-gray-700 px-2 py-1 rounded text-sm">${feature}</span>`
                        ).join('')}
                    </div>
                </div>
                <div class="flex flex-col space-y-2">
                    <button class="favorite-btn w-8 h-8 border border-gray-300 rounded-full flex items-center justify-center ${isFavorite ? 'text-red-500' : 'text-gray-400'}" 
                            data-property-id="${property.id}">
                        ♥
                    </button>
                    <button class="comparison-btn w-8 h-8 border border-gray-300 rounded-full flex items-center justify-center ${isInComparison ? 'bg-blue-600 text-white' : 'text-gray-400'}" 
                            data-property-id="${property.id}">
                        +
                    </button>
                    <button class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center view-details-btn" 
                            data-property-id="${property.id}">
                        →
                    </button>
                </div>
            `;

            // Add event listeners
            item.querySelector('.favorite-btn').addEventListener('click', toggleFavorite);
            item.querySelector('.comparison-btn').addEventListener('click', toggleComparison);
            item.querySelector('.view-details-btn').addEventListener('click', showPropertyModal);

            return item;
        }

        function toggleFavorite(e) {
            e.preventDefault();
            const propertyId = parseInt(e.target.dataset.propertyId);
            
            if (favorites.has(propertyId)) {
                favorites.delete(propertyId);
                e.target.classList.remove('active');
            } else {
                favorites.add(propertyId);
                e.target.classList.add('active');
            }
            
            // Animate
            anime({
                targets: e.target,
                scale: [1, 1.2, 1],
                duration: 300,
                easing: 'easeOutQuart'
            });
        }

        function toggleComparison(e) {
            e.preventDefault();
            const propertyId = parseInt(e.target.dataset.propertyId);
            
            if (comparisonList.has(propertyId)) {
                comparisonList.delete(propertyId);
                e.target.classList.remove('bg-blue-600', 'text-white');
            } else {
                if (comparisonList.size < 3) {
                    comparisonList.add(propertyId);
                    e.target.classList.add('bg-blue-600', 'text-white');
                } else {
                    alert('You can compare up to 3 properties at once.');
                    return;
                }
            }
            
            updateComparisonTable();
            
            // Animate
            anime({
                targets: e.target,
                scale: [1, 1.2, 1],
                duration: 300,
                easing: 'easeOutQuart'
            });
        }

        function updateComparisonTable() {
            const section = document.getElementById('comparison-section');
            const table = document.getElementById('comparison-table');
            
            if (comparisonList.size === 0) {
                section.classList.add('hidden');
                return;
            }
            
            section.classList.remove('hidden');
            
            const properties = Array.from(comparisonList).map(id => 
                currentProperties.find(p => p.id === id)
            );
            
            table.innerHTML = `
                <table class="w-full">
                    <thead>
                        <tr class="border-b">
                            <th class="text-left py-3 px-4 font-medium text-gray-900">Property</th>
                            ${properties.map(p => `<th class="text-left py-3 px-4 font-medium text-gray-900">${p.title}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="border-b">
                            <td class="py-3 px-4 font-medium text-gray-700">Price</td>
                            ${properties.map(p => `<td class="py-3 px-4 text-blue-600 font-bold">€${p.price.toLocaleString()}</td>`).join('')}
                        </tr>
                        <tr class="border-b">
                            <td class="py-3 px-4 font-medium text-gray-700">Type</td>
                            ${properties.map(p => `<td class="py-3 px-4">${p.type}</td>`).join('')}
                        </tr>
                        <tr class="border-b">
                            <td class="py-3 px-4 font-medium text-gray-700">Bedrooms</td>
                            ${properties.map(p => `<td class="py-3 px-4">${p.bedrooms}</td>`).join('')}
                        </tr>
                        <tr class="border-b">
                            <td class="py-3 px-4 font-medium text-gray-700">Area</td>
                            ${properties.map(p => `<td class="py-3 px-4">${p.area} m²</td>`).join('')}
                        </tr>
                        <tr class="border-b">
                            <td class="py-3 px-4 font-medium text-gray-700">Location</td>
                            ${properties.map(p => `<td class="py-3 px-4">${p.city}</td>`).join('')}
                        </tr>
                        <tr>
                            <td class="py-3 px-4 font-medium text-gray-700">Features</td>
                            ${properties.map(p => `<td class="py-3 px-4">${p.features.join(', ')}</td>`).join('')}
                        </tr>
                    </tbody>
                </table>
            `;
        }

        function showPropertyModal(e) {
            const propertyId = parseInt(e.target.dataset.propertyId);
            const property = currentProperties.find(p => p.id === propertyId);
            
            const modal = document.getElementById('property-modal');
            const content = document.getElementById('modal-content');
            
            content.innerHTML = `
                <div class="relative">
                    <button class="absolute top-4 right-4 z-10 w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-lg" onclick="closePropertyModal()">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                    <img src="${property.image}" alt="${property.title}" class="w-full h-64 object-cover">
                </div>
                <div class="p-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">${property.title}</h2>
                    <p class="text-3xl font-bold text-blue-600 mb-6">€${property.price.toLocaleString()}</p>
                    
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-gray-900">${property.bedrooms}</div>
                            <div class="text-gray-600">Bedrooms</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-gray-900">${property.bathrooms}</div>
                            <div class="text-gray-600">Bathrooms</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-gray-900">${property.area}</div>
                            <div class="text-gray-600">m² Area</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-gray-900">${property.type}</div>
                            <div class="text-gray-600">Type</div>
                        </div>
                    </div>
                    
                    <div class="mb-8">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Features</h3>
                        <div class="flex flex-wrap gap-3">
                            ${property.features.map(feature => 
                                `<span class="bg-blue-100 text-blue-800 px-3 py-2 rounded-lg text-sm font-medium">${feature}</span>`
                            ).join('')}
                        </div>
                    </div>
                    
                    <div class="mb-8">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Location</h3>
                        <div class="flex items-center text-gray-600">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            ${property.city}
                        </div>
                    </div>
                    
                    <div class="flex space-x-4">
                        <button class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-medium transition-colors">
                            Contact Agent
                        </button>
                        <button class="flex-1 border border-blue-600 text-blue-600 hover:bg-blue-50 py-3 px-6 rounded-lg font-medium transition-colors">
                            Schedule Viewing
                        </button>
                    </div>
                </div>
            `;
            
            modal.classList.remove('hidden');
            
            // Animate modal
            anime({
                targets: modal.querySelector('.bg-white'),
                scale: [0.8, 1],
                opacity: [0, 1],
                duration: 300,
                easing: 'easeOutQuart'
            });
        }

        function closePropertyModal() {
            const modal = document.getElementById('property-modal');
            modal.classList.add('hidden');
        }

        function clearAllFilters() {
            document.getElementById('search-input').value = '';
            document.querySelectorAll('.property-type-filter').forEach(cb => {
                cb.checked = cb.value === 'all';
            });
            document.getElementById('price-min').value = 0;
            document.getElementById('price-max').value = 2000000;
            document.getElementById('price-min-value').textContent = '€0';
            document.getElementById('price-max-value').textContent = '€2M';
            document.querySelectorAll('.bedroom-filter').forEach(btn => {
                btn.classList.remove('bg-blue-600', 'text-white');
            });
            document.querySelector('.bedroom-filter[data-value="any"]').classList.add('bg-blue-600', 'text-white');
            document.getElementById('city-filter').value = 'all';
            document.querySelectorAll('.feature-filter').forEach(cb => cb.checked = false);
            
            applyFilters();
        }

        function updateResultsCount() {
            document.getElementById('results-count').textContent = `Showing ${filteredProperties.length} properties`;
        }

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
</body>
</html>