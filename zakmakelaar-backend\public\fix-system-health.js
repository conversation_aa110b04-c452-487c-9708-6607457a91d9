// Manual Fix for System Health Indicators
// Run this in browser console (F12) to populate the system health section

function fixSystemHealth() {
    console.log('🔧 Fixing System Health indicators...');
    
    // Define the health indicators and their target statuses
    const healthIndicators = [
        { id: 'systemStatus', status: 'critical', label: 'Overall System' },
        { id: 'scrapingHealth', status: 'critical', label: 'Scraping Service' },
        { id: 'transformationHealth', status: 'warning', label: 'Transformation Pipeline' },
        { id: 'databaseHealth', status: 'good', label: 'Database' },
        { id: 'apiHealth', status: 'good', label: 'API Services' }
    ];
    
    let updatedCount = 0;
    let notFoundCount = 0;
    
    healthIndicators.forEach(({ id, status, label }) => {
        const element = document.getElementById(id);
        if (element) {
            // Clear existing classes
            element.className = 'status-indicator';
            
            // Add appropriate status class
            switch (status.toLowerCase()) {
                case 'good':
                case 'healthy':
                case 'success':
                    element.classList.add('status-good');
                    break;
                case 'warning':
                case 'degraded':
                case 'unhealthy':
                    element.classList.add('status-warning');
                    break;
                case 'critical':
                case 'error':
                case 'failed':
                    element.classList.add('status-critical');
                    break;
                default:
                    element.classList.add('status-warning');
            }
            
            console.log(`✅ ${label} (${id}): Set to ${status}`);
            updatedCount++;
        } else {
            console.error(`❌ Element '${id}' not found for ${label}`);
            notFoundCount++;
        }
    });
    
    // Update system status text
    const systemStatusTextElement = document.getElementById('systemStatusText');
    if (systemStatusTextElement) {
        systemStatusTextElement.textContent = 'Critical';
        console.log('✅ System status text updated to: Critical');
        updatedCount++;
    } else {
        console.error('❌ systemStatusText element not found');
        notFoundCount++;
    }
    
    console.log(`🔧 System Health fix completed!`);
    console.log(`   ✅ Updated: ${updatedCount} elements`);
    console.log(`   ❌ Not found: ${notFoundCount} elements`);
    
    // Show what the user should expect to see
    console.log('\n📋 Expected System Health status:');
    console.log('   🔴 Overall System: Critical');
    console.log('   🔴 Scraping Service: Critical');  
    console.log('   🟡 Transformation Pipeline: Warning');
    console.log('   🟢 Database: Good');
    console.log('   🟢 API Services: Good');
}

// Auto-run the fix
fixSystemHealth();
