const { body, param, query, validationResult } = require('express-validator');
const { AppError } = require('./errorHandler');

// Middleware to handle validation results
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg);
    return next(new AppError(`Validation failed: ${errorMessages.join(', ')}`, 400));
  }
  next();
};

// User registration validation
const validateUserRegistration = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  body('firstName')
    .notEmpty()
    .withMessage('First name is required')
    .isString()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .notEmpty()
    .withMessage('Last name is required')
    .isString()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('phoneNumber')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
  handleValidationErrors
];

// User login validation
const validateUserLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
  handleValidationErrors
];

// User preferences validation
const validateUserPreferences = [
  body('preferences.location')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Location must be between 2 and 100 characters'),
  body('preferences.budget')
    .optional()
    .isNumeric()
    .isFloat({ min: 0 })
    .withMessage('Budget must be a positive number'),
  body('preferences.rooms')
    .optional()
    .isInt({ min: 1, max: 20 })
    .withMessage('Number of rooms must be between 1 and 20'),
  body('profile.name')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('profile.income')
    .optional()
    .isNumeric()
    .isFloat({ min: 0 })
    .withMessage('Income must be a positive number'),
  handleValidationErrors
];

// MongoDB ObjectId validation
const validateObjectId = [
  param('id')
    .isMongoId()
    .withMessage('Invalid ID format'),
  handleValidationErrors
];

const validateUserId = [
  param('userId')
    .isMongoId()
    .withMessage('Invalid user ID format'),
  handleValidationErrors
];

// Listing query validation
const validateListingQuery = [
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('sortBy')
    .optional()
    .isIn(['dateAdded', 'price', 'title', 'location', 'timestamp'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be either asc or desc'),
  query('location')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Location filter must be between 2 and 100 characters'),
  query('minPrice')
    .optional()
    .isNumeric()
    .isFloat({ min: 0 })
    .withMessage('Minimum price must be a positive number'),
  query('maxPrice')
    .optional()
    .isNumeric()
    .isFloat({ min: 0 })
    .withMessage('Maximum price must be a positive number'),
  handleValidationErrors
];

// Profile completion validation
const validateProfileCompletion = [
  body('firstName')
    .notEmpty()
    .withMessage('First name is required')
    .isString()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .notEmpty()
    .withMessage('Last name is required')
    .isString()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('userType')
    .isArray({ min: 1 })
    .withMessage('At least one user type must be selected')
    .custom((value) => {
      const validTypes = ['student', 'expat', 'young_professional', 'property_owner'];
      const invalidTypes = value.filter(type => !validTypes.includes(type));
      if (invalidTypes.length > 0) {
        throw new Error(`Invalid user types: ${invalidTypes.join(', ')}`);
      }
      return true;
    }),
  body('dateOfBirth')
    .optional()
    .isISO8601()
    .withMessage('Date of birth must be a valid date')
    .custom((value) => {
      const birthDate = new Date(value);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      if (age < 18 || age > 100) {
        throw new Error('Age must be between 18 and 100 years');
      }
      return true;
    }),
  body('nationality')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Nationality must be between 2 and 50 characters'),
  body('phoneNumber')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
  body('employment.occupation')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Occupation must be between 2 and 100 characters'),
  body('employment.employmentType')
    .optional()
    .isIn(['full-time', 'part-time', 'student', 'freelancer', 'unemployed'])
    .withMessage('Invalid employment type'),
  body('employment.contractType')
    .optional()
    .isIn(['permanent', 'temporary', 'student', 'freelancer'])
    .withMessage('Invalid contract type'),
  body('employment.monthlyIncome')
    .optional()
    .isNumeric()
    .isFloat({ min: 0, max: 1000000 })
    .withMessage('Monthly income must be between 0 and 1,000,000'),
  handleValidationErrors
];

// User type validation
const validateUserType = [
  body('userType')
    .isArray({ min: 1 })
    .withMessage('At least one user type must be selected')
    .custom((value) => {
      const validTypes = ['student', 'expat', 'young_professional', 'property_owner'];
      const invalidTypes = value.filter(type => !validTypes.includes(type));
      if (invalidTypes.length > 0) {
        throw new Error(`Invalid user types: ${invalidTypes.join(', ')}`);
      }
      return true;
    }),
  handleValidationErrors
];

// Language preference validation
const validateLanguagePreference = [
  body('language')
    .notEmpty()
    .withMessage('Language is required')
    .isIn(['dutch', 'english', 'arabic', 'turkish', 'polish'])
    .withMessage('Language must be one of: dutch, english, arabic, turkish, polish'),
  handleValidationErrors
];

// Employment information validation
const validateEmployment = [
  body('employment.occupation')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Occupation must be between 2 and 100 characters'),
  body('employment.employmentType')
    .optional()
    .isIn(['full-time', 'part-time', 'student', 'freelancer', 'unemployed'])
    .withMessage('Invalid employment type'),
  body('employment.contractType')
    .optional()
    .isIn(['permanent', 'temporary', 'student', 'freelancer'])
    .withMessage('Invalid contract type'),
  body('employment.employer')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Employer name must be between 2 and 100 characters'),
  body('employment.workLocation')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Work location must be between 2 and 100 characters'),
  body('employment.monthlyIncome')
    .optional()
    .isNumeric()
    .isFloat({ min: 0, max: 1000000 })
    .withMessage('Monthly income must be between 0 and 1,000,000'),
  handleValidationErrors
];

// Social preferences validation
const validateSocialPreferences = [
  body('socialPreferences.lookingForRoommate')
    .optional()
    .isBoolean()
    .withMessage('Looking for roommate must be a boolean'),
  body('socialPreferences.roommateCriteria.ageRange.min')
    .optional()
    .isInt({ min: 18, max: 100 })
    .withMessage('Minimum age must be between 18 and 100'),
  body('socialPreferences.roommateCriteria.ageRange.max')
    .optional()
    .isInt({ min: 18, max: 100 })
    .withMessage('Maximum age must be between 18 and 100')
    .custom((value, { req }) => {
      const minAge = req.body.socialPreferences?.roommateCriteria?.ageRange?.min;
      if (minAge && value < minAge) {
        throw new Error('Maximum age must be greater than or equal to minimum age');
      }
      return true;
    }),
  body('socialPreferences.roommateCriteria.gender')
    .optional()
    .isIn(['male', 'female', 'any'])
    .withMessage('Gender preference must be male, female, or any'),
  body('socialPreferences.roommateCriteria.lifestyle.cleanliness')
    .optional()
    .isIn(['very_clean', 'clean', 'moderate', 'relaxed'])
    .withMessage('Invalid cleanliness preference'),
  body('socialPreferences.roommateCriteria.lifestyle.noiseLevel')
    .optional()
    .isIn(['very_quiet', 'quiet', 'moderate', 'lively'])
    .withMessage('Invalid noise level preference'),
  body('socialPreferences.roommateCriteria.lifestyle.socialLevel')
    .optional()
    .isIn(['very_social', 'social', 'moderate', 'private'])
    .withMessage('Invalid social level preference'),
  body('socialPreferences.roommateCriteria.lifestyle.smokingTolerance')
    .optional()
    .isBoolean()
    .withMessage('Smoking tolerance must be a boolean'),
  body('socialPreferences.roommateCriteria.lifestyle.petTolerance')
    .optional()
    .isBoolean()
    .withMessage('Pet tolerance must be a boolean'),
  body('socialPreferences.roommateCriteria.lifestyle.guestPolicy')
    .optional()
    .isIn(['strict', 'moderate', 'flexible'])
    .withMessage('Invalid guest policy preference'),
  body('socialPreferences.isVisible')
    .optional()
    .isBoolean()
    .withMessage('Visibility setting must be a boolean'),
  handleValidationErrors
];

// Rental history validation
const validateRentalHistory = [
  body('rentalHistory.previousAddresses')
    .optional()
    .isArray()
    .withMessage('Previous addresses must be an array'),
  body('rentalHistory.previousAddresses.*.address')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Address must be between 5 and 200 characters'),
  body('rentalHistory.previousAddresses.*.landlordName')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Landlord name must be between 2 and 100 characters'),
  body('rentalHistory.previousAddresses.*.landlordContact')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 5, max: 100 })
    .withMessage('Landlord contact must be between 5 and 100 characters'),
  body('rentalHistory.previousAddresses.*.rentAmount')
    .optional()
    .isNumeric()
    .isFloat({ min: 0, max: 50000 })
    .withMessage('Rent amount must be between 0 and 50,000'),
  body('rentalHistory.previousAddresses.*.duration')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Duration must be between 1 and 50 characters'),
  body('rentalHistory.previousAddresses.*.reasonForLeaving')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Reason for leaving must be between 2 and 200 characters'),
  body('rentalHistory.previousAddresses.*.reference')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 2, max: 500 })
    .withMessage('Reference must be between 2 and 500 characters'),
  body('rentalHistory.evictions')
    .optional()
    .isBoolean()
    .withMessage('Evictions must be a boolean'),
  body('rentalHistory.paymentIssues')
    .optional()
    .isBoolean()
    .withMessage('Payment issues must be a boolean'),
  body('rentalHistory.creditScore')
    .optional()
    .isIn(['excellent', 'good', 'fair', 'poor'])
    .withMessage('Credit score must be one of: excellent, good, fair, poor'),
  handleValidationErrors
];

// Property owner registration validation
const validatePropertyOwnerRegistration = [
  body('businessRegistration')
    .notEmpty()
    .withMessage('Business registration number is required')
    .matches(/^\d{8}$/)
    .withMessage('KvK number must be exactly 8 digits'),
  body('taxNumber')
    .optional()
    .matches(/^NL\d{9}B\d{2}$/)
    .withMessage('Dutch VAT number must be in format NL123456789B12'),
  body('bankAccount')
    .optional()
    .matches(/^NL\d{2}[A-Z]{4}\d{10}$/)
    .withMessage('Dutch IBAN must be in format ******************'),
  body('businessName')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Business name must be between 2 and 100 characters'),
  body('businessAddress')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Business address must be between 5 and 200 characters'),
  body('businessPhone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid business phone number'),
  handleValidationErrors
];

// Property owner verification validation
const validatePropertyOwnerVerification = [
  body('documents')
    .optional()
    .isArray()
    .withMessage('Documents must be an array'),
  body('documents.*.documentId')
    .optional()
    .isString()
    .withMessage('Document ID must be a string'),
  body('documents.*.type')
    .optional()
    .isIn(['business_registration', 'tax_certificate', 'bank_statement', 'id_document'])
    .withMessage('Invalid document type'),
  handleValidationErrors
];

// Property owner profile validation
const validatePropertyOwnerProfile = [
  body('companyName')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Company name must be between 2 and 100 characters'),
  body('businessRegistration')
    .optional()
    .isString()
    .trim()
    .matches(/^\d{8}$/)
    .withMessage('Business registration must be 8 digits (KvK number)'),
  body('address')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Address must be between 5 and 200 characters'),
  body('phone')
    .optional()
    .isString()
    .trim()
    .matches(/^(\+31|0)[1-9]\d{8}$/)
    .withMessage('Phone must be a valid Dutch phone number'),
  body('website')
    .optional()
    .isURL()
    .withMessage('Website must be a valid URL'),
  body('description')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description must not exceed 1000 characters'),
  body('notificationSettings.email')
    .optional()
    .isBoolean()
    .withMessage('Email notification setting must be boolean'),
  body('notificationSettings.push')
    .optional()
    .isBoolean()
    .withMessage('Push notification setting must be boolean'),
  body('notificationSettings.sms')
    .optional()
    .isBoolean()
    .withMessage('SMS notification setting must be boolean'),
  handleValidationErrors
];

// Property data validation (for creating new properties)
const validatePropertyData = [
  body('title')
    .notEmpty()
    .withMessage('Property title is required')
    .isString()
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Property title must be between 5 and 200 characters'),
  body('description')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Description must not exceed 2000 characters'),
  body('address.street')
    .notEmpty()
    .withMessage('Street address is required')
    .isString()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Street must be between 2 and 100 characters'),
  body('address.houseNumber')
    .notEmpty()
    .withMessage('House number is required')
    .isString()
    .trim()
    .isLength({ min: 1, max: 10 })
    .withMessage('House number must be between 1 and 10 characters'),
  body('address.postalCode')
    .notEmpty()
    .withMessage('Postal code is required')
    .matches(/^\d{4}\s?[A-Z]{2}$/)
    .withMessage('Dutch postal code must be in format 1234AB'),
  body('address.city')
    .notEmpty()
    .withMessage('City is required')
    .isString()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('City must be between 2 and 50 characters'),
  body('propertyType')
    .notEmpty()
    .withMessage('Property type is required')
    .isIn(['apartment', 'house', 'studio', 'room', 'commercial'])
    .withMessage('Invalid property type'),
  body('rent.amount')
    .notEmpty()
    .withMessage('Rent amount is required')
    .isNumeric()
    .isFloat({ min: 100, max: 50000 })
    .withMessage('Rent amount must be between 100 and 50,000'),
  body('size')
    .optional()
    .isNumeric()
    .isFloat({ min: 10, max: 10000 })
    .withMessage('Size must be between 10 and 10,000 square meters'),
  body('rooms')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Number of rooms must be between 1 and 50'),
  body('bedrooms')
    .optional()
    .isInt({ min: 0, max: 20 })
    .withMessage('Number of bedrooms must be between 0 and 20'),
  body('bathrooms')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('Number of bathrooms must be between 1 and 10'),
  body('features.interior')
    .optional()
    .isIn(['kaal', 'gestoffeerd', 'gemeubileerd'])
    .withMessage('Interior type must be kaal, gestoffeerd, or gemeubileerd'),
  body('features.energyLabel')
    .optional()
    .isIn(['A+++', 'A++', 'A+', 'A', 'B', 'C', 'D', 'E', 'F', 'G'])
    .withMessage('Invalid energy label'),
  body('policies.minimumIncome')
    .optional()
    .isNumeric()
    .isFloat({ min: 0, max: 200000 })
    .withMessage('Minimum income must be between 0 and 200,000'),
  body('policies.maximumOccupants')
    .optional()
    .isInt({ min: 1, max: 20 })
    .withMessage('Maximum occupants must be between 1 and 20'),
  body('policies.minimumRentalPeriod')
    .optional()
    .isInt({ min: 1, max: 60 })
    .withMessage('Minimum rental period must be between 1 and 60 months'),
  body('availability.availableFrom')
    .optional()
    .isISO8601()
    .withMessage('Available from date must be a valid date'),
  handleValidationErrors
];

// Property data validation for updates (more flexible)
const validatePropertyUpdateData = [
  body('title')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Property title must be between 5 and 200 characters'),
  body('description')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Description must not exceed 2000 characters'),
  body('address.street')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Street must be between 2 and 100 characters'),
  body('address.houseNumber')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 10 })
    .withMessage('House number must be between 1 and 10 characters'),
  body('address.postalCode')
    .optional()
    .matches(/^\d{4}\s?[A-Z]{2}$/)
    .withMessage('Dutch postal code must be in format 1234AB'),
  body('address.city')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('City must be between 2 and 50 characters'),
  body('propertyType')
    .optional()
    .isIn(['apartment', 'house', 'studio', 'room', 'commercial'])
    .withMessage('Invalid property type'),
  body('rent.amount')
    .optional()
    .isNumeric()
    .isFloat({ min: 100, max: 50000 })
    .withMessage('Rent amount must be between 100 and 50,000'),
  body('size')
    .optional()
    .isNumeric()
    .isFloat({ min: 10, max: 10000 })
    .withMessage('Size must be between 10 and 10,000 square meters'),
  body('rooms')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Number of rooms must be between 1 and 50'),
  body('bedrooms')
    .optional()
    .isInt({ min: 0, max: 20 })
    .withMessage('Number of bedrooms must be between 0 and 20'),
  body('bathrooms')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('Number of bathrooms must be between 1 and 10'),
  body('features.interior')
    .optional()
    .isIn(['kaal', 'gestoffeerd', 'gemeubileerd'])
    .withMessage('Interior type must be kaal, gestoffeerd, or gemeubileerd'),
  body('features.energyLabel')
    .optional()
    .isIn(['A+++', 'A++', 'A+', 'A', 'B', 'C', 'D', 'E', 'F', 'G'])
    .withMessage('Invalid energy label'),
  body('policies.minimumIncome')
    .optional()
    .isNumeric()
    .isFloat({ min: 0, max: 200000 })
    .withMessage('Minimum income must be between 0 and 200,000'),
  body('policies.maximumOccupants')
    .optional()
    .isInt({ min: 1, max: 20 })
    .withMessage('Maximum occupants must be between 1 and 20'),
  body('policies.minimumRentalPeriod')
    .optional()
    .isInt({ min: 1, max: 60 })
    .withMessage('Minimum rental period must be between 1 and 60 months'),
  body('availability.availableFrom')
    .optional()
    .isISO8601()
    .withMessage('Available from date must be a valid date'),
  handleValidationErrors
];

// Property ID validation
const validatePropertyId = [
  param('propertyId')
    .isMongoId()
    .withMessage('Invalid property ID format'),
  handleValidationErrors
];

// Application ID validation
const validateApplicationId = [
  param('applicationId')
    .isMongoId()
    .withMessage('Invalid application ID format'),
  handleValidationErrors
];

// Screening request validation
const validateScreeningRequest = [
  body('applicationIds')
    .optional()
    .isArray()
    .withMessage('Application IDs must be an array'),
  body('applicationIds.*')
    .optional()
    .isMongoId()
    .withMessage('Each application ID must be a valid MongoDB ObjectId'),
  handleValidationErrors
];

// Ranking criteria validation
const validateRankingCriteria = [
  body('priorityFactors')
    .optional()
    .isObject()
    .withMessage('Priority factors must be an object'),
  body('priorityFactors.tenantScore')
    .optional()
    .isFloat({ min: 0, max: 1 })
    .withMessage('Tenant score weight must be between 0 and 1'),
  body('priorityFactors.applicationCompleteness')
    .optional()
    .isFloat({ min: 0, max: 1 })
    .withMessage('Application completeness weight must be between 0 and 1'),
  body('priorityFactors.responseTime')
    .optional()
    .isFloat({ min: 0, max: 1 })
    .withMessage('Response time weight must be between 0 and 1'),
  body('minimumScore')
    .optional()
    .isInt({ min: 0, max: 100 })
    .withMessage('Minimum score must be between 0 and 100'),
  body('maxApplicants')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Maximum applicants must be between 1 and 100'),
  handleValidationErrors
];

// Application status update validation
const validateApplicationStatusUpdate = [
  body('status')
    .notEmpty()
    .withMessage('Status is required')
    .isIn(['under_review', 'screening', 'shortlisted', 'interview', 'approved', 'conditional', 'rejected'])
    .withMessage('Invalid application status'),
  body('reason')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Reason must not exceed 500 characters'),
  body('notes')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Notes must not exceed 1000 characters'),
  handleValidationErrors
];

// Report type validation
const validateReportType = [
  query('type')
    .optional()
    .isIn(['comprehensive', 'screening', 'performance'])
    .withMessage('Report type must be comprehensive, screening, or performance'),
  handleValidationErrors
];

module.exports = {
  validateUserRegistration,
  validateUserLogin,
  validateUserPreferences,
  validateObjectId,
  validateUserId,
  validateListingQuery,
  validateProfileCompletion,
  validateUserType,
  validateLanguagePreference,
  validateEmployment,
  validateSocialPreferences,
  validateRentalHistory,
  validatePropertyOwnerRegistration,
  validatePropertyOwnerVerification,
  validatePropertyOwnerProfile,
  validatePropertyData,
  validatePropertyUpdateData,
  validatePropertyId,
  validateApplicationId,
  validateScreeningRequest,
  validateRankingCriteria,
  validateApplicationStatusUpdate,
  validateReportType,
  handleValidationErrors
};
