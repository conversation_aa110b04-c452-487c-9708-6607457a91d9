const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../app');
const User = require('../models/User');
const Property = require('../models/Property');
const Application = require('../models/Application');

// Mock Google AI to avoid actual API calls during tests
jest.mock('@google/generative-ai', () => ({
  GoogleGenerativeAI: jest.fn().mockImplementation(() => ({
    getGenerativeModel: jest.fn().mockReturnValue({
      generateContent: jest.fn().mockResolvedValue({
        response: {
          text: () => JSON.stringify({
            recommendation: 'approve',
            confidence: 85,
            reasoning: 'Strong candidate with stable income',
            strengths: ['Good income ratio', 'Complete documentation'],
            concerns: [],
            score: 85,
            specificAdvice: 'Consider scheduling interview'
          })
        }
      })
    })
  }))
}));

describe('Property Owner AI Features Integration Tests', () => {
  let propertyOwnerId;
  let propertyId;
  let applicationId;
  let authToken;

  beforeAll(async () => {
    // Connect to test database
    const mongoUri = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/zakmakelaar-test';
    await mongoose.connect(mongoUri);
    
    // Clear test data
    await User.deleteMany({});
    await Property.deleteMany({});
    await Application.deleteMany({});
  });

  afterAll(async () => {
    // Clean up and close database connection
    await User.deleteMany({});
    await Property.deleteMany({});
    await Application.deleteMany({});
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Create a test property owner
    const propertyOwner = new User({
      email: '<EMAIL>',
      password: 'hashedPassword123',
      profile: {
        firstName: 'John',
        lastName: 'Owner',
        phoneNumber: '+***********',
        dateOfBirth: new Date('1980-01-01'),
        nationality: 'Dutch'
      },
      propertyOwner: {
        isPropertyOwner: true,
        verificationStatus: 'verified',
        businessRegistration: '12345678',
        companyName: 'Test Properties BV',
        address: 'Test Street 123, Amsterdam'
      }
    });
    const savedOwner = await propertyOwner.save();
    propertyOwnerId = savedOwner._id;

    // Create auth token (simplified)
    authToken = 'Bearer test-jwt-token';

    // Create a test property
    const property = new Property({
      title: 'Test Property',
      description: 'A test property for AI features',
      address: {
        street: 'Test Street',
        houseNumber: '123',
        postalCode: '1000AA',
        city: 'Amsterdam'
      },
      propertyType: 'apartment',
      rent: {
        amount: 1500,
        currency: 'EUR'
      },
      size: 75,
      rooms: 3,
      bedrooms: 2,
      bathrooms: 1,
      owner: {
        userId: propertyOwnerId,
        name: 'John Owner'
      },
      status: 'active'
    });
    const savedProperty = await property.save();
    propertyId = savedProperty._id;

    // Create a test application
    const application = new Application({
      property: {
        propertyId: propertyId,
        snapshot: {
          title: 'Test Property',
          address: 'Test Street 123, Amsterdam',
          rent: 1500
        }
      },
      applicant: {
        userId: new mongoose.Types.ObjectId(),
        snapshot: {
          name: 'Jane Applicant',
          email: '<EMAIL>',
          phone: '+31687654321'
        }
      },
      applicationData: {
        personalMessage: 'I would love to rent this property',
        moveInDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        numberOfOccupants: 2,
        employment: {
          occupation: 'Software Developer',
          employer: 'Tech Company',
          monthlyIncome: 5000,
          contractType: 'permanent'
        }
      },
      status: 'submitted',
      submittedAt: new Date()
    });
    const savedApplication = await application.save();
    applicationId = savedApplication._id;
  });

  afterEach(async () => {
    // Clean up test data
    await User.deleteMany({});
    await Property.deleteMany({});
    await Application.deleteMany({});
  });

  describe('AI Applicant Filtering', () => {
    it('should filter and rank applicants successfully', async () => {
      // Mock authentication middleware
      jest.spyOn(require('../middleware/auth'), 'auth').mockImplementation((req, res, next) => {
        req.user = { _id: propertyOwnerId };
        next();
      });

      const response = await request(app)
        .post(`/api/property-owner/properties/${propertyId}/filter-applicants`)
        .set('Authorization', authToken)
        .send({
          minimumIncomeRatio: 3.0,
          minimumTenantScore: 60,
          weights: {
            income: 30,
            employment: 20,
            tenantScore: 25,
            completeness: 15,
            personalFit: 10
          }
        })
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data).toHaveProperty('filteredApplications');
      expect(response.body.data).toHaveProperty('summary');
      expect(response.body.data.totalApplications).toBeGreaterThanOrEqual(0);
    });

    it('should get AI insights for an application', async () => {
      // Mock authentication middleware
      jest.spyOn(require('../middleware/auth'), 'auth').mockImplementation((req, res, next) => {
        req.user = { _id: propertyOwnerId };
        next();
      });

      const response = await request(app)
        .get(`/api/property-owner/applications/${applicationId}/insights`)
        .set('Authorization', authToken)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data).toHaveProperty('quickInsights');
      expect(response.body.data).toHaveProperty('scores');
      expect(response.body.data).toHaveProperty('recommendation');
    });
  });

  describe('Viewing Scheduler', () => {
    it('should initialize viewing schedule', async () => {
      // Mock authentication middleware
      jest.spyOn(require('../middleware/auth'), 'auth').mockImplementation((req, res, next) => {
        req.user = { _id: propertyOwnerId };
        next();
      });

      const availabilityData = {
        weekdays: {
          monday: { available: true, startTime: '09:00', endTime: '18:00' },
          tuesday: { available: true, startTime: '09:00', endTime: '18:00' },
          wednesday: { available: true, startTime: '09:00', endTime: '18:00' },
          thursday: { available: true, startTime: '09:00', endTime: '18:00' },
          friday: { available: true, startTime: '09:00', endTime: '18:00' },
          saturday: { available: true, startTime: '10:00', endTime: '16:00' },
          sunday: { available: false, startTime: '', endTime: '' }
        },
        preferredDuration: 30,
        bufferTime: 15,
        maxViewingsPerDay: 8
      };

      const response = await request(app)
        .post(`/api/property-owner/properties/${propertyId}/viewing-schedule`)
        .set('Authorization', authToken)
        .send(availabilityData)
        .expect(201);

      expect(response.body.status).toBe('success');
      expect(response.body.data).toHaveProperty('property');
      expect(response.body.data).toHaveProperty('availability');
      expect(response.body.data).toHaveProperty('timeSlots');
    });

    it('should get viewing schedule', async () => {
      // First initialize the schedule
      const { ViewingSchedulerService } = require('../services/viewingSchedulerService');
      await ViewingSchedulerService.initializeSchedule(propertyId);

      // Mock authentication middleware
      jest.spyOn(require('../middleware/auth'), 'auth').mockImplementation((req, res, next) => {
        req.user = { _id: propertyOwnerId };
        next();
      });

      const response = await request(app)
        .get(`/api/property-owner/properties/${propertyId}/viewing-schedule`)
        .set('Authorization', authToken)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data).toHaveProperty('property');
      expect(response.body.data).toHaveProperty('timeSlots');
    });
  });

  describe('Applicant Checklist', () => {
    it('should create applicant checklist', async () => {
      // Mock authentication middleware
      jest.spyOn(require('../middleware/auth'), 'auth').mockImplementation((req, res, next) => {
        req.user = { _id: propertyOwnerId };
        next();
      });

      const response = await request(app)
        .post(`/api/property-owner/applications/${applicationId}/checklist`)
        .set('Authorization', authToken)
        .send({})
        .expect(201);

      expect(response.body.status).toBe('success');
      expect(response.body.data).toHaveProperty('checklist');
      expect(response.body.data).toHaveProperty('summary');
      expect(response.body.data.checklist).toHaveProperty('documents');
      expect(response.body.data.checklist).toHaveProperty('backgroundCheck');
      expect(response.body.data.checklist).toHaveProperty('interview');
    });

    it('should get all checklists for owner', async () => {
      // First create a checklist
      const { ApplicantChecklistService } = require('../services/applicantChecklistService');
      await ApplicantChecklistService.createChecklist(applicationId, propertyOwnerId);

      // Mock authentication middleware
      jest.spyOn(require('../middleware/auth'), 'auth').mockImplementation((req, res, next) => {
        req.user = { _id: propertyOwnerId };
        next();
      });

      const response = await request(app)
        .get('/api/property-owner/checklists')
        .set('Authorization', authToken)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data).toBeInstanceOf(Array);
    });
  });

  describe('Enhanced Dashboard', () => {
    it('should get enhanced dashboard data', async () => {
      // Mock authentication middleware
      jest.spyOn(require('../middleware/auth'), 'auth').mockImplementation((req, res, next) => {
        req.user = { _id: propertyOwnerId };
        next();
      });

      const response = await request(app)
        .get('/api/property-owner/dashboard-data')
        .set('Authorization', authToken)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data).toHaveProperty('aiInsights');
      expect(response.body.data.aiInsights).toHaveProperty('quickStats');
      expect(response.body.data.aiInsights).toHaveProperty('nextActions');
    });
  });

  describe('Error Handling', () => {
    it('should return 403 for unauthorized property access', async () => {
      const otherOwnerId = new mongoose.Types.ObjectId();
      
      // Mock authentication middleware with different user
      jest.spyOn(require('../middleware/auth'), 'auth').mockImplementation((req, res, next) => {
        req.user = { _id: otherOwnerId };
        next();
      });

      const response = await request(app)
        .post(`/api/property-owner/properties/${propertyId}/filter-applicants`)
        .set('Authorization', authToken)
        .send({})
        .expect(403);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toContain('Unauthorized');
    });

    it('should return 404 for non-existent application', async () => {
      const fakeApplicationId = new mongoose.Types.ObjectId();
      
      // Mock authentication middleware
      jest.spyOn(require('../middleware/auth'), 'auth').mockImplementation((req, res, next) => {
        req.user = { _id: propertyOwnerId };
        next();
      });

      const response = await request(app)
        .get(`/api/property-owner/applications/${fakeApplicationId}/insights`)
        .set('Authorization', authToken)
        .expect(404);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toContain('not found');
    });
  });
});