@echo on
setlocal

rem Android SDK / emulator resolution (robust)
set "SDK="
set "EMU="
set "ADB="
for %%D in ("%ANDROID_SDK_ROOT%" "%ANDROID_HOME%" "%LOCALAPPDATA%\Android\Sdk" "%USERPROFILE%\AppData\Local\Android\Sdk" "C:\Android\Sdk") do (
  if not "%%~D"=="" if exist "%%~D\emulator\emulator.exe" (
    set "SDK=%%~D"
    set "EMU=%%~D\emulator\emulator.exe"
    set "ADB=%%~D\platform-tools\adb.exe"
  )
)
if not defined EMU (
  for /f "delims=" %%P in ('where emulator.exe 2^>nul') do if not defined EMU set "EMU=%%~fP"
)
if not defined ADB (
  for /f "delims=" %%P in ('where adb.exe 2^>nul') do if not defined ADB set "ADB=%%~fP"
)

rem Configurable options
set "AVD=Pixel_9_Pro"
set "GPU=swiftshader_indirect"
set "PORT=5556"
set "LOG=%TEMP%\emulator_%AVD%_%RANDOM%.log"
set "PORTS="
set "WIPE="
set "COLD=1"

rem Parse optional flags
set "NOPAUSE="
set "DETACHED="
if not "%~1"=="" (
  for %%A in (%*) do (
    if /I "%%~A"=="-nopause" set "NOPAUSE=1"
    if /I "%%~A"=="-detached" set "DETACHED=1"
    if /I "%%~A"=="wipe" set "WIPE=1" & set "COLD="
    if /I "%%~A"=="cold" set "COLD=1"
    for /f "tokens=1,2 delims==" %%K in ("%%~A") do (
      if /I "%%K"=="avd" set "AVD=%%L"
      if /I "%%K"=="gpu" set "GPU=%%L"
      if /I "%%K"=="ports" set "PORTS=%%L"
      if /I "%%K"=="log" set "LOG=%%L"
    )
  )
)

rem Try fallback to PATH if emulator.exe not at expected location
if not exist "%EMU%" (
  echo Emulator not found. Looked for: "%EMU%"
  echo Install Android Emulator via SDK Manager or add it to PATH
  if not defined NOPAUSE pause
  exit /b 1
)

echo Resolved EMU path: %EMU%
if defined ADB echo Resolved ADB path: %ADB%
echo Starting Android Emulator: %AVD%
echo GPU: %GPU%
if defined PORTS (echo Ports: %PORTS%) else (echo Port: %PORT%)
if defined WIPE (echo Mode: wipe-data) else if defined COLD (echo Mode: cold boot) else (echo Mode: default)
echo Log: %LOG%
echo.

echo Available AVDs:
"%EMU%" -list-avds
echo.
echo Emulator on PATH:
where emulator.exe 2>nul
echo.
echo Checking acceleration support...
if exist "%SDK%\emulator\emulator-check.exe" (
  "%SDK%\emulator\emulator-check.exe" accel >> "%LOG%" 2>&1
)
echo.

echo Killing stale processes...
taskkill /IM emulator.exe /F >nul 2>&1
taskkill /IM qemu-system-x86_64.exe /F >nul 2>&1
taskkill /IM qemu-system-aarch64.exe /F >nul 2>&1
if exist "%ADB%" (
  "%ADB%" kill-server >nul 2>&1
  "%ADB%" start-server >nul 2>&1
)
echo.

echo Launching emulator...
set "SNAPSHOT_OPTS="
if defined WIPE set "SNAPSHOT_OPTS=-wipe-data"
if defined COLD set "SNAPSHOT_OPTS=-no-snapshot-load -no-snapshot-save"
set "PORT_OPTS=-port %PORT%"
if defined PORTS set "PORT_OPTS=-ports %PORTS%"
set "COMMON_OPTS=-avd %AVD% %PORT_OPTS% -gpu %GPU% %SNAPSHOT_OPTS% -no-boot-anim -verbose -debug init,adb,emulator,egl,gralloc,accel"
if defined DETACHED (
  rem Launch in a separate process and return immediately
  start "Android Emulator" cmd /c ""%EMU%" %COMMON_OPTS% >> "%LOG%" 2>&1"
  set "EXITCODE=%ERRORLEVEL%"
  rem Give it a moment to write to the log
  timeout /t 3 /nobreak >nul 2>&1
)
if not defined DETACHED (
  "%EMU%" %COMMON_OPTS% > "%LOG%" 2>&1
  set "EXITCODE=%ERRORLEVEL%"
)

echo.
echo Emulator exited with code %EXITCODE%.
echo Tail of log:
powershell -NoProfile -Command "Get-Content -Tail 80 '%LOG%'" 2>nul
echo.
echo Full log at: %LOG%
if not defined NOPAUSE (
  echo Press any key to close...
  pause >nul
)

endlocal
