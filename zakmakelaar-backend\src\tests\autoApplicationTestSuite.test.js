/**
 * Comprehensive Auto Application Test Suite
 *
 * Tests all components of the auto application system including:
 * - Browser management and stability
 * - Form detection accuracy
 * - Rate limiting effectiveness
 * - Error handling and recovery
 * - End-to-end application flow
 */

const ImprovedBrowserManager = require("../services/improvedBrowserManager");
const EnhancedFormDetector = require("../services/enhancedFormDetector");
const AdaptiveRateLimiter = require("../services/adaptiveRateLimiter");
const FormAutomationService = require("../services/formAutomationService");

describe("Auto Application System Test Suite", () => {
  let browserManager;
  let formDetector;
  let rateLimiter;
  let formAutomationService;

  beforeEach(() => {
    browserManager = new ImprovedBrowserManager();
    formDetector = new EnhancedFormDetector();
    rateLimiter = new AdaptiveRateLimiter();
    formAutomationService = new FormAutomationService();
  });

  afterEach(async () => {
    // Cleanup browsers after each test
    if (browserManager) {
      await browserManager.cleanup();
    }
    if (formAutomationService && formAutomationService.browser) {
      try {
        await formAutomationService.browser.close();
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  });

  describe("Browser Management Tests", () => {
    it("should initialize browser successfully", async () => {
      const browser = await browserManager.getBrowser();
      expect(browser).toBeDefined();
      expect(browser.isConnected()).toBe(true);
    }, 30000);

    it("should handle browser crashes gracefully", async () => {
      const browser = await browserManager.getBrowser();

      // Simulate browser crash
      await browser.close();

      // Should recover and provide new browser
      const newBrowser = await browserManager.getBrowser();
      expect(newBrowser).toBeDefined();
      expect(newBrowser.isConnected()).toBe(true);
    }, 30000);

    it("should maintain browser health monitoring", async () => {
      await browserManager.getBrowser();
      const status = browserManager.getStatus();

      expect(status).toHaveProperty("isHealthy");
      expect(status).toHaveProperty("uptime");
      expect(status).toHaveProperty("memoryUsage");
      expect(status).toHaveProperty("pageCount");
    }, 15000);

    it("should cleanup resources properly", async () => {
      await browserManager.getBrowser();
      const initialStatus = browserManager.getStatus();

      await browserManager.cleanup();
      const finalStatus = browserManager.getStatus();

      expect(finalStatus.pageCount).toBe(0);
      expect(finalStatus.isHealthy).toBe(false);
    }, 15000);
  });

  describe("Form Detection Tests", function () {
    let browser, page;

    beforeEach(async function () {
      browser = await browserManager.getBrowser();
      page = await browser.newPage();
    });

    afterEach(async function () {
      if (page) {
        await page.close();
      }
    });

    it("should detect Funda.nl forms correctly", async function () {
      // Create mock Funda page
      await page.setContent(`
        <html>
          <body>
            <button class="contact-button">Contact</button>
            <form id="contact-form">
              <input id="firstName" name="name" type="text" />
              <input id="emailAddress" name="email" type="email" />
              <input id="phoneNumber" name="phone" type="tel" />
              <textarea id="questionInput" name="message"></textarea>
              <button type="submit">Send</button>
            </form>
          </body>
        </html>
      `);

      const formInfo = await formDetector.detectForms(
        page,
        "https://www.funda.nl/test"
      );

      expect(formInfo.platform).to.equal("funda.nl");
      expect(formInfo.confidence).to.be.above(0.7);
      expect(formInfo.contactButton).to.exist;
      expect(formInfo.fields).to.have.length.above(3);
    });

    it("should handle unknown platforms with generic detection", async function () {
      await page.setContent(`
        <html>
          <body>
            <form class="contact-form">
              <input name="name" type="text" placeholder="Your name" />
              <input name="email" type="email" placeholder="Your email" />
              <textarea name="message" placeholder="Your message"></textarea>
              <button type="submit">Submit</button>
            </form>
          </body>
        </html>
      `);

      const formInfo = await formDetector.detectForms(
        page,
        "https://unknown-site.com/contact"
      );

      expect(formInfo.platform).to.equal("generic");
      expect(formInfo.type).to.equal("generic");
      expect(formInfo.fields).to.have.length.above(2);
    });

    it("should calculate form complexity accurately", async function () {
      await page.setContent(`
        <html>
          <body>
            <form>
              <input name="name" type="text" required />
              <input name="email" type="email" required />
              <input name="phone" type="tel" />
              <select name="country">
                <option>Netherlands</option>
                <option>Germany</option>
              </select>
              <input name="document" type="file" />
              <textarea name="message" required></textarea>
            </form>
          </body>
        </html>
      `);

      const formInfo = await formDetector.detectForms(page, "https://test.com");
      const complexity = formDetector.calculateComplexity(formInfo);

      expect(complexity).to.be.above(3); // Should be complex due to file upload and select
    });
  });

  describe("Rate Limiting Tests", function () {
    const testUserId = "test-user-123";
    const testPlatform = "funda.nl";
    const testUrl = "https://www.funda.nl/test-property";

    it("should allow applications within limits", async function () {
      const canSubmit = await rateLimiter.canSubmit(
        testUserId,
        testPlatform,
        testUrl
      );
      expect(canSubmit.allowed).to.be.true;
      expect(canSubmit.recommendedDelay).to.be.a("number");
    });

    it("should enforce platform limits", async function () {
      // Record multiple applications quickly
      for (let i = 0; i < 10; i++) {
        rateLimiter.recordSuccess(testUserId, testPlatform, testUrl);
      }

      const canSubmit = await rateLimiter.canSubmit(
        testUserId,
        testPlatform,
        testUrl
      );
      expect(canSubmit.allowed).to.be.false;
      expect(canSubmit.reason).to.equal("platform_limit_exceeded");
    });

    it("should adapt to success rates", async function () {
      // Record several failures
      for (let i = 0; i < 5; i++) {
        rateLimiter.recordFailure(
          testUserId,
          testPlatform,
          testUrl,
          new Error("Test failure")
        );
      }

      const limits = rateLimiter.getCurrentLimits(testPlatform, testUrl);
      expect(limits.adaptationLevel).to.be.oneOf([
        "poor",
        "critical",
        "failing",
      ]);
    });

    it("should handle detection events with cooldown", async function () {
      // Record detection event
      rateLimiter.recordFailure(
        testUserId,
        testPlatform,
        testUrl,
        new Error("blocked by captcha")
      );

      const canSubmit = await rateLimiter.canSubmit(
        testUserId,
        testPlatform,
        testUrl
      );
      expect(canSubmit.allowed).to.be.false;
      expect(canSubmit.reason).to.equal("detection_cooldown");
    });
  });

  describe("Error Handling Tests", function () {
    it("should categorize errors correctly", function () {
      const rateLimiter = new AdaptiveRateLimiter();

      expect(
        rateLimiter._categorizeError(new Error("blocked by server"))
      ).to.equal("blocked");
      expect(
        rateLimiter._categorizeError(new Error("captcha required"))
      ).to.equal("detection");
      expect(
        rateLimiter._categorizeError(new Error("network timeout"))
      ).to.equal("network");
      expect(
        rateLimiter._categorizeError(new Error("form not found"))
      ).to.equal("form");
      expect(rateLimiter._categorizeError(new Error("unknown error"))).to.equal(
        "unknown"
      );
    });

    it("should handle browser disconnection gracefully", async function () {
      const browser = await browserManager.getBrowser();
      await browser.disconnect();

      // Should recover automatically
      const newBrowser = await browserManager.getBrowser();
      expect(newBrowser.isConnected()).to.be.true;
    });
  });

  describe("Integration Tests", function () {
    it("should complete full application flow simulation", async function () {
      // This test simulates the complete flow without actually submitting
      const mockUserData = {
        name: "Test User",
        email: "<EMAIL>",
        phone: "+31612345678",
        message: "I am interested in this property.",
      };

      const mockListingData = {
        url: "https://www.funda.nl/test-property",
        title: "Test Property",
        price: "€1,500",
        location: "Amsterdam",
      };

      // Check rate limiting
      const canSubmit = await rateLimiter.canSubmit(
        "test-user",
        "funda.nl",
        mockListingData.url
      );
      expect(canSubmit.allowed).to.be.true;

      // Get browser
      const browser = await browserManager.getBrowser();
      expect(browser.isConnected()).to.be.true;

      // Create test page
      const page = await browser.newPage();
      await page.setContent(`
        <html>
          <body>
            <h1>Test Property Listing</h1>
            <button class="contact-button">Contact Agent</button>
            <form id="contact-form" style="display: none;">
              <input id="firstName" name="name" type="text" />
              <input id="emailAddress" name="email" type="email" />
              <input id="phoneNumber" name="phone" type="tel" />
              <textarea id="questionInput" name="message"></textarea>
              <button type="submit">Send Message</button>
            </form>
            <script>
              document.querySelector('.contact-button').onclick = function() {
                document.getElementById('contact-form').style.display = 'block';
              };
            </script>
          </body>
        </html>
      `);

      // Detect forms
      const formInfo = await formDetector.detectForms(
        page,
        mockListingData.url
      );
      expect(formInfo.confidence).to.be.above(0.5);

      // Simulate clicking contact button
      if (formInfo.contactButton) {
        await page.click(formInfo.contactButton.selector);
        await page.waitForTimeout(1000); // Wait for form to appear
      }

      // Verify form is now visible
      const formVisible = await page.evaluate(() => {
        const form = document.getElementById("contact-form");
        return form && form.style.display !== "none";
      });
      expect(formVisible).to.be.true;

      // Record successful simulation
      rateLimiter.recordSuccess(
        "test-user",
        "funda.nl",
        mockListingData.url,
        2500
      );

      await page.close();
    });
  });

  describe("Performance Tests", function () {
    it("should handle multiple concurrent operations", async function () {
      const operations = [];

      // Create multiple concurrent browser operations
      for (let i = 0; i < 5; i++) {
        operations.push(async () => {
          const browser = await browserManager.getBrowser();
          const page = await browser.newPage();
          await page.goto(
            "data:text/html,<html><body><h1>Test</h1></body></html>"
          );
          await page.close();
        });
      }

      // All operations should complete successfully
      await Promise.all(operations.map((op) => op()));

      const status = browserManager.getStatus();
      expect(status.isHealthy).to.be.true;
    });

    it("should maintain performance under load", async function () {
      const startTime = Date.now();

      // Perform multiple form detections
      const browser = await browserManager.getBrowser();
      const page = await browser.newPage();

      for (let i = 0; i < 10; i++) {
        await page.setContent(
          `<html><body><form><input name="test${i}" /></form></body></html>`
        );
        await formDetector.detectForms(page, `https://test${i}.com`);
      }

      const endTime = Date.now();
      const totalTime = endTime - startTime;

      // Should complete within reasonable time (10 seconds)
      expect(totalTime).to.be.below(10000);

      await page.close();
    });
  });
});

module.exports = {
  ImprovedBrowserManager,
  EnhancedFormDetector,
  AdaptiveRateLimiter,
  FormAutomationService,
};
