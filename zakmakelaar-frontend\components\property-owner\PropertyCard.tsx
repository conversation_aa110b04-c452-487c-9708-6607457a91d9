import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Image,
  Dimensions,
  ActivityIndicator,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Theme, StatusColors } from '../../constants/Theme';

const { width } = Dimensions.get('window');

interface Property {
  id: string;
  title: string;
  address: string;
  price: number;
  bedrooms: number;
  bathrooms: number;
  size: number;
  images: string[];
  status: 'draft' | 'active' | 'rented' | 'maintenance' | 'inactive';
  occupancyStatus: string;
  applicationsCount: number;
}

interface PropertyCardProps {
  property: Property;
  onPress: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onActivate?: () => void;
  onDeactivate?: () => void;
  isLoading?: boolean;
}

export const PropertyCard: React.FC<PropertyCardProps> = ({
  property,
  onPress,
  onEdit,
  onDelete,
  onActivate,
  onDeactivate,
  isLoading = false,
}) => {
  const scaleValue = React.useRef(new Animated.Value(1)).current;
  const fadeValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.timing(fadeValue, {
      toValue: 1,
      duration: Theme.animation.normal,
      useNativeDriver: true,
    }).start();
  }, []);

  const handlePressIn = () => {
    Animated.spring(scaleValue, {
      toValue: 0.98,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleValue, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const getStatusConfig = (status: string) => {
    return StatusColors[status as keyof typeof StatusColors] || StatusColors.draft;
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('nl-NL', {
      style: 'currency',
      currency: 'EUR',
    }).format(price);
  };

  const statusConfig = getStatusConfig(property.status);

  return (
    <Animated.View
      style={[
        styles.wrapper,
        {
          opacity: fadeValue,
          transform: [{ scale: scaleValue }],
        },
      ]}
    >
      <TouchableOpacity
        style={styles.container}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.95}
      >
        {/* Image Container */}
        <View style={styles.imageContainer}>
          {property.images && property.images.length > 0 ? (
            <>
              <Image source={{ uri: property.images[0] }} style={styles.image} />
              <View style={styles.imageOverlay} />
            </>
          ) : (
            <View style={styles.placeholderImage}>
              <Ionicons name="home" size={56} color={Theme.colors.neutral[400]} />
            </View>
          )}
          
          {/* Modern Status Badge */}
          <View style={[styles.statusBadge, { backgroundColor: statusConfig.background }]}>
            <View style={[styles.statusIndicator, { backgroundColor: statusConfig.border }]} />
            <Text style={[styles.statusText, { color: statusConfig.text }]}>
              {property.status.charAt(0).toUpperCase() + property.status.slice(1)}
            </Text>
          </View>
        </View>

        {/* Content Section */}
        <View style={styles.content}>
          {/* Title and Address */}
          <View style={styles.headerSection}>
            <Text style={styles.title} numberOfLines={2}>
              {property.title}
            </Text>
            
            <View style={styles.addressRow}>
              <Ionicons name="location-outline" size={16} color={Theme.colors.textTertiary} />
              <Text style={styles.address} numberOfLines={1}>
                {property.address}
              </Text>
            </View>
          </View>
          
          {/* Price */}
          <Text style={styles.price}>{formatPrice(property.price)}</Text>
          
          {/* Property Details Grid */}
          <View style={styles.detailsGrid}>
            <View style={styles.detailItem}>
              <View style={styles.detailIconContainer}>
                <Ionicons name="bed-outline" size={18} color={Theme.colors.primary} />
              </View>
              <Text style={styles.detailText}>{property.bedrooms}</Text>
              <Text style={styles.detailLabel}>bed</Text>
            </View>
            
            <View style={styles.detailItem}>
              <View style={styles.detailIconContainer}>
                <Ionicons name="water-outline" size={18} color={Theme.colors.primary} />
              </View>
              <Text style={styles.detailText}>{property.bathrooms}</Text>
              <Text style={styles.detailLabel}>bath</Text>
            </View>
            
            <View style={styles.detailItem}>
              <View style={styles.detailIconContainer}>
                <Ionicons name="expand-outline" size={18} color={Theme.colors.primary} />
              </View>
              <Text style={styles.detailText}>{property.size}</Text>
              <Text style={styles.detailLabel}>m²</Text>
            </View>
          </View>

          {/* Footer with Applications and Status */}
          <View style={styles.footer}>
            <View style={styles.applicationsBadge}>
              <Ionicons name="people" size={16} color={Theme.colors.primary} />
              <Text style={styles.applicationsText}>
                {property.applicationsCount} applications
              </Text>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.actions}>
            {/* Primary Action Button */}
            {property.status === 'draft' && onActivate && (
              <TouchableOpacity 
                onPress={onActivate} 
                style={[styles.primaryButton, isLoading && styles.buttonDisabled]}
                disabled={isLoading}
              >
                {isLoading ? (
                  <ActivityIndicator size="small" color={Theme.colors.textInverse} />
                ) : (
                  <>
                    <Ionicons name="play-circle-outline" size={18} color={Theme.colors.textInverse} />
                    <Text style={styles.primaryButtonText}>Publish</Text>
                  </>
                )}
              </TouchableOpacity>
            )}
            
            {property.status === 'active' && onDeactivate && (
              <TouchableOpacity 
                onPress={onDeactivate} 
                style={[styles.warningButton, isLoading && styles.buttonDisabled]}
                disabled={isLoading}
              >
                {isLoading ? (
                  <ActivityIndicator size="small" color={Theme.colors.textInverse} />
                ) : (
                  <>
                    <Ionicons name="pause-circle-outline" size={18} color={Theme.colors.textInverse} />
                    <Text style={styles.warningButtonText}>Pause</Text>
                  </>
                )}
              </TouchableOpacity>
            )}
            
            {property.status === 'inactive' && onActivate && (
              <TouchableOpacity 
                onPress={onActivate} 
                style={[styles.primaryButton, isLoading && styles.buttonDisabled]}
                disabled={isLoading}
              >
                {isLoading ? (
                  <ActivityIndicator size="small" color={Theme.colors.textInverse} />
                ) : (
                  <>
                    <Ionicons name="play-circle-outline" size={18} color={Theme.colors.textInverse} />
                    <Text style={styles.primaryButtonText}>Reactivate</Text>
                  </>
                )}
              </TouchableOpacity>
            )}

            {/* Secondary Actions */}
            <View style={styles.secondaryActions}>
              {onEdit && (
                <TouchableOpacity onPress={onEdit} style={styles.iconButton}>
                  <Ionicons name="create-outline" size={20} color={Theme.colors.primary} />
                </TouchableOpacity>
              )}
              {onDelete && property.status !== 'rented' && (
                <TouchableOpacity onPress={onDelete} style={[styles.iconButton, styles.deleteIconButton]}>
                  <Ionicons name="trash-outline" size={20} color={Theme.colors.error} />
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    marginBottom: Theme.spacing.base,
  },
  container: {
    backgroundColor: Theme.colors.surface,
    borderRadius: Theme.borderRadius.xl,
    overflow: 'hidden',
    ...Theme.shadows.lg,
  },
  imageContainer: {
    position: 'relative',
    height: 240,
    backgroundColor: Theme.colors.neutral[100],
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  imageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.03)',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: Theme.colors.neutral[50],
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusBadge: {
    position: 'absolute',
    top: Theme.spacing.md,
    right: Theme.spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing.md,
    paddingVertical: Theme.spacing.xs,
    borderRadius: Theme.borderRadius.full,
    backdropFilter: 'blur(10px)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: Theme.spacing.xs,
  },
  statusText: {
    fontSize: Theme.typography.fontSize.xs,
    fontWeight: Theme.typography.fontWeight.semiBold,
    textTransform: 'capitalize',
  },
  content: {
    padding: Theme.spacing.xl,
  },
  headerSection: {
    marginBottom: Theme.spacing.base,
  },
  title: {
    fontSize: Theme.typography.fontSize.lg,
    fontWeight: Theme.typography.fontWeight.bold,
    color: Theme.colors.textPrimary,
    lineHeight: Theme.typography.fontSize.lg * 1.3, // ensure no clipping
    marginBottom: Theme.spacing.sm,
    includeFontPadding: false,
  },
  addressRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  address: {
    fontSize: Theme.typography.fontSize.sm,
    color: Theme.colors.textTertiary,
    marginLeft: Theme.spacing.xs,
    flex: 1,
    lineHeight: Theme.typography.fontSize.sm * 1.4,
    includeFontPadding: false,
  },
  price: {
    fontSize: Theme.typography.fontSize['2xl'],
    fontWeight: Theme.typography.fontWeight.bold,
    color: Theme.colors.primary,
    marginBottom: Theme.spacing.lg,
    lineHeight: Theme.typography.fontSize['2xl'] * 1.25,
    includeFontPadding: false,
  },
  detailsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Theme.spacing.lg,
    paddingVertical: Theme.spacing.md,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: Theme.colors.borderLight,
  },
  detailItem: {
    flex: 1,
    alignItems: 'center',
  },
  detailIconContainer: {
    width: 36,
    height: 36,
    borderRadius: Theme.borderRadius.base,
    backgroundColor: Theme.colors.primaryAlpha,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Theme.spacing.xs,
  },
  detailText: {
    fontSize: Theme.typography.fontSize.lg,
    fontWeight: Theme.typography.fontWeight.semiBold,
    color: Theme.colors.textPrimary,
    marginBottom: 2,
  },
  detailLabel: {
    fontSize: Theme.typography.fontSize.xs,
    color: Theme.colors.textTertiary,
    fontWeight: Theme.typography.fontWeight.medium,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.base,
  },
  applicationsBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Theme.colors.primaryAlpha,
    paddingHorizontal: Theme.spacing.md,
    paddingVertical: Theme.spacing.sm,
    borderRadius: Theme.borderRadius.full,
    borderWidth: 1,
    borderColor: Theme.colors.primary + '20',
  },
  applicationsText: {
    fontSize: Theme.typography.fontSize.sm,
    color: Theme.colors.primary,
    fontWeight: Theme.typography.fontWeight.medium,
    marginLeft: Theme.spacing.xs,
    lineHeight: Theme.typography.fontSize.sm * 1.4,
    includeFontPadding: false,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  primaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Theme.colors.primary,
    paddingHorizontal: Theme.spacing.lg,
    paddingVertical: Theme.spacing.md,
    borderRadius: Theme.borderRadius.full,
    ...Theme.shadows.sm,
  },
  primaryButtonText: {
    color: Theme.colors.textInverse,
    fontSize: Theme.typography.fontSize.sm,
    fontWeight: Theme.typography.fontWeight.semiBold,
    marginLeft: Theme.spacing.xs,
  },
  warningButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Theme.colors.warning,
    paddingHorizontal: Theme.spacing.lg,
    paddingVertical: Theme.spacing.md,
    borderRadius: Theme.borderRadius.full,
    ...Theme.shadows.sm,
  },
  warningButtonText: {
    color: Theme.colors.textInverse,
    fontSize: Theme.typography.fontSize.sm,
    fontWeight: Theme.typography.fontWeight.semiBold,
    marginLeft: Theme.spacing.xs,
  },
  secondaryActions: {
    flexDirection: 'row',
    gap: Theme.spacing.sm,
  },
  iconButton: {
    width: 44,
    height: 44,
    borderRadius: Theme.borderRadius.md,
    backgroundColor: Theme.colors.neutral[50],
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Theme.colors.border,
  },
  deleteIconButton: {
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderColor: 'rgba(239, 68, 68, 0.2)',
  },
  buttonDisabled: {
    opacity: 0.6,
  },
});
