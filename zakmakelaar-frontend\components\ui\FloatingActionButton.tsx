import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  Animated,
  View,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Theme } from '../../constants/Theme';

export interface FloatingActionButtonProps {
  icon?: keyof typeof Ionicons.glyphMap;
  label?: string;
  onPress: () => void;
  position?: {
    bottom: number;
    right: number;
  };
  backgroundColor?: string;
  size?: 'sm' | 'md' | 'lg';
  extended?: boolean;
}

const sizeMap = {
  sm: { size: 48, iconSize: 20 },
  md: { size: 56, iconSize: 24 },
  lg: { size: 64, iconSize: 28 },
};

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  icon = 'add',
  label,
  onPress,
  position = { bottom: 24, right: 24 },
  backgroundColor = Theme.colors.primary,
  size = 'md',
  extended = false,
}) => {
  const scaleValue = React.useRef(new Animated.Value(1)).current;
  const fadeValue = React.useRef(new Animated.Value(0)).current;

  const { size: buttonSize, iconSize } = sizeMap[size];

  React.useEffect(() => {
    Animated.timing(fadeValue, {
      toValue: 1,
      duration: Theme.animation.slow,
      useNativeDriver: true,
    }).start();
  }, []);

  const handlePressIn = () => {
    Animated.spring(scaleValue, {
      toValue: 0.92,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleValue, {
      toValue: 1,
      friction: 3,
      useNativeDriver: true,
    }).start();
  };

  const dynamicStyles = {
    position: 'absolute' as const,
    bottom: position.bottom,
    right: position.right,
    width: extended && label ? undefined : buttonSize,
    height: buttonSize,
    backgroundColor,
    borderRadius: extended && label ? buttonSize / 2 : buttonSize / 2,
    paddingHorizontal: extended && label ? Theme.spacing.lg : 0,
  };

  return (
    <Animated.View
      style={[
        styles.container,
        dynamicStyles,
        {
          opacity: fadeValue,
          transform: [{ scale: scaleValue }],
        },
      ]}
    >
      <TouchableOpacity
        style={styles.touchable}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        <View style={styles.content}>
          <Ionicons name={icon} size={iconSize} color={Theme.colors.textInverse} />
          {extended && label && (
            <Text style={styles.label}>{label}</Text>
          )}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    ...Theme.shadows.lg,
  },
  touchable: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  label: {
    color: Theme.colors.textInverse,
    fontSize: Theme.typography.fontSize.base,
    fontWeight: Theme.typography.fontWeight.semiBold,
    marginLeft: Theme.spacing.sm,
  },
});

export default FloatingActionButton;