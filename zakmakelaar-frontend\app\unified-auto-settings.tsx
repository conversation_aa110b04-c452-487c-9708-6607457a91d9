import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Switch,
  Alert,
  TextInput,
} from "react-native";
import { useRouter } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { useAuthStore } from "../store/authStore";
import { useAIStore } from "../store/aiStore";
import { autoApplicationService } from "../services/autoApplicationService";
import { NumberInput } from "../components/preferences/NumberInput";
import { PrimaryButton } from "../components/PrimaryButton";

const THEME = {
  primary: "#4361ee",
  secondary: "#7209b7",
  light: "#ffffff",
  gray: "#6b7280",
  lightGray: "#f3f4f6",
  success: "#10b981",
  warning: "#f59e0b",
  danger: "#ef4444",
};

export default function UnifiedAutoSettingsScreen() {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { user } = useAuthStore();
  const { autonomousSettings, updateAutonomousSettings } = useAIStore();

  const [settings, setSettings] = useState(autonomousSettings);
  const [personalInfo, setPersonalInfo] = useState({
    fullName:
      user?.firstName && user?.lastName
        ? `${user.firstName} ${user.lastName}`
        : "",
    email: user?.email || "",
    phone: "",
    monthlyIncome: "",
  });
  const [isSaving, setIsSaving] = useState(false);

  const criticalIssues = [
    !personalInfo.fullName && "Full name is required",
    !personalInfo.email && "Email address is required",
    !personalInfo.phone && "Phone number is required",
    settings.enabled &&
      !settings.autoApplyThreshold &&
      "Auto Submit must be enabled",
    settings.maxApplicationsPerDay < 1 && "Daily limit must be at least 1",
    settings.autoApplyMaxPrice < 500 && "Maximum price too low",
  ].filter(Boolean);

  const handleSave = async () => {
    if (criticalIssues.length > 0) {
      Alert.alert(
        "Setup Required",
        "Please resolve all critical issues before saving."
      );
      return;
    }

    setIsSaving(true);
    try {
      await updateAutonomousSettings(settings);
      if (user?._id || user?.id) {
        await autoApplicationService.updateSettings(
          (user._id || user.id) as string,
          {
            personalInfo: {
              fullName: personalInfo.fullName,
              email: personalInfo.email,
              phone: personalInfo.phone,
              monthlyIncome: parseInt(personalInfo.monthlyIncome) || 0,
              dateOfBirth: new Date("1990-01-01"),
              nationality: "Dutch",
              occupation: "Professional",
              employer: "To be specified",
              moveInDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
              leaseDuration: 12,
              emergencyContact: {
                name: "To be specified",
                phone: personalInfo.phone,
                relationship: "Family",
              },
            },
          }
        );
      }
      Alert.alert("Success", "Settings saved successfully!");
    } catch (error) {
      Alert.alert("Error", "Failed to save settings.");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[THEME.primary, THEME.secondary]}
        style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="chevron-back" size={24} color={THEME.light} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Auto Application Setup</Text>
        </View>
      </LinearGradient>

      <ScrollView style={styles.scrollView}>
        {/* Status */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Status</Text>
          <View style={styles.statusRow}>
            <Text style={styles.statusText}>Auto Application</Text>
            <Switch
              value={settings.enabled}
              onValueChange={(enabled) =>
                setSettings((prev) => ({ ...prev, enabled }))
              }
              disabled={criticalIssues.length > 0}
            />
          </View>
          {criticalIssues.length > 0 && (
            <Text style={styles.errorText}>
              {criticalIssues.length} issues must be resolved
            </Text>
          )}
        </View>

        {/* Critical Issues */}
        {criticalIssues.length > 0 && (
          <View style={[styles.card, styles.errorCard]}>
            <Text style={[styles.cardTitle, { color: THEME.danger }]}>
              Setup Required
            </Text>
            {criticalIssues.map((issue, index) => (
              <Text key={index} style={styles.issueText}>
                • {issue}
              </Text>
            ))}
          </View>
        )}

        {/* Personal Information */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Personal Information</Text>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Full Name *</Text>
            <TextInput
              style={styles.input}
              value={personalInfo.fullName}
              onChangeText={(value) =>
                setPersonalInfo((prev) => ({ ...prev, fullName: value }))
              }
              placeholder="Enter your full name"
            />
          </View>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Email *</Text>
            <TextInput
              style={styles.input}
              value={personalInfo.email}
              onChangeText={(value) =>
                setPersonalInfo((prev) => ({ ...prev, email: value }))
              }
              placeholder="<EMAIL>"
              keyboardType="email-address"
            />
          </View>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Phone *</Text>
            <TextInput
              style={styles.input}
              value={personalInfo.phone}
              onChangeText={(value) =>
                setPersonalInfo((prev) => ({ ...prev, phone: value }))
              }
              placeholder="+31 6 12345678"
              keyboardType="phone-pad"
            />
          </View>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Monthly Income (€)</Text>
            <TextInput
              style={styles.input}
              value={personalInfo.monthlyIncome}
              onChangeText={(value) =>
                setPersonalInfo((prev) => ({ ...prev, monthlyIncome: value }))
              }
              placeholder="3000"
              keyboardType="numeric"
            />
          </View>
        </View>

        {/* Application Settings */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Application Settings</Text>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Auto Submit *</Text>
              <Text style={styles.settingDesc}>
                Required for applications to be sent
              </Text>
            </View>
            <Switch
              value={settings.autoApplyThreshold > 0}
              onValueChange={(enabled) =>
                setSettings((prev) => ({
                  ...prev,
                  autoApplyThreshold: enabled ? 75 : 0,
                }))
              }
            />
          </View>
          {settings.autoApplyThreshold > 0 && (
            <View style={styles.settingRow}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>Match Score</Text>
                <Text style={styles.settingDesc}>Minimum match percentage</Text>
              </View>
              <NumberInput
                value={settings.autoApplyThreshold}
                onValueChange={(value) =>
                  setSettings((prev) => ({
                    ...prev,
                    autoApplyThreshold: value,
                  }))
                }
                minimumValue={50}
                maximumValue={95}
                step={5}
                suffix="%"
              />
            </View>
          )}
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Daily Limit *</Text>
              <Text style={styles.settingDesc}>Max applications per day</Text>
            </View>
            <NumberInput
              value={settings.maxApplicationsPerDay}
              onValueChange={(value) =>
                setSettings((prev) => ({
                  ...prev,
                  maxApplicationsPerDay: value,
                }))
              }
              minimumValue={1}
              maximumValue={25}
              step={1}
            />
          </View>
        </View>

        {/* Property Criteria */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Property Criteria</Text>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Max Price *</Text>
              <Text style={styles.settingDesc}>Maximum rent price</Text>
            </View>
            <NumberInput
              value={settings.autoApplyMaxPrice}
              onValueChange={(value) =>
                setSettings((prev) => ({ ...prev, autoApplyMaxPrice: value }))
              }
              minimumValue={500}
              maximumValue={5000}
              step={50}
              prefix="€"
            />
          </View>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Min Size</Text>
              <Text style={styles.settingDesc}>Minimum square meters</Text>
            </View>
            <NumberInput
              value={(settings as any).minSizeM2 || 20}
              onValueChange={(value) =>
                setSettings((prev) => ({ ...prev, minSizeM2: value } as any))
              }
              minimumValue={10}
              maximumValue={200}
              step={5}
              suffix="m²"
            />
          </View>
        </View>

        <View style={styles.saveContainer}>
          <PrimaryButton
            title={isSaving ? "Saving..." : "Save Settings"}
            onPress={handleSave}
            isLoading={isSaving}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: THEME.lightGray },
  header: { paddingBottom: 20 },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 8,
  },
  backButton: {
    marginRight: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: THEME.light,
    justifyContent: "center",
    alignItems: "center",
  },
  headerTitle: { fontSize: 20, fontWeight: "bold", color: THEME.light },
  scrollView: { flex: 1, paddingHorizontal: 20 },
  card: {
    backgroundColor: THEME.light,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    marginTop: 8,
  },
  errorCard: { borderLeftWidth: 4, borderLeftColor: THEME.danger },
  cardTitle: { fontSize: 18, fontWeight: "bold", marginBottom: 16 },
  statusRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  statusText: { fontSize: 16, fontWeight: "600" },
  errorText: { color: THEME.danger, fontSize: 14, marginTop: 8 },
  issueText: { color: THEME.danger, fontSize: 14, marginBottom: 4 },
  inputGroup: { marginBottom: 16 },
  label: { fontSize: 14, fontWeight: "600", marginBottom: 8 },
  input: {
    backgroundColor: THEME.lightGray,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  settingRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: THEME.lightGray,
  },
  settingInfo: { flex: 1, marginRight: 16 },
  settingTitle: { fontSize: 16, fontWeight: "600", marginBottom: 2 },
  settingDesc: { fontSize: 14, color: THEME.gray },
  saveContainer: { marginTop: 20, marginBottom: 40 },
});
