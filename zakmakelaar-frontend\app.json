{"expo": {"name": "zakmakelaar-ai", "slug": "zakmakelaar-ai", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "zakmakelaarai", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.abdelazizelm.zakmakelaarai"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "c70a159e-fdc3-444b-b771-9b8f58f75e92"}}, "owner": "abdelazizelm"}}