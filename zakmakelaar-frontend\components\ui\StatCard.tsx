import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Theme } from '../../constants/Theme';

export interface StatCardProps {
  title: string;
  value: string | number;
  icon?: keyof typeof Ionicons.glyphMap;
  color?: string;
  backgroundColor?: string;
  subtitle?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  onPress?: () => void;
  animated?: boolean;
}

export const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  color = Theme.colors.primary,
  backgroundColor = Theme.colors.surface,
  subtitle,
  trend,
  onPress,
  animated = true,
}) => {
  const animatedValue = React.useRef(new Animated.Value(0)).current;
  const scaleValue = React.useRef(new Animated.Value(1)).current;

  React.useEffect(() => {
    if (animated) {
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: Theme.animation.slow,
        useNativeDriver: true,
      }).start();
    }
  }, [animated, animatedValue]);

  const handlePress = () => {
    if (onPress) {
      // Scale animation on press
      Animated.sequence([
        Animated.timing(scaleValue, {
          toValue: 0.95,
          duration: Theme.animation.fast,
          useNativeDriver: true,
        }),
        Animated.timing(scaleValue, {
          toValue: 1,
          duration: Theme.animation.fast,
          useNativeDriver: true,
        }),
      ]).start();
      
      onPress();
    }
  };

  const CardWrapper = onPress ? TouchableOpacity : View;

  return (
    <CardWrapper
      style={[styles.container, { backgroundColor }]}
      onPress={handlePress}
      activeOpacity={0.8}
    >
      <Animated.View
        style={[
          styles.content,
          {
            opacity: animated ? animatedValue : 1,
            transform: [
              {
                translateY: animated
                  ? animatedValue.interpolate({
                      inputRange: [0, 1],
                      outputRange: [20, 0],
                    })
                  : 0,
              },
              { scale: scaleValue },
            ],
          },
        ]}
      >
        {/* Icon */}
        {icon && (
          <View style={[styles.iconContainer, { backgroundColor: `${color}15` }]}>
            <Ionicons name={icon} size={24} color={color} />
          </View>
        )}

        {/* Main Content */}
        <View style={styles.mainContent}>
          <Text style={[styles.value, { color }]} numberOfLines={1}>
            {value}
          </Text>
          
          <Text style={styles.title} numberOfLines={1}>
            {title}
          </Text>

          {subtitle && (
            <Text style={styles.subtitle} numberOfLines={1}>
              {subtitle}
            </Text>
          )}

          {/* Trend Indicator */}
          {trend && (
            <View style={styles.trendContainer}>
              <Ionicons
                name={trend.isPositive ? 'trending-up' : 'trending-down'}
                size={14}
                color={trend.isPositive ? Theme.colors.success : Theme.colors.error}
              />
              <Text
                style={[
                  styles.trendText,
                  {
                    color: trend.isPositive ? Theme.colors.success : Theme.colors.error,
                  },
                ]}
              >
                {Math.abs(trend.value)}%
              </Text>
            </View>
          )}
        </View>

        {/* Press indicator */}
        {onPress && (
          <View style={styles.pressIndicator}>
            <Ionicons
              name="chevron-forward"
              size={16}
              color={Theme.colors.textTertiary}
            />
          </View>
        )}
      </Animated.View>
    </CardWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: Theme.borderRadius.lg,
    padding: Theme.spacing.base,
    ...Theme.shadows.base,
    minHeight: 100,
  },
  content: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: Theme.borderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Theme.spacing.md,
  },
  mainContent: {
    flex: 1,
    justifyContent: 'center',
  },
  value: {
    fontSize: Theme.typography.fontSize['2xl'],
    fontWeight: Theme.typography.fontWeight.bold,
    lineHeight: Theme.typography.lineHeight.tight,
    marginBottom: Theme.spacing.xs,
  },
  title: {
    fontSize: Theme.typography.fontSize.sm,
    fontWeight: Theme.typography.fontWeight.medium,
    color: Theme.colors.textSecondary,
    lineHeight: Theme.typography.lineHeight.normal,
  },
  subtitle: {
    fontSize: Theme.typography.fontSize.xs,
    color: Theme.colors.textTertiary,
    marginTop: Theme.spacing.xs / 2,
    lineHeight: Theme.typography.lineHeight.normal,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Theme.spacing.xs,
  },
  trendText: {
    fontSize: Theme.typography.fontSize.xs,
    fontWeight: Theme.typography.fontWeight.medium,
    marginLeft: Theme.spacing.xs / 2,
  },
  pressIndicator: {
    marginLeft: Theme.spacing.sm,
    opacity: 0.6,
  },
});

export default StatCard;