#!/bin/bash

# Update Admin Password Script for Linux Production Server
# Updates existing admin user password to meet validation requirements

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Update Admin User Password ===${NC}"

# Function to run Node.js script
run_node_script() {
    local script_name="$1"
    local description="$2"
    
    echo -e "\n${YELLOW}🔄 $description...${NC}"
    
    if [ ! -f "$script_name" ]; then
        echo -e "${RED}❌ $script_name not found${NC}"
        return 1
    fi
    
    local output
    if output=$(node "$script_name" 2>&1); then
        echo -e "${GREEN}✅ $description completed successfully${NC}"
        echo -e "${GRAY}$output${NC}"
        return 0
    else
        echo -e "${RED}❌ $description failed${NC}"
        echo -e "${YELLOW}$output${NC}"
        return 1
    fi
}

# Create the password update script
create_update_script() {
    cat > update-admin-password.js << 'EOF'
const mongoose = require("mongoose");
const User = require("./src/models/User");
const config = require("./src/config/config");

async function updateAdminPassword() {
  try {
    // Auto-detect the correct MongoDB URI (same logic as create-admin.js)
    let mongoURI = config.mongoURI;
    
    const isProduction = process.env.NODE_ENV === 'production';
    
    console.log('🔍 Detecting MongoDB configuration...');
    
    if (process.env.MONGO_URI_AUTH) {
      mongoURI = process.env.MONGO_URI_AUTH;
      console.log('🔐 Using MONGO_URI_AUTH from environment');
    }
    else if (isProduction && mongoURI.includes('localhost:27017')) {
      mongoURI = '************************************************************************';
      console.log('🐳 Using Docker MongoDB URI with authentication');
    }
    else if (process.env.MONGO_USER || process.env.MONGO_PASSWORD) {
      const dbUser = process.env.MONGO_USER || 'admin';
      const dbPass = process.env.MONGO_PASSWORD || 'password123';
      const dbHost = process.env.MONGO_HOST || 'localhost';
      const dbPort = process.env.MONGO_PORT || '27017';
      const dbName = process.env.MONGO_DB_NAME || 'zakmakelaar';
      const authSource = process.env.MONGO_AUTH_SOURCE || 'admin';
      
      mongoURI = `mongodb://${dbUser}:${dbPass}@${dbHost}:${dbPort}/${dbName}?authSource=${authSource}`;
      console.log('🔧 Constructed authenticated MongoDB URI from individual variables');
    }
    else {
      console.log('📍 Using default MongoDB URI from config');
    }
    
    const maskedURI = mongoURI.replace(/\/\/([^:]+:[^@]+)@/, "//***:***@");
    console.log(`Attempting to connect to MongoDB: ${maskedURI}`);
    
    const connectionOptions = {
      serverSelectionTimeoutMS: 10000,
      connectTimeoutMS: 10000,
      socketTimeoutMS: 30000,
    };
    
    // Try connecting with retry logic
    let connectionSuccessful = false;
    let lastError = null;
    
    const uriOptions = [
      mongoURI,
      '************************************************************************',
      'mongodb://localhost:27017/zakmakelaar',
    ];
    
    const uniqueUriOptions = [...new Set(uriOptions)];
    
    for (let i = 0; i < uniqueUriOptions.length && !connectionSuccessful; i++) {
      const tryURI = uniqueUriOptions[i];
      const maskedTryURI = tryURI.replace(/\/\/([^:]+:[^@]+)@/, "//***:***@");
      
      try {
        console.log(`\n🔗 Attempt ${i + 1}/${uniqueUriOptions.length}: ${maskedTryURI}`);
        await mongoose.connect(tryURI, connectionOptions);
        connectionSuccessful = true;
        console.log("✅ Connected to MongoDB successfully");
        break;
      } catch (connectError) {
        lastError = connectError;
        console.log(`❌ Attempt ${i + 1} failed: ${connectError.message}`);
        
        if (mongoose.connection.readyState !== 0) {
          try {
            await mongoose.connection.close();
          } catch (closeError) {
            // Ignore close errors
          }
        }
      }
    }
    
    if (!connectionSuccessful) {
      throw lastError || new Error('Failed to connect with all URI options');
    }

    // Find existing admin user
    console.log("🔍 Looking for existing admin user...");
    const adminUser = await User.findOne({
      email: "<EMAIL>",
    });

    if (!adminUser) {
      console.log("❌ Admin user not found. Please run create-admin.js first.");
      await mongoose.connection.close();
      process.exit(1);
    }

    console.log("📋 Found existing admin user:");
    console.log(`   Email: ${adminUser.email}`);
    console.log(`   Role: ${adminUser.role}`);
    console.log(`   ID: ${adminUser._id}`);
    console.log(`   Created: ${adminUser.createdAt}`);

    // Update password - the pre-save middleware will hash it
    console.log("\n🔐 Updating admin password...");
    adminUser.password = "Admin123!"; // Strong password that meets validation requirements
    
    await adminUser.save();
    
    console.log("✅ Admin password updated successfully:");
    console.log(`   Email: ${adminUser.email}`);
    console.log(`   New Password: Admin123!`);
    console.log(`   Role: ${adminUser.role}`);
    console.log(`   Updated: ${new Date()}`);

    await mongoose.connection.close();
    console.log("✅ Database connection closed");
    
  } catch (error) {
    console.error("❌ Error updating admin password:");
    console.error(`   Error Type: ${error.constructor.name}`);
    console.error(`   Error Code: ${error.code || 'N/A'}`);
    console.error(`   Error Message: ${error.message}`);
    
    if (mongoose.connection.readyState !== 0) {
      try {
        await mongoose.connection.close();
        console.log("Database connection closed");
      } catch (closeError) {
        console.error("Error closing database connection:", closeError.message);
      }
    }
    
    process.exit(1);
  }
}

updateAdminPassword();
EOF

    echo -e "${GREEN}✅ Created update-admin-password.js script${NC}"
}

# Main execution
echo -e "This script will update the existing admin user password to meet validation requirements.\n"

# Create the update script
create_update_script

# Run the update
if run_node_script "update-admin-password.js" "Updating admin password"; then
    echo -e "\n${GREEN}🎉 Admin password updated successfully!${NC}"
    echo -e "\n${CYAN}📋 Updated Admin Credentials:${NC}"
    echo -e "   ${NC}Email: <EMAIL>${NC}"
    echo -e "   ${NC}Password: Admin123!${NC}"
    echo -e "   ${NC}Role: admin${NC}"
    
    # Offer to test the login
    echo -e "\n${YELLOW}🧪 Would you like to test the admin login? (y/n)${NC}"
    read -r test_login
    
    if [[ "$test_login" =~ ^[Yy]$ ]]; then
        if [ -f "test-admin-login.sh" ]; then
            chmod +x test-admin-login.sh
            ./test-admin-login.sh
        else
            echo -e "${YELLOW}⚠️ test-admin-login.sh not found. You can test manually at: http://localhost:3000${NC}"
        fi
    fi
    
    # Clean up the temporary script
    echo -e "\n${YELLOW}🧹 Cleaning up temporary files...${NC}"
    rm -f update-admin-password.js
    echo -e "${GREEN}✅ Cleanup complete${NC}"
    
else
    echo -e "\n${RED}❌ Failed to update admin password${NC}"
    echo -e "\n${CYAN}💡 Troubleshooting:${NC}"
    echo -e "   1. Check MongoDB connection: node test-mongo-connection.js"
    echo -e "   2. Verify admin user exists in database"
    echo -e "   3. Check MongoDB credentials in .env file"
    
    # Clean up the temporary script
    rm -f update-admin-password.js
    exit 1
fi