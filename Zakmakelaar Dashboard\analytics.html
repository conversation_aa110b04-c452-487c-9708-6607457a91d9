<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics - ZakMakelaar Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.4.3/echarts.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pixi.js/7.3.2/pixi.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Inter', sans-serif;
        }
        .hero-title {
            font-family: 'Playfair Display', serif;
        }
        .metric-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .metric-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.15);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-healthy { background-color: #10b981; }
        .status-warning { background-color: #f59e0b; }
        .status-error { background-color: #ef4444; }
        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }
        .alert-item {
            transition: all 0.2s ease;
        }
        .alert-item:hover {
            transform: translateX(4px);
        }
        .progress-bar {
            transition: width 0.5s ease;
        }
        .network-node {
            transition: all 0.3s ease;
        }
        .network-node:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-gray-900 hero-title">ZakMakelaar</h1>
                    </div>
                </div>
                <div class="flex items-center space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-blue-600 transition-colors">Dashboard</a>
                    <a href="properties.html" class="text-gray-700 hover:text-blue-600 transition-colors">Properties</a>
                    <a href="analytics.html" class="text-blue-600 font-medium border-b-2 border-blue-600 pb-1">Analytics</a>
                    <div class="flex items-center space-x-4">
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-medium">JD</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <section class="bg-white py-8 border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 hero-title">System Analytics</h1>
                    <p class="text-gray-600 mt-2">Real-time monitoring and performance insights</p>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        System Healthy
                    </button>
                    <button class="border border-gray-300 hover:bg-gray-50 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors">
                        Export Report
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- System Health Overview -->
    <section class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                <div class="metric-card rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <p class="text-gray-600 text-sm font-medium">System Uptime</p>
                            <p class="text-3xl font-bold text-gray-900">99.8%</p>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <span class="status-indicator status-healthy"></span>
                        </div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-600 h-2 rounded-full progress-bar" style="width: 99.8%"></div>
                    </div>
                </div>

                <div class="metric-card rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <p class="text-gray-600 text-sm font-medium">API Response Time</p>
                            <p class="text-3xl font-bold text-gray-900">142ms</p>
                        </div>
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <span class="status-indicator status-warning"></span>
                        </div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-yellow-600 h-2 rounded-full progress-bar" style="width: 75%"></div>
                    </div>
                </div>

                <div class="metric-card rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <p class="text-gray-600 text-sm font-medium">Active Connections</p>
                            <p class="text-3xl font-bold text-gray-900">1,247</p>
                        </div>
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="text-green-600 text-sm font-medium">+12% from yesterday</div>
                </div>

                <div class="metric-card rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <p class="text-gray-600 text-sm font-medium">Error Rate</p>
                            <p class="text-3xl font-bold text-gray-900">0.02%</p>
                        </div>
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                            <span class="status-indicator status-error"></span>
                        </div>
                    </div>
                    <div class="text-green-600 text-sm font-medium">-5% from yesterday</div>
                </div>
            </div>

            <!-- Network Architecture -->
            <div class="bg-white rounded-xl shadow-lg p-8 mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">System Architecture</h2>
                <div id="network-diagram" class="relative h-96 bg-gray-50 rounded-lg overflow-hidden">
                    <!-- Network nodes will be dynamically generated -->
                    <svg width="100%" height="100%" class="absolute inset-0">
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
                            </marker>
                        </defs>
                        <!-- Connection lines will be dynamically generated -->
                    </svg>
                </div>
            </div>

            <!-- Performance Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
                <div class="chart-container rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">API Performance</h3>
                    <div id="api-performance-chart" style="height: 300px;"></div>
                </div>
                <div class="chart-container rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Database Queries</h3>
                    <div id="database-queries-chart" style="height: 300px;"></div>
                </div>
            </div>

            <!-- Detailed Analytics -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
                <div class="lg:col-span-2">
                    <div class="chart-container rounded-xl shadow-lg p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">User Activity Trends</h3>
                        <div id="user-activity-chart" style="height: 350px;"></div>
                    </div>
                </div>
                <div class="space-y-6">
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Pages</h3>
                        <div class="space-y-4" id="top-pages">
                            <!-- Top pages will be dynamically generated -->
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Traffic Sources</h3>
                        <div id="traffic-sources-chart" style="height: 200px;"></div>
                    </div>
                </div>
            </div>

            <!-- System Alerts and Logs -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-semibold text-gray-900">System Alerts</h3>
                        <button class="text-blue-600 hover:text-blue-700 font-medium">View All</button>
                    </div>
                    <div class="space-y-4" id="system-alerts">
                        <!-- System alerts will be dynamically generated -->
                    </div>
                </div>

                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-semibold text-gray-900">Recent Logs</h3>
                        <button class="text-blue-600 hover:text-blue-700 font-medium">View All</button>
                    </div>
                    <div class="space-y-3" id="recent-logs">
                        <!-- Recent logs will be dynamically generated -->
                    </div>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="mt-12 bg-white rounded-xl shadow-lg p-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Performance Metrics</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="text-center p-6 bg-gray-50 rounded-lg">
                        <div class="text-3xl font-bold text-blue-600 mb-2">2.3s</div>
                        <div class="text-gray-600">Average Load Time</div>
                        <div class="text-green-600 text-sm mt-2">-15% improvement</div>
                    </div>
                    <div class="text-center p-6 bg-gray-50 rounded-lg">
                        <div class="text-3xl font-bold text-green-600 mb-2">98.5%</div>
                        <div class="text-gray-600">Success Rate</div>
                        <div class="text-green-600 text-sm mt-2">+2.1% improvement</div>
                    </div>
                    <div class="text-center p-6 bg-gray-50 rounded-lg">
                        <div class="text-3xl font-bold text-purple-600 mb-2">847</div>
                        <div class="text-gray-600">Concurrent Users</div>
                        <div class="text-blue-600 text-sm mt-2">Peak: 1,203</div>
                    </div>
                    <div class="text-center p-6 bg-gray-50 rounded-lg">
                        <div class="text-3xl font-bold text-orange-600 mb-2">45GB</div>
                        <div class="text-gray-600">Data Processed</div>
                        <div class="text-green-600 text-sm mt-2">+23% today</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h3 class="text-2xl font-bold hero-title mb-4">ZakMakelaar</h3>
                <p class="text-gray-400 mb-6">Advanced real estate analytics and property management</p>
                <p class="text-gray-500 text-sm">© 2024 ZakMakelaar. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="main.js"></script>
    <script>
        // Analytics page specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            initializeAnalyticsPage();
        });

        function initializeAnalyticsPage() {
            initializeNetworkDiagram();
            initializeCharts();
            initializeTopPages();
            initializeSystemAlerts();
            initializeRecentLogs();
            animateMetrics();
        }

        function initializeNetworkDiagram() {
            const container = document.getElementById('network-diagram');
            const svg = container.querySelector('svg');
            
            const nodes = [
                { id: 'load-balancer', x: 50, y: 50, name: 'Load Balancer', status: 'healthy' },
                { id: 'api-gateway', x: 200, y: 50, name: 'API Gateway', status: 'healthy' },
                { id: 'web-server-1', x: 350, y: 20, name: 'Web Server 1', status: 'healthy' },
                { id: 'web-server-2', x: 350, y: 80, name: 'Web Server 2', status: 'warning' },
                { id: 'database-1', x: 500, y: 30, name: 'Primary DB', status: 'healthy' },
                { id: 'database-2', x: 500, y: 90, name: 'Replica DB', status: 'healthy' },
                { id: 'cache', x: 650, y: 60, name: 'Cache Layer', status: 'healthy' }
            ];

            const connections = [
                { from: 'load-balancer', to: 'api-gateway' },
                { from: 'api-gateway', to: 'web-server-1' },
                { from: 'api-gateway', to: 'web-server-2' },
                { from: 'web-server-1', to: 'database-1' },
                { from: 'web-server-2', to: 'database-2' },
                { from: 'database-1', to: 'cache' },
                { from: 'database-2', to: 'cache' }
            ];

            // Draw connections
            connections.forEach(conn => {
                const fromNode = nodes.find(n => n.id === conn.from);
                const toNode = nodes.find(n => n.id === conn.to);
                
                const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                line.setAttribute('x1', fromNode.x + 40);
                line.setAttribute('y1', fromNode.y + 20);
                line.setAttribute('x2', toNode.x);
                line.setAttribute('y2', toNode.y + 20);
                line.setAttribute('stroke', '#6b7280');
                line.setAttribute('stroke-width', '2');
                line.setAttribute('marker-end', 'url(#arrowhead)');
                svg.appendChild(line);
            });

            // Create nodes
            nodes.forEach(node => {
                const nodeElement = document.createElement('div');
                nodeElement.className = `network-node absolute w-20 h-12 rounded-lg flex items-center justify-center text-xs font-medium cursor-pointer ${
                    node.status === 'healthy' ? 'bg-green-100 text-green-800 border-2 border-green-300' :
                    node.status === 'warning' ? 'bg-yellow-100 text-yellow-800 border-2 border-yellow-300' :
                    'bg-red-100 text-red-800 border-2 border-red-300'
                }`;
                nodeElement.style.left = node.x + 'px';
                nodeElement.style.top = node.y + 'px';
                nodeElement.textContent = node.name;
                nodeElement.title = `${node.name} - Status: ${node.status}`;
                
                nodeElement.addEventListener('click', function() {
                    showNodeDetails(node);
                });
                
                container.appendChild(nodeElement);
            });
        }

        function showNodeDetails(node) {
            alert(`${node.name}\nStatus: ${node.status}\nClick for detailed metrics`);
        }

        function initializeCharts() {
            initializeApiPerformanceChart();
            initializeDatabaseQueriesChart();
            initializeUserActivityChart();
            initializeTrafficSourcesChart();
        }

        function initializeApiPerformanceChart() {
            const chartDom = document.getElementById('api-performance-chart');
            const myChart = echarts.init(chartDom);
            
            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { type: 'cross' }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                    axisLine: { lineStyle: { color: '#e2e8f0' } },
                    axisLabel: { color: '#64748b' }
                },
                yAxis: {
                    type: 'value',
                    name: 'Response Time (ms)',
                    axisLine: { lineStyle: { color: '#e2e8f0' } },
                    axisLabel: { color: '#64748b' },
                    splitLine: { lineStyle: { color: '#f1f5f9' } }
                },
                series: [{
                    name: 'Response Time',
                    type: 'line',
                    smooth: true,
                    data: [120, 132, 101, 134, 90, 230, 210],
                    lineStyle: { color: '#4a90e2', width: 3 },
                    itemStyle: { color: '#4a90e2' },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                { offset: 0, color: 'rgba(74, 144, 226, 0.3)' },
                                { offset: 1, color: 'rgba(74, 144, 226, 0.05)' }
                            ]
                        }
                    }
                }]
            };
            
            myChart.setOption(option);
        }

        function initializeDatabaseQueriesChart() {
            const chartDom = document.getElementById('database-queries-chart');
            const myChart = echarts.init(chartDom);
            
            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { type: 'shadow' }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['SELECT', 'INSERT', 'UPDATE', 'DELETE'],
                    axisLine: { lineStyle: { color: '#e2e8f0' } },
                    axisLabel: { color: '#64748b' }
                },
                yAxis: {
                    type: 'value',
                    name: 'Queries/sec',
                    axisLine: { lineStyle: { color: '#e2e8f0' } },
                    axisLabel: { color: '#64748b' },
                    splitLine: { lineStyle: { color: '#f1f5f9' } }
                },
                series: [{
                    name: 'Query Rate',
                    type: 'bar',
                    data: [450, 120, 89, 34],
                    itemStyle: {
                        color: '#5a9bd4',
                        borderRadius: [4, 4, 0, 0]
                    }
                }]
            };
            
            myChart.setOption(option);
        }

        function initializeUserActivityChart() {
            const chartDom = document.getElementById('user-activity-chart');
            const myChart = echarts.init(chartDom);
            
            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { type: 'cross' }
                },
                legend: {
                    data: ['Page Views', 'Unique Visitors', 'Sessions'],
                    textStyle: { color: '#64748b' }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    axisLine: { lineStyle: { color: '#e2e8f0' } },
                    axisLabel: { color: '#64748b' }
                },
                yAxis: {
                    type: 'value',
                    axisLine: { lineStyle: { color: '#e2e8f0' } },
                    axisLabel: { color: '#64748b' },
                    splitLine: { lineStyle: { color: '#f1f5f9' } }
                },
                series: [
                    {
                        name: 'Page Views',
                        type: 'line',
                        data: [820, 932, 901, 934, 1290, 1330, 1320],
                        lineStyle: { color: '#4a90e2' },
                        itemStyle: { color: '#4a90e2' }
                    },
                    {
                        name: 'Unique Visitors',
                        type: 'line',
                        data: [220, 182, 191, 234, 290, 330, 310],
                        lineStyle: { color: '#5a9bd4' },
                        itemStyle: { color: '#5a9bd4' }
                    },
                    {
                        name: 'Sessions',
                        type: 'line',
                        data: [150, 232, 201, 154, 190, 330, 410],
                        lineStyle: { color: '#f4a261' },
                        itemStyle: { color: '#f4a261' }
                    }
                ]
            };
            
            myChart.setOption(option);
        }

        function initializeTrafficSourcesChart() {
            const chartDom = document.getElementById('traffic-sources-chart');
            const myChart = echarts.init(chartDom);
            
            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}: {c}% ({d}%)'
                },
                series: [{
                    type: 'pie',
                    radius: ['40%', '70%'],
                    center: ['50%', '50%'],
                    data: [
                        { value: 40, name: 'Direct', itemStyle: { color: '#4a90e2' } },
                        { value: 30, name: 'Search', itemStyle: { color: '#5a9bd4' } },
                        { value: 20, name: 'Social', itemStyle: { color: '#f4a261' } },
                        { value: 10, name: 'Referral', itemStyle: { color: '#e76f51' } }
                    ],
                    itemStyle: {
                        borderRadius: 4,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: { show: false }
                }]
            };
            
            myChart.setOption(option);
        }

        function initializeTopPages() {
            const container = document.getElementById('top-pages');
            const pages = [
                { name: '/dashboard', views: 12543, change: '+12%' },
                { name: '/properties', views: 8932, change: '+8%' },
                { name: '/analytics', views: 5643, change: '+15%' },
                { name: '/profile', views: 3421, change: '-3%' },
                { name: '/settings', views: 2156, change: '+5%' }
            ];

            container.innerHTML = '';
            pages.forEach(page => {
                const item = document.createElement('div');
                item.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg';
                item.innerHTML = `
                    <div>
                        <div class="font-medium text-gray-900">${page.name}</div>
                        <div class="text-sm text-gray-600">${page.views.toLocaleString()} views</div>
                    </div>
                    <div class="text-sm font-medium ${page.change.startsWith('+') ? 'text-green-600' : 'text-red-600'}">
                        ${page.change}
                    </div>
                `;
                container.appendChild(item);
            });
        }

        function initializeSystemAlerts() {
            const container = document.getElementById('system-alerts');
            const alerts = [
                { type: 'warning', message: 'High memory usage detected on Web Server 2', time: '5 minutes ago' },
                { type: 'success', message: 'Database backup completed successfully', time: '15 minutes ago' },
                { type: 'info', message: 'New deployment scheduled for tonight', time: '1 hour ago' },
                { type: 'error', message: 'API rate limit exceeded for client XYZ', time: '2 hours ago' }
            ];

            container.innerHTML = '';
            alerts.forEach(alert => {
                const item = document.createElement('div');
                item.className = 'alert-item flex items-center p-4 rounded-lg cursor-pointer';
                
                const typeColors = {
                    success: 'bg-green-50 border-l-4 border-green-400',
                    warning: 'bg-yellow-50 border-l-4 border-yellow-400',
                    error: 'bg-red-50 border-l-4 border-red-400',
                    info: 'bg-blue-50 border-l-4 border-blue-400'
                };

                const iconColors = {
                    success: 'text-green-600',
                    warning: 'text-yellow-600',
                    error: 'text-red-600',
                    info: 'text-blue-600'
                };

                item.className += ` ${typeColors[alert.type]}`;
                
                item.innerHTML = `
                    <div class="w-6 h-6 rounded-full flex items-center justify-center mr-3 ${iconColors[alert.type]} bg-current bg-opacity-20">
                        ${getAlertIcon(alert.type)}
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">${alert.message}</p>
                        <p class="text-xs text-gray-500">${alert.time}</p>
                    </div>
                `;

                container.appendChild(item);
            });
        }

        function initializeRecentLogs() {
            const container = document.getElementById('recent-logs');
            const logs = [
                { level: 'INFO', message: 'User authentication successful', timestamp: '14:32:15' },
                { level: 'WARN', message: 'Slow query detected: SELECT * FROM properties', timestamp: '14:31:22' },
                { level: 'ERROR', message: 'Failed to connect to cache server', timestamp: '14:30:45' },
                { level: 'INFO', message: 'Property index updated successfully', timestamp: '14:29:33' },
                { level: 'DEBUG', message: 'Cache hit ratio: 94.2%', timestamp: '14:28:12' }
            ];

            container.innerHTML = '';
            logs.forEach(log => {
                const item = document.createElement('div');
                item.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg text-sm';
                
                const levelColors = {
                    'INFO': 'text-blue-600',
                    'WARN': 'text-yellow-600',
                    'ERROR': 'text-red-600',
                    'DEBUG': 'text-gray-600'
                };
                
                item.innerHTML = `
                    <div class="flex items-center">
                        <span class="font-medium ${levelColors[log.level]} mr-3">[${log.level}]</span>
                        <span class="text-gray-900">${log.message}</span>
                    </div>
                    <span class="text-gray-500">${log.timestamp}</span>
                `;
                container.appendChild(item);
            });
        }

        function animateMetrics() {
            // Animate progress bars
            const progressBars = document.querySelectorAll('.progress-bar');
            progressBars.forEach((bar, index) => {
                anime({
                    targets: bar,
                    width: bar.style.width,
                    duration: 1000,
                    delay: index * 200,
                    easing: 'easeOutQuart'
                });
            });

            // Animate metric cards
            const metricCards = document.querySelectorAll('.metric-card');
            metricCards.forEach((card, index) => {
                anime({
                    targets: card,
                    opacity: [0, 1],
                    translateY: [30, 0],
                    delay: index * 100,
                    duration: 600,
                    easing: 'easeOutQuart'
                });
            });

            // Animate network nodes
            const networkNodes = document.querySelectorAll('.network-node');
            networkNodes.forEach((node, index) => {
                anime({
                    targets: node,
                    opacity: [0, 1],
                    scale: [0.8, 1],
                    delay: index * 150,
                    duration: 500,
                    easing: 'easeOutQuart'
                });
            });
        }

        function getAlertIcon(type) {
            const icons = {
                success: '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>',
                warning: '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
                error: '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
                info: '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
            };
            return icons[type] || icons.info;
        }
    </script>
</body>
</html>