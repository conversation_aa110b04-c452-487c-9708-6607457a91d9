const cheerio = require("cheerio");
const Listing = require("../../models/Listing");
const { sendAlerts } = require("../alertService");
const { loggers } = require("../logger");
const {
  browserPool,
  validateAndNormalizeListing,
  setupPageStealth,
  autoScroll,
  scrapingMetrics,
  isRetryableError,
} = require("../scraperUtils");
const {
  validateAndNormalizeListingEnhanced,
} = require("../transformationIntegration");
const scraperAutoApplicationIntegration = require("../scraperAutoApplicationIntegration");

// Enhanced stealth setup for Funda specifically
const setupFundaStealth = async (page) => {
  // Basic stealth setup
  await setupPageStealth(page);

  // Additional Funda-specific stealth measures
  await page.evaluateOnNewDocument(() => {
    // Override chrome detection
    Object.defineProperty(navigator, "webdriver", { get: () => undefined });
    Object.defineProperty(navigator, "plugins", { get: () => [1, 2, 3, 4, 5] });
    Object.defineProperty(navigator, "languages", {
      get: () => ["en-US", "en", "nl"],
    });

    // Mock realistic screen properties
    Object.defineProperty(screen, "colorDepth", { get: () => 24 });
    Object.defineProperty(screen, "pixelDepth", { get: () => 24 });

    // Remove automation indicators
    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
  });

  // Set additional realistic headers
  await page.setExtraHTTPHeaders({
    "Accept-Language": "nl-NL,nl;q=0.9,en;q=0.8",
    "Accept-Encoding": "gzip, deflate, br",
    Accept:
      "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
    "Cache-Control": "max-age=0",
    "Sec-Ch-Ua":
      '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
    "Sec-Ch-Ua-Mobile": "?0",
    "Sec-Ch-Ua-Platform": '"Windows"',
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "none",
    "Sec-Fetch-User": "?1",
    "Upgrade-Insecure-Requests": "1",
  });
};

// Helper function to detect if page is showing verification/security check
const isVerificationPage = (html) => {
  const verificationIndicators = [
    "Je bent bijna op de pagina die je zoekt",
    "We houden ons platform graag veilig",
    "verifiëren dat onze bezoekers echte mensen zijn",
    "<EMAIL>",
    "security check",
    "verification",
  ];

  return verificationIndicators.some((indicator) =>
    html.toLowerCase().includes(indicator.toLowerCase())
  );
};

// Helper function to fetch detailed listing information
const fetchListingDetails = async (browser, url, retryCount = 0) => {
  const maxRetries = 3;
  let detailPage = null;

  try {
    detailPage = await browser.newPage();
    await setupFundaStealth(detailPage);

    // Add longer random delay before navigation
    const delay = 3000 + Math.random() * 4000; // 3-7 seconds
    loggers.scraper.debug("Applying delay before navigation", {
      delayMs: Math.round(delay),
      url,
    });
    await new Promise((r) => setTimeout(r, delay));

    await detailPage.goto(url, {
      waitUntil: "networkidle2",
      timeout: 45000, // Increased timeout
    });

    // Wait for content to load with longer delay
    await new Promise((r) => setTimeout(r, 4000));

    const detailHtml = await detailPage.content();

    // Check if we hit a verification page
    if (isVerificationPage(detailHtml)) {
      loggers.scraper.warn("Verification page detected", { url, retryCount });

      if (retryCount < maxRetries) {
        loggers.scraper.info("Retrying after verification page", {
          retryCount: retryCount + 1,
          maxRetries,
          url,
        });
        await detailPage.close();
        await new Promise((r) => setTimeout(r, 10000)); // Wait 10 seconds
        return fetchListingDetails(browser, url, retryCount + 1);
      } else {
        loggers.scraper.error("Max retries exceeded for verification page", {
          url,
          maxRetries,
        });
        return null;
      }
    }

    const $ = cheerio.load(detailHtml);

    // Save HTML for debugging (only for first listing to avoid too many files)
    const urlId = url.split("/").pop() || "unknown";
    if (Math.random() < 0.1) {
      // Save 10% of detail pages for debugging
      require("fs").writeFileSync(`funda_detail_${urlId}.html`, detailHtml);
      loggers.scraper.debug("Debug HTML saved", {
        filename: `funda_detail_${urlId}.html`,
        url,
      });
    }

    // Initialize all possible fields
    let price = "Prijs op aanvraag";
    let size = null;
    let bedrooms = null;
    let rooms = null;
    let description = null;
    let year = null;
    let interior = null;
    let propertyType = "woning";
    let energyLabel = null;
    let availableFrom = null;
    let constructionPeriod = null;
    let garden = null;
    let balcony = null;
    let parking = null;
    let heating = null;
    let isolation = null;
    let images = [];

    // Extract price from multiple possible locations
    const priceSelectors = [
      'span[class*="object-header__price"]',
      ".object-header__price",
      '[data-test-id="price-rent"]',
      ".object-price",
      'span:contains("€")',
    ];

    for (const selector of priceSelectors) {
      const priceEl = $(selector).first();
      if (priceEl.length && priceEl.text().includes("€")) {
        price = priceEl.text().trim();
        loggers.scraper.debug("Price extracted", { selector, price, url });
        break;
      }
    }

    // Extract description from multiple possible locations
    const descriptionSelectors = [
      '[data-test-id="object-description-body"]',
      ".object-description-body",
      ".object-description",
      ".description",
      '[class*="description"]',
    ];

    for (const selector of descriptionSelectors) {
      const descEl = $(selector).first();
      if (descEl.length && descEl.text().trim().length > 50) {
        description = descEl.text().trim();
        loggers.scraper.debug("Description extracted", {
          selector,
          descriptionLength: description.length,
          url,
        });
        break;
      }
    }

    // Extract property details from multiple possible sections
    loggers.scraper.debug("Starting detail extraction", { url });

    // Method 1: Try kenmerken (characteristics) section
    $("dl.object-kenmerken-list dt, dl.object-kenmerken-list dd").each(
      (index, element) => {
        const text = $(element).text().trim().toLowerCase();
        const nextElement = $(element).next();
        const nextText = nextElement.text().trim();

        if (text.includes("woonoppervlakte") || text.includes("oppervlakte")) {
          const match = nextText.match(/(\d+)\s*m²/);
          if (match) {
            size = parseInt(match[1]);
            loggers.scraper.debug("Size extracted", { size, url });
          }
        } else if (text.includes("slaapkamer")) {
          const match = nextText.match(/(\d+)/);
          if (match) {
            bedrooms = parseInt(match[1]);
            loggers.scraper.debug("Bedrooms extracted", { bedrooms, url });
          }
        } else if (text.includes("kamer") && !text.includes("slaapkamer")) {
          const match = nextText.match(/(\d+)/);
          if (match) {
            rooms = parseInt(match[1]);
            loggers.scraper.debug("Rooms extracted", { rooms, url });
          }
        } else if (text.includes("bouwjaar") || text.includes("jaar")) {
          const match = nextText.match(/(\d{4})/);
          if (match) {
            year = parseInt(match[1]);
            loggers.scraper.debug("Year extracted", { year, url });
          }
        } else if (
          text.includes("interieur") ||
          text.includes("gemeubileerd")
        ) {
          interior = nextText;
          loggers.scraper.debug("Interior extracted", { interior, url });
        } else if (text.includes("energielabel")) {
          energyLabel = nextText;
        } else if (text.includes("beschikbaar")) {
          availableFrom = nextText;
        } else if (text.includes("tuin")) {
          garden = nextText;
        } else if (text.includes("balkon")) {
          balcony = nextText;
        } else if (text.includes("parkeer")) {
          parking = nextText;
        } else if (text.includes("verwarming")) {
          heating = nextText;
        } else if (text.includes("isolatie")) {
          isolation = nextText;
        }
      }
    );

    // Method 2: Try alternative selectors for missing fields
    if (!size) {
      const sizeSelectors = [
        '[data-test-id*="surface"]',
        '.object-kenmerken-highlighted [class*="surface"]',
        '.object-kenmerken-highlighted span:contains("m²")',
        '.kenmerken-highlighted span:contains("m²")',
        'span:contains("m²")',
        'dt:contains("Woonoppervlakte") + dd',
        'dt:contains("Oppervlakte") + dd',
      ];

      for (const selector of sizeSelectors) {
        const sizeEl = $(selector).first();
        if (sizeEl.length) {
          const sizeText = sizeEl.text().trim();
          const match = sizeText.match(/(\d+)\s*m²/);
          if (match) {
            size = parseInt(match[1]);
            loggers.scraper.debug("Size extracted", { selector, size, url });
            break;
          }
        }
      }
    }

    if (!bedrooms) {
      const bedroomSelectors = [
        'dt:contains("slaapkamer") + dd',
        'dt:contains("Aantal slaapkamers") + dd',
        '.kenmerken-highlighted span:contains("slaapkamer")',
        '[class*="bedroom"]',
        'span:contains("slaapkamer")',
      ];

      for (const selector of bedroomSelectors) {
        const bedroomEl = $(selector).first();
        if (bedroomEl.length) {
          const bedroomText = bedroomEl.text().trim();
          const match = bedroomText.match(/(\d+)/);
          if (match) {
            bedrooms = parseInt(match[1]);
            loggers.scraper.debug("Bedrooms extracted", {
              selector,
              bedrooms,
              url,
            });
            break;
          }
        }
      }
    }

    if (!rooms) {
      const roomSelectors = [
        'dt:contains("kamer") + dd',
        'dt:contains("Aantal kamers") + dd',
        '.kenmerken-highlighted span:contains("kamer")',
        '[class*="room"]',
        'span:contains("kamer")',
      ];

      for (const selector of roomSelectors) {
        const roomEl = $(selector).first();
        if (roomEl.length) {
          const roomText = roomEl.text().trim();
          const match = roomText.match(/(\d+)/);
          if (match && !roomText.toLowerCase().includes("slaapkamer")) {
            rooms = parseInt(match[1]);
            loggers.scraper.debug("Rooms extracted", { selector, rooms, url });
            break;
          }
        }
      }
    }

    if (!year) {
      const yearSelectors = [
        'dt:contains("Bouwjaar") + dd',
        'dt:contains("bouwjaar") + dd',
        '.kenmerken-highlighted span:contains("19")',
        '.kenmerken-highlighted span:contains("20")',
        'span:contains("Bouwjaar")',
        '[class*="year"]',
      ];

      for (const selector of yearSelectors) {
        const yearEl = $(selector).first();
        if (yearEl.length) {
          const yearText = yearEl.text().trim();
          const match = yearText.match(/(\d{4})/);
          if (match) {
            year = parseInt(match[1]);
            loggers.scraper.debug("Year extracted", { selector, year, url });
            break;
          }
        }
      }
    }

    if (!interior) {
      const interiorSelectors = [
        'dt:contains("Interieur") + dd',
        'dt:contains("interieur") + dd',
        'dt:contains("Gemeubileerd") + dd',
        'dt:contains("gemeubileerd") + dd',
        '.kenmerken-highlighted span:contains("gemeubileerd")',
        '.kenmerken-highlighted span:contains("gestoffeerd")',
        '.kenmerken-highlighted span:contains("kaal")',
        'span:contains("gemeubileerd")',
        'span:contains("gestoffeerd")',
        'span:contains("kaal")',
      ];

      for (const selector of interiorSelectors) {
        const interiorEl = $(selector).first();
        if (interiorEl.length) {
          const interiorText = interiorEl.text().trim();
          if (interiorText && interiorText.length > 0) {
            interior = interiorText;
            loggers.scraper.debug("Interior extracted", {
              selector,
              interior,
              url,
            });
            break;
          }
        }
      }
    }

    // Extract images with enhanced selectors
    const imageSelectors = [
      ".object-media-gallery img",
      ".media-gallery img",
      ".object-photos img",
      '[data-test-id*="media"] img',
      '[data-test-id*="photo"] img',
      '[data-test-id*="image"] img',
      ".object-media img",
      ".photo-gallery img",
      ".gallery img",
      'img[src*="funda"]',
      'img[data-src*="funda"]',
      'img[srcset*="funda"]',
      ".object-header img",
      "picture img",
      '[class*="gallery"] img',
      '[class*="photo"] img',
      '[class*="image"] img',
    ];

    for (const selector of imageSelectors) {
      $(selector).each((index, img) => {
        const src =
          $(img).attr("src") ||
          $(img).attr("data-src") ||
          $(img).attr("data-lazy-src");
        if (
          src &&
          !images.includes(src) &&
          (src.includes("funda") || src.includes("http"))
        ) {
          // Convert relative URLs to absolute
          const absoluteSrc = src.startsWith("http")
            ? src
            : `https://www.funda.nl${src}`;
          images.push(absoluteSrc);
        }
      });
      if (images.length > 0) {
        loggers.scraper.debug("Images extracted", {
          selector,
          count: images.length,
          url,
        });
        break;
      }
    }

    // If no images found, try to extract from srcset attributes
    if (images.length === 0) {
      $("img[srcset]").each((index, img) => {
        const srcset = $(img).attr("srcset");
        if (srcset && srcset.includes("funda")) {
          // Extract the first URL from srcset
          const match = srcset.match(/(https?:\/\/[^\s,]+)/);
          if (match && !images.includes(match[1])) {
            images.push(match[1]);
          }
        }
      });
      if (images.length > 0) {
        loggers.scraper.debug("Images extracted from srcset", {
          count: images.length,
          url,
        });
      }
    }

    loggers.scraper.debug("Images extraction completed", {
      count: images.length,
      url,
    });
    if (images.length === 0) {
      loggers.scraper.warn(
        "No images found - this might indicate a selector issue",
        { url }
      );
    }

    // Method 3: Extract missing fields from description text as last resort
    if (description && (!size || !bedrooms || !rooms || !year)) {
      loggers.scraper.debug(
        "Trying to extract missing fields from description text",
        { url }
      );

      if (!size) {
        const sizeMatch = description.match(
          /(\d+)\s*m[²2]|oppervlakte[^\d]*(\d+)/i
        );
        if (sizeMatch) {
          size = parseInt(sizeMatch[1] || sizeMatch[2]);
          loggers.scraper.debug("Size extracted from description", {
            size,
            url,
          });
        }
      }

      if (!bedrooms) {
        const bedroomMatch = description.match(
          /(\d+)[\s-]*slaapkamer|slaapkamer[^\d]*(\d+)/i
        );
        if (bedroomMatch) {
          bedrooms = parseInt(bedroomMatch[1] || bedroomMatch[2]);
          loggers.scraper.debug("Bedrooms extracted from description", {
            bedrooms,
            url,
          });
        }
      }

      if (!rooms) {
        const roomMatch = description.match(
          /(\d+)[\s-]*kamer(?!s*slaap)|kamer[^\d]*(\d+)(?!.*slaap)/i
        );
        if (roomMatch) {
          rooms = parseInt(roomMatch[1] || roomMatch[2]);
          loggers.scraper.debug("Rooms extracted from description", {
            rooms,
            url,
          });
        }
      }

      if (!year) {
        const yearMatch = description.match(
          /bouwjaar[^\d]*(\d{4})|(\d{4})[^\d]*gebouwd|uit[^\d]*(\d{4})/i
        );
        if (yearMatch) {
          year = parseInt(yearMatch[1] || yearMatch[2] || yearMatch[3]);
          loggers.scraper.debug("Year extracted from description", {
            year,
            url,
          });
        }
      }

      if (!interior) {
        const interiorMatch = description.match(
          /(gemeubileerd|gestoffeerd|kaal|unfurnished|furnished)/i
        );
        if (interiorMatch) {
          interior = interiorMatch[1];
          loggers.scraper.debug("Interior extracted from description", {
            interior,
            url,
          });
        }
      }
    }

    // Log final extraction results
    loggers.scraper.debug("Final extraction results", {
      url,
      size: size || "not found",
      bedrooms: bedrooms || "not found",
      rooms: rooms || "not found",
      year: year || "not found",
      interior: interior || "not found",
      imageCount: images.length,
    });

    return {
      price,
      size,
      bedrooms,
      rooms,
      description,
      year,
      interior,
      propertyType,
      energyLabel,
      availableFrom,
      constructionPeriod,
      garden,
      balcony,
      parking,
      heating,
      isolation,
      images: images.slice(0, 10), // Limit to 10 images
    };
  } catch (error) {
    loggers.scraper.error("Error fetching details", {
      url,
      error: error.message,
    });
    return {
      price: "Prijs op aanvraag",
      size: null,
      bedrooms: null,
      rooms: null,
      description: null,
      year: null,
      interior: null,
      propertyType: "woning",
      images: [],
    };
  } finally {
    if (detailPage) {
      await detailPage.close().catch(() => {
        // Ignore page close errors - page may already be closed
      });
    }
  }
};

const scrapeFundaPage = async (page, pageUrl, pageNumber) => {
  const listings = [];

  try {
    // Navigate to the provided page URL
    loggers.scraper.info("Navigating to Funda page", { pageNumber, pageUrl });

    await page.goto(pageUrl, {
      waitUntil: "networkidle0",
      timeout: 90000,
    });

    // Add delay to avoid being blocked
    const pageDelay = Math.floor(Math.random() * 3000) + 2000;
    loggers.scraper.debug("Adding delay between pages", {
      pageDelay,
      pageNumber,
    });
    await new Promise((r) => setTimeout(r, pageDelay));

    // Scroll to load content
    await autoScroll(page);
    await new Promise((r) => setTimeout(r, 1000));

    const html = await page.content();

    // Save HTML for debugging (only first page)
    if (pageNumber === 1) {
      require("fs").writeFileSync("funda_page.html", html);
    }

    // Detect next page URL (pagination)
    let nextUrl = null;
    try {
      const $ = cheerio.load(html);
      const nextHref =
        $('a[rel="next"]').attr("href") ||
        $('a[aria-label="Volgende"]').attr("href") ||
        $('a[aria-label="Next"]').attr("href") ||
        $('nav a:contains("Volgende")').attr("href") ||
        $('nav a:contains("Next")').attr("href");
      if (nextHref) {
        if (nextHref.startsWith("http")) {
          nextUrl = nextHref;
        } else if (nextHref.startsWith("/")) {
          nextUrl = `https://www.funda.nl${nextHref}`;
        } else if (nextHref.startsWith("?")) {
          // Handle query parameter URLs like ?page=2
          const currentURL = new URL(pageUrl);
          nextUrl = `${currentURL.origin}${currentURL.pathname}${nextHref}`;
        } else {
          nextUrl = `https://www.funda.nl/${nextHref}`;
        }
        loggers.scraper.debug("Next page URL detected", {
          originalHref: nextHref,
          constructedUrl: nextUrl,
          pageNumber,
        });
      }
    } catch (e) {
      loggers.scraper.warn("Failed to detect next page link", {
        error: e.message,
        pageNumber,
      });
    }

    // Method 1: Extract listings from JSON-LD metadata
    const jsonLdMatches = html.match(
      /\<script type="application\/ld\+json" data-hid="result-list-metadata"\>(.+?)\<\/script\>/s
    );

    if (jsonLdMatches && jsonLdMatches.length > 1) {
      try {
        const jsonLdData = JSON.parse(jsonLdMatches[1]);

        if (
          jsonLdData &&
          jsonLdData.itemListElement &&
          Array.isArray(jsonLdData.itemListElement)
        ) {
          loggers.scraper.debug("JSON-LD data extracted", {
            totalListings: jsonLdData.itemListElement.length,
            pageNumber,
          });

          // Filter only rental listings
          const rentalListings = jsonLdData.itemListElement.filter(
            (item) => item.url && item.url.includes("/huur/")
          );

          loggers.scraper.debug("Filtered rental listings", {
            rentalCount: rentalListings.length,
            pageNumber,
          });

          // Process each rental listing URL to extract data
          for (const item of rentalListings) {
            const urlParts = item.url.split("/");
            const id = urlParts[urlParts.length - 1];

            // Extract property details from URL structure
            let propertyType = "woning";
            let city = "";
            let streetName = "";
            let title = "";

            // Parse URL parts: /detail/huur/[city]/[property-type]-[street-name]/[id]/
            const huurIndex = urlParts.indexOf("huur");
            if (huurIndex !== -1 && huurIndex + 1 < urlParts.length) {
              // City is the first segment after 'huur'
              if (huurIndex + 1 < urlParts.length) {
                city = urlParts[huurIndex + 1].replace(/-/g, " ");
                // Capitalize first letter of each word
                city = city.replace(/\b\w/g, (l) => l.toUpperCase());
              }

              // Property type and street name are combined in the next segment
              if (huurIndex + 2 < urlParts.length) {
                const propertyStreetSegment = urlParts[huurIndex + 2];

                // Check if it starts with a known property type
                if (propertyStreetSegment.startsWith("appartement-")) {
                  propertyType = "appartement";
                  streetName = propertyStreetSegment
                    .substring(12)
                    .replace(/-/g, " ");
                } else if (propertyStreetSegment.startsWith("huis-")) {
                  propertyType = "huis";
                  streetName = propertyStreetSegment
                    .substring(5)
                    .replace(/-/g, " ");
                } else if (propertyStreetSegment.startsWith("kamer-")) {
                  propertyType = "kamer";
                  streetName = propertyStreetSegment
                    .substring(6)
                    .replace(/-/g, " ");
                } else if (
                  propertyStreetSegment.startsWith("parkeergelegenheid-")
                ) {
                  propertyType = "parkeergelegenheid";
                  streetName = propertyStreetSegment
                    .substring(19)
                    .replace(/-/g, " ");
                } else {
                  // Default to house if no specific type found
                  propertyType = "huis";
                  streetName = propertyStreetSegment.replace(/-/g, " ");
                }

                // Capitalize street name properly
                streetName = streetName.replace(/\b\w/g, (l) =>
                  l.toUpperCase()
                );
                title = streetName;
              }
            }

            // Create a listing object with extracted data
            const listingData = {
              title: title || `${propertyType} in ${city}`,
              url: item.url,
              location: city,
              propertyType,
              price: "Prijs op aanvraag", // Will be updated if we can extract actual price
              source: "funda.nl",
              dateAdded: new Date(),
            };

            loggers.scraper.debug("Listing found", {
              title: listingData.title,
              location: listingData.location,
              url: item.url,
            });

            // Try to fetch detailed information including price
            loggers.scraper.debug("Fetching listing details", {
              url: item.url,
            });
            const browser = await browserPool.getBrowser();
            const details = await fetchListingDetails(browser, item.url);

            // Check if details were successfully fetched (not blocked by verification page)
            if (details === null) {
              loggers.scraper.warn(
                "Skipping listing due to verification page",
                { title: listingData.title, url: item.url }
              );
              continue; // Skip this listing and move to the next one
            }

            // Update listing with detailed information
            if (details.price) listingData.price = details.price;
            if (details.size) listingData.size = details.size;
            if (details.bedrooms) listingData.bedrooms = details.bedrooms;
            if (details.rooms) listingData.rooms = details.rooms;
            if (details.description) {
              listingData.description = details.description;
              loggers.scraper.debug("Description added to listing", {
                title: listingData.title,
                descriptionPreview:
                  details.description.substring(0, 50) + "...",
                url: item.url,
              });
            } else {
              loggers.scraper.warn("No description found for listing", {
                title: listingData.title,
                url: item.url,
              });
            }
            if (details.year) listingData.year = details.year;
            if (details.interior) listingData.interior = details.interior;
            if (details.propertyType)
              listingData.propertyType = details.propertyType;
            if (details.images && details.images.length > 0)
              listingData.images = details.images;

            // Store additional details as extended properties
            const extendedDetails = {};
            if (details.energyLabel)
              extendedDetails.energyLabel = details.energyLabel;
            if (details.availableFrom)
              extendedDetails.availableFrom = details.availableFrom;
            if (details.garden) extendedDetails.garden = details.garden;
            if (details.balcony) extendedDetails.balcony = details.balcony;
            if (details.parking) extendedDetails.parking = details.parking;
            if (details.heating) extendedDetails.heating = details.heating;
            if (details.isolation)
              extendedDetails.isolation = details.isolation;

            // Add extended details if any were found
            if (Object.keys(extendedDetails).length > 0) {
              listingData.extendedDetails = JSON.stringify(extendedDetails);
            }

            listings.push(listingData);
          }
        }
      } catch (jsonError) {
        loggers.scraper.error("Error parsing JSON-LD data", {
          error: jsonError.message,
          pageNumber,
        });
      }
    }

    // Method 2: If JSON-LD doesn't work, try HTML parsing as fallback
    if (listings.length === 0) {
      loggers.scraper.debug("Falling back to HTML parsing", { pageNumber });
      const $ = cheerio.load(html);

      // Look for listing cards
      $('ol[data-test-id="search-results"] > li').each((index, element) => {
        try {
          // Skip elements without data-test-id (those are usually ads)
          if (!$(element).attr("data-test-id")) return;

          const linkElement = $(element).find(
            'a[data-test-id="object-image-link"]'
          );
          if (linkElement.length) {
            const url = "https://www.funda.nl" + linkElement.attr("href");

            // Skip duplicates
            const isDuplicate = listings.some((listing) => listing.url === url);
            if (isDuplicate) return;

            // Extract basic details
            const titleElement = $(element).find(
              'h2[data-test-id="street-name-house-number"]'
            );
            const priceElement = $(element).find('p[data-test-id="price"]');
            const locationElement = $(element).find(
              'p[data-test-id="postal-code-city"]'
            );

            const title = titleElement.length ? titleElement.text().trim() : "";
            const price = priceElement.length
              ? priceElement.text().trim()
              : "Prijs op aanvraag";
            const location = locationElement.length
              ? locationElement.text().trim()
              : "";

            // Extract property type from URL
            const urlParts = url.split("/");
            const huurIndex = urlParts.indexOf("huur");
            let propertyType = "woning";
            let city = "";

            if (huurIndex !== -1 && huurIndex + 1 < urlParts.length) {
              city = urlParts[huurIndex + 1].replace(/-/g, " ");
              city = city.replace(/\b\w/g, (l) => l.toUpperCase());

              if (huurIndex + 2 < urlParts.length) {
                const propertyStreetSegment = urlParts[huurIndex + 2];
                if (propertyStreetSegment.startsWith("appartement-")) {
                  propertyType = "appartement";
                } else if (propertyStreetSegment.startsWith("huis-")) {
                  propertyType = "huis";
                } else if (propertyStreetSegment.startsWith("kamer-")) {
                  propertyType = "kamer";
                }
              }
            }

            listings.push({
              title: title || `${propertyType} in ${city}`,
              url: url,
              location: location || city,
              propertyType,
              price,
              source: "funda.nl",
              dateAdded: new Date(),
            });
          }
        } catch (err) {
          loggers.scraper.error("Error processing listing in HTML fallback", {
            error: err.message,
            pageNumber,
          });
        }
      });
    }

    loggers.scraper.debug("Page scraping completed", {
      pageNumber,
      listingsFound: listings.length,
      nextUrl: nextUrl || null,
    });
    return { listings, nextUrl };
  } catch (error) {
    loggers.scraper.error("Error scraping page", {
      pageNumber,
      error: error.message,
    });
    return { listings: [], nextUrl: null };
  }
};

const scrapeFunda = async (retryCount = 0, maxRetries = 3) => {
  if (retryCount === 0) {
    scrapingMetrics.recordScrapeStart();
  }

  let browser = null;
  let page = null;
  let listingsSaved = 0;
  let duplicatesSkipped = 0;

  try {
    // Use browser pool instead of launching new browser each time
    browser = await browserPool.getBrowser();
    page = await browser.newPage();

    // Apply enhanced Funda-specific stealth settings
    await setupFundaStealth(page);

    // Enable JavaScript
    await page.setJavaScriptEnabled(true);

    // Add cookies to make it look like a returning user
    await page.setCookie(
      {
        name: "OptanonAlertBoxClosed",
        value: new Date().toISOString(),
        domain: ".funda.nl",
        httpOnly: false,
        secure: true,
      },
      {
        name: "OptanonConsent",
        value:
          "isGpcEnabled=0&datestamp=" +
          new Date().toISOString() +
          "&version=6.26.0",
        domain: ".funda.nl",
        httpOnly: false,
        secure: true,
      },
      {
        name: "ajs_anonymous_id",
        value: "%22" + Math.random().toString(36).substring(2, 15) + "%22",
        domain: ".funda.nl",
        httpOnly: false,
        secure: true,
      }
    );

    const allListings = [];
    const maxPages = 20; // Cap to avoid being blocked; adjust as needed

    // Start from all-Netherlands rental search
    let currentUrl =
      "https://www.funda.nl/zoeken/huur/?selected_area=%5B%22nl%22%5D&availability=%5B%22available%22,%22negotiations%22%5D&object_type=%5B%22house%22,%22apartment%22%5D&sort=%22date_down%22";

    // Scrape multiple pages by following the 'next' link
    for (
      let pageNumber = 1;
      pageNumber <= maxPages && currentUrl;
      pageNumber++
    ) {
      const { listings: pageListings, nextUrl } = await scrapeFundaPage(
        page,
        currentUrl,
        pageNumber
      );

      if (!pageListings || pageListings.length === 0) {
        loggers.scraper.info("No listings found on page, stopping pagination", {
          pageNumber,
          currentUrl,
        });
        break;
      }

      allListings.push(...pageListings);

      // Prepare next page URL
      currentUrl = nextUrl;

      // Add delay between pages to avoid being blocked
      if (pageNumber < maxPages && currentUrl) {
        const delay = Math.floor(Math.random() * 2000) + 3000; // 3-5 seconds
        loggers.scraper.debug("Waiting between pages", {
          delayMs: delay,
          nextPage: pageNumber + 1,
        });
        await new Promise((r) => setTimeout(r, delay));
      }
    }

    loggers.scraper.info("All pages scraped", {
      totalListings: allListings.length,
    });

    // Transform and save listings to database
    if (allListings.length > 0) {
      for (const rawListingData of allListings) {
        // Transform the listing data using the unified schema
        const transformedData = await validateAndNormalizeListingEnhanced(
          rawListingData
        );

        if (!transformedData) {
          loggers.scraper.warn("Skipping invalid listing", {
            title: rawListingData.title || "Unknown",
          });
          continue;
        }

        loggers.scraper.debug("Processing listing", {
          title: transformedData.title,
          url: transformedData.url,
        });
        try {
          const newListing = new Listing(transformedData);
          await newListing.save();
          loggers.scraper.info("Listing saved successfully", {
            title: newListing.title,
          });
          listingsSaved++;
          sendAlerts(newListing);
        } catch (error) {
          if (error.code === 11000) {
            // Duplicate key error
            loggers.scraper.debug("Skipping duplicate listing", {
              title: transformedData.title,
            });
            duplicatesSkipped++;
          } else {
            loggers.scraper.error("Error saving listing", {
              title: transformedData.title,
              error: error.message,
            });
          }
        }
      }
    } else {
      loggers.scraper.info("No listings found on Funda");
    }

    scrapingMetrics.recordScrapeSuccess(
      allListings.length,
      listingsSaved,
      duplicatesSkipped
    );
    loggers.scraper.info("Funda scraping completed", {
      found: allListings.length,
      saved: listingsSaved,
      duplicates: duplicatesSkipped,
    });

    // Trigger auto-application processing for new listings
    if (listingsSaved > 0) {
      try {
        loggers.scraper.info("Processing new listings for auto-application", {
          newListingsCount: listingsSaved,
        });

        // Get the newly saved listings for auto-application processing
        const recentListings = await Listing.find({
          source: "funda.nl",
          dateAdded: { $gte: new Date(Date.now() - 60 * 60 * 1000) }, // Last hour
        }).limit(listingsSaved * 2); // Get more than saved to account for timing issues

        // Process through auto-application integration
        const autoAppResults =
          await scraperAutoApplicationIntegration.processNewListings(
            recentListings,
            "funda.nl"
          );

        loggers.scraper.info("Auto-application processing completed", {
          processed: autoAppResults.processed,
          duplicates: autoAppResults.duplicates,
          autoApplicationTriggered: autoAppResults.autoApplicationTriggered,
          errors: autoAppResults.errors,
        });
      } catch (error) {
        loggers.scraper.error("Error in auto-application processing", {
          error: error.message,
          stack: error.stack,
        });
        // Don't throw the error to avoid breaking the scraper
      }
    }

    return allListings;
  } catch (error) {
    loggers.scraper.error("Error during Funda scraping", {
      attempt: retryCount + 1,
      maxAttempts: maxRetries + 1,
      error: error.message,
    });

    // Record failure only on first attempt
    if (retryCount === 0) {
      scrapingMetrics.recordScrapeFailure(error);
    }

    // Retry logic for transient errors
    if (retryCount < maxRetries && isRetryableError(error)) {
      const retryDelay = (retryCount + 1) * 5;
      loggers.scraper.info("Retrying Funda scraping", {
        retryDelay,
        attempt: retryCount + 2,
      });
      await new Promise((resolve) => setTimeout(resolve, retryDelay * 1000));
      return scrapeFunda(retryCount + 1, maxRetries);
    }

    loggers.scraper.error("Funda scraping failed after max attempts", {
      maxAttempts: maxRetries + 1,
      saved: listingsSaved,
      duplicates: duplicatesSkipped,
    });
    return [];
  } finally {
    // Close the page to free up resources
    if (page) {
      try {
        await page.close();
      } catch (closeError) {
        loggers.scraper.error("Error closing Funda page", {
          error: closeError.message,
        });
      }
    }
  }
};

module.exports = {
  scrapeFunda,
};
