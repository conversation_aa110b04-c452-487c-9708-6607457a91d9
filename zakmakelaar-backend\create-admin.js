const mongoose = require("mongoose");
const User = require("./src/models/User");
const config = require("./src/config/config");

async function createAdminUser() {
  try {
    // Auto-detect the correct MongoDB URI based on environment
    let mongoURI = config.mongoURI;

    // Check if we need to use authenticated URI
    const isProduction = process.env.NODE_ENV === "production";

    // If current URI fails, try different options
    console.log("🔍 Detecting MongoDB configuration...");

    // Option 1: Check for specific Docker/authenticated URI in environment
    if (process.env.MONGO_URI_AUTH) {
      mongoURI = process.env.MONGO_URI_AUTH;
      console.log("🔐 Using MONGO_URI_AUTH from environment");
    }
    // Option 2: Try to use the commented Docker URI from .env if production
    else if (isProduction && mongoURI.includes("localhost:27017")) {
      // Use the Docker MongoDB URI with authentication
      mongoURI =
        "************************************************************************";
      console.log("🐳 Using Docker MongoDB URI with authentication");
    }
    // Option 3: Construct from individual environment variables
    else if (process.env.MONGO_USER || process.env.MONGO_PASSWORD) {
      const dbUser = process.env.MONGO_USER || "admin";
      const dbPass = process.env.MONGO_PASSWORD || "password123";
      const dbHost = process.env.MONGO_HOST || "localhost";
      const dbPort = process.env.MONGO_PORT || "27017";
      const dbName = process.env.MONGO_DB_NAME || "zakmakelaar";
      const authSource = process.env.MONGO_AUTH_SOURCE || "admin";

      mongoURI = `mongodb://${dbUser}:${dbPass}@${dbHost}:${dbPort}/${dbName}?authSource=${authSource}`;
      console.log(
        "🔧 Constructed authenticated MongoDB URI from individual variables"
      );
    } else {
      console.log("📍 Using default MongoDB URI from config");
    }

    // Debug: Print connection info (without credentials)
    const maskedURI = mongoURI.replace(/\/\/([^:]+:[^@]+)@/, "//***:***@");
    console.log(`Attempting to connect to MongoDB: ${maskedURI}`);

    // Enhanced connection options for production
    const connectionOptions = {
      serverSelectionTimeoutMS: 10000, // 10 second timeout
      connectTimeoutMS: 10000, // 10 second timeout
      socketTimeoutMS: 30000, // 30 second timeout
    };

    // Add authentication database if not specified in URI
    if (process.env.MONGO_AUTH_DB) {
      connectionOptions.authSource = process.env.MONGO_AUTH_DB;
    }

    console.log("Connection options:", {
      serverSelectionTimeoutMS: connectionOptions.serverSelectionTimeoutMS,
      connectTimeoutMS: connectionOptions.connectTimeoutMS,
      authSource: connectionOptions.authSource || "default",
    });

    // Try connecting with retry logic for different URI options
    let connectionSuccessful = false;
    let lastError = null;

    // List of URI options to try
    const uriOptions = [
      mongoURI, // Primary URI
      "************************************************************************", // Docker URI
      "mongodb://localhost:27017/zakmakelaar", // Simple local URI
    ];

    // Remove duplicates
    const uniqueUriOptions = [...new Set(uriOptions)];

    for (let i = 0; i < uniqueUriOptions.length && !connectionSuccessful; i++) {
      const tryURI = uniqueUriOptions[i];
      const maskedTryURI = tryURI.replace(/\/\/([^:]+:[^@]+)@/, "//***:***@");

      try {
        console.log(
          `\n\ud83d� Attempt ${i + 1}/${
            uniqueUriOptions.length
          }: ${maskedTryURI}`
        );
        await mongoose.connect(tryURI, connectionOptions);
        connectionSuccessful = true;
        console.log("✅ Connected to MongoDB successfully");
        break;
      } catch (connectError) {
        lastError = connectError;
        console.log(`❌ Attempt ${i + 1} failed: ${connectError.message}`);

        // Close any partial connections
        if (mongoose.connection.readyState !== 0) {
          try {
            await mongoose.connection.close();
          } catch (closeError) {
            // Ignore close errors
          }
        }
      }
    }

    if (!connectionSuccessful) {
      throw lastError || new Error("Failed to connect with all URI options");
    }

    // Test the connection with a simple operation
    console.log("Testing database connection...");
    const dbStats = await mongoose.connection.db.stats();
    console.log(
      `Database connected: ${mongoose.connection.name}, Collections: ${dbStats.collections}`
    );

    // Check if admin user already exists
    console.log("Checking for existing admin user...");
    const existingAdmin = await User.findOne({
      email: "<EMAIL>",
    });

    if (existingAdmin) {
      console.log("ℹ️  Admin user already exists:");
      console.log(`   Email: ${existingAdmin.email}`);
      console.log(`   Role: ${existingAdmin.role}`);
      console.log(`   ID: ${existingAdmin._id}`);
      console.log(`   Created: ${existingAdmin.createdAt}`);
      await mongoose.connection.close();
      return;
    }

    console.log("Creating new admin user...");
    // Create admin user
    const adminUser = new User({
      email: "<EMAIL>",
      password: "admin123", // Will be hashed by the pre-save middleware
      role: "admin",
      emailVerified: true,
      profile: {
        firstName: "Admin",
        lastName: "User",
        name: "Admin User",
      },
      gdprConsent: {
        consents: new Map([
          [
            "data_processing",
            {
              granted: true,
              timestamp: new Date(),
              version: "1.0",
            },
          ],
        ]),
        lastUpdated: new Date(),
        privacyPolicyVersion: "1.0",
        dataRetentionConsent: true,
      },
    });

    await adminUser.save();
    console.log("✅ Admin user created successfully:");
    console.log(`   Email: ${adminUser.email}`);
    console.log(`   Role: ${adminUser.role}`);
    console.log(`   ID: ${adminUser._id}`);
    console.log(`   Created: ${adminUser.createdAt}`);

    await mongoose.connection.close();
    console.log("✅ Database connection closed");
  } catch (error) {
    console.error("❌ Error creating admin user:");
    console.error(`   Error Type: ${error.constructor.name}`);
    console.error(`   Error Code: ${error.code || "N/A"}`);
    console.error(`   Error Message: ${error.message}`);

    if (error.code === 13) {
      console.error("\n🔒 Authentication Error Troubleshooting:");
      console.error("   1. Check if MONGO_URI includes username and password:");
      console.error(
        "      Format: ********************************:port/database"
      );
      console.error("   2. Verify the MongoDB user has read/write permissions");
      console.error("   3. Check if authentication database is correct");
      console.error("   4. Ensure the database name in the URI is correct");
      console.error("\n💡 Current environment variables:");
      console.error(`   NODE_ENV: ${process.env.NODE_ENV || "not set"}`);
      console.error(
        `   MONGO_URI: ${process.env.MONGO_URI ? "[SET]" : "[NOT SET]"}`
      );
      console.error(
        `   MONGO_AUTH_DB: ${process.env.MONGO_AUTH_DB || "[NOT SET]"}`
      );
    }

    if (mongoose.connection.readyState !== 0) {
      try {
        await mongoose.connection.close();
        console.log("Database connection closed");
      } catch (closeError) {
        console.error("Error closing database connection:", closeError.message);
      }
    }

    process.exit(1);
  }
}

createAdminUser();
