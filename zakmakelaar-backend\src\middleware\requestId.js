const { v4: uuidv4 } = require("uuid");

/**
 * Ensures every request carries a stable Request-Id for logging and tracing.
 * If the client already supplied one we reuse it, otherwise a new UUID is issued.
 */
module.exports = (req, res, next) => {
  const headerId =
    req.headers["x-request-id"] ||
    req.headers["x-correlation-id"] ||
    req.headers["x-amzn-trace-id"];

  const requestId =
    typeof headerId === "string" && headerId.trim().length > 0
      ? headerId.trim()
      : uuidv4();

  req.id = requestId;
  req.requestId = requestId;

  res.setHeader("X-Request-Id", requestId);

  return next();
};
