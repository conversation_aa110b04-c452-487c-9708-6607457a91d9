const config = require("./config");

const DEFAULT_CORS = {
  development: ["http://localhost:3000", "http://127.0.0.1:3000"],
  test: ["http://localhost:3000"],
  staging: ["https://staging.zakmakelaar.com"],
  production: ["https://zakmakelaar.com", "https://app.zakmakelaar.com"],
};

const parseList = (value) => {
  if (!value) return [];
  return value
    .split(",")
    .map((item) => item.trim())
    .filter(Boolean);
};

const envOrigins = parseList(
  process.env.CORS_ORIGINS || process.env.CORS_ORIGIN
);

const configuredOrigins = Array.isArray(config.corsOrigin)
  ? config.corsOrigin
  : [];

const fallbackOrigins =
  DEFAULT_CORS[config.nodeEnv] || DEFAULT_CORS.development;

const allowedOrigins = Array.from(
  new Set([...envOrigins, ...configuredOrigins, ...fallbackOrigins].filter(Boolean))
);

const allowAllOrigins = allowedOrigins.includes("*");

const wildcardToRegExp = (pattern) => {
  const escaped = pattern.replace(/[.+?^${}()|[\]\\]/g, "\\$&");
  return new RegExp(`^${escaped.replace(/\*/g, ".*")}$`, "i");
};

const matchesOrigin = (origin, pattern) => {
  if (pattern === "*") return true;
  if (pattern.includes("*")) {
    return wildcardToRegExp(pattern).test(origin);
  }
  return origin.toLowerCase() === pattern.toLowerCase();
};

const websocketFromHttp = (origin) => {
  if (!origin) return null;
  if (origin.startsWith("https://")) {
    return origin.replace("https://", "wss://");
  }
  if (origin.startsWith("http://")) {
    return origin.replace("http://", "ws://");
  }
  return null;
};

const additionalConnectSources = parseList(process.env.CSP_CONNECT_SRC);

const connectSources = Array.from(
  new Set(
    [
      ...allowedOrigins,
      ...allowedOrigins.map(websocketFromHttp),
      ...additionalConnectSources,
    ].filter(Boolean)
  )
);

const baseHelmetOptions = {
  contentSecurityPolicy: false,
  crossOriginEmbedderPolicy: false,
  referrerPolicy: { policy: "strict-origin-when-cross-origin" },
  frameguard: { action: "deny" },
  hsts:
    config.nodeEnv === "development"
      ? false
      : {
          maxAge: 31536000,
          includeSubDomains: true,
          preload: true,
        },
};

const buildCorsOptions = () => ({
  origin: (origin, callback) => {
    if (!origin || allowAllOrigins) {
      return callback(null, true);
    }

    const isAllowed = allowedOrigins.some((pattern) =>
      matchesOrigin(origin, pattern)
    );

    if (isAllowed) {
      return callback(null, true);
    }

    const error = new Error(
      `Origin ${origin} is not permitted by CORS policy`
    );
    error.status = 403;
    return callback(error, false);
  },
  methods: ["GET", "HEAD", "PUT", "PATCH", "POST", "DELETE", "OPTIONS"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "X-Requested-With",
    "X-Request-Id",
    "X-CSRF-Token",
  ],
  credentials: true,
  exposedHeaders: ["Request-Id"],
  optionsSuccessStatus: 204,
});

const buildCspDirectives = (nonce) => {
  const directives = {
    "default-src": ["'self'"],
    "script-src": ["'self'", `'nonce-${nonce}'`, "https://cdn.jsdelivr.net"],
    "style-src": [
      "'self'",
      `'nonce-${nonce}'`,
      "https://fonts.googleapis.com",
      "https://cdn.jsdelivr.net",
    ],
    "font-src": [
      "'self'",
      "https://fonts.gstatic.com",
      "https://cdn.jsdelivr.net",
      "data:",
    ],
    "img-src": ["'self'", "data:", "blob:", "https:"],
    "connect-src": ["'self'", ...connectSources],
    "frame-src": ["'none'"],
    "frame-ancestors": ["'none'"],
    "object-src": ["'none'"],
    "base-uri": ["'self'"],
    "form-action": ["'self'"],
    "worker-src": ["'self'", "blob:"],
    "media-src": ["'self'", "blob:", "data:"],
  };

  if (config.nodeEnv !== "development") {
    directives["upgrade-insecure-requests"] = [];
  }

  const reportUri = process.env.CSP_REPORT_URI;
  if (reportUri) {
    directives["report-uri"] = [reportUri];
  }

  return directives;
};

const serializeCsp = (directives) =>
  Object.entries(directives)
    .map(([directive, values]) => {
      if (!values || values.length === 0) {
        return directive;
      }
      return `${directive} ${values.join(" ")}`;
    })
    .join("; ");

const buildCspHeader = (nonce) => serializeCsp(buildCspDirectives(nonce));

const applyCsp = (req, res, next) => {
  const nonce =
    res.locals?.cspNonce || req.cspNonce || req.headers["x-csp-nonce"];
  if (!nonce) {
    return next();
  }

  res.setHeader("Content-Security-Policy", buildCspHeader(nonce));
  return next();
};

const buildHelmetOptions = () => ({ ...baseHelmetOptions });

const getAllowedOrigins = () => [...allowedOrigins];
const getConnectSources = () => [...connectSources];

module.exports = {
  allowedOrigins,
  getAllowedOrigins,
  getConnectSources,
  buildCorsOptions,
  buildHelmetOptions,
  buildCspDirectives,
  buildCspHeader,
  applyCsp,
  matchesOrigin,
};
