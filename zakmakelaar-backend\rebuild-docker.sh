#!/usr/bin/env bash
# Bash script to rebuild and restart Docker containers
# Usage: ./rebuild-docker.sh

set -e

echo "🐳 Zakmakelaar Docker Rebuild Script"
echo "====================================="
echo ""

# Check if Docker is running
echo "📋 Checking Docker status..."
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed!"
    echo "Please install Docker and make sure it's running."
    exit 1
fi

DOCKER_VERSION=$(docker --version)
echo "✅ Docker is installed: $DOCKER_VERSION"

# Check if docker-compose is available
if ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose is not available!"
    exit 1
fi

COMPOSE_VERSION=$(docker compose version)
echo "✅ Docker Compose is available: $COMPOSE_VERSION"

echo ""
echo "🛑 Stopping existing containers..."
docker compose down

echo ""
echo "🗑️  Removing old images (optional)..."
docker compose down --rmi local 2>/dev/null || true

echo ""
echo "🔨 Building Docker images..."
docker compose build --no-cache

echo ""
echo "🚀 Starting containers..."
docker compose up -d

echo ""
echo "✅ Docker containers rebuilt and started successfully!"
echo ""
echo "📊 Container Status:"
docker compose ps

echo ""
echo "📝 Useful Commands:"
echo "  View logs:           docker compose logs -f"
echo "  View backend logs:   docker compose logs -f backend"
echo "  Stop containers:     docker compose down"
echo "  Restart containers:  docker compose restart"
echo ""
echo "🌐 Services should be available at:"
echo "  Backend API:  http://localhost:3000"
echo "  MongoDB:      mongodb://localhost:27017"
echo "  Redis:        localhost:6379"
echo ""

