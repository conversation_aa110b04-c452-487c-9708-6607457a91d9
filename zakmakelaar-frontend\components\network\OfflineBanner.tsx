import React, { useMemo } from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import Animated, { FadeInDown, FadeOutUp } from "react-native-reanimated";

import { useConnectivityStore } from "@/store/connectivityStore";
import { offlineUtils, queryClient } from "@/services/queryClient";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const Banner: React.FC = () => {
  const insets = useSafeAreaInsets();
  const isOnline = useConnectivityStore((state) => state.isOnline);
  const queuedMutations = useConnectivityStore(
    (state) => state.queuedMutations
  );

  const pendingCopy = useMemo(() => {
    if (!queuedMutations.length) {
      return "We'll queue any changes until your connection is back.";
    }

    const count = queuedMutations.length;
    const plural = count === 1 ? "action" : "actions";
    return `${count} pending ${plural} will retry automatically when you're online.`;
  }, [queuedMutations]);

  const handleRetryPress = async () => {
    await offlineUtils.processQueuedMutations();
    await queryClient.refetchQueries({
      type: "active",
      stale: true,
    });
  };

  if (isOnline) {
    return null;
  }

  return (
    <Animated.View
      entering={FadeInDown.duration(200)}
      exiting={FadeOutUp.duration(150)}
      style={[
        styles.container,
        {
          paddingTop: Math.max(insets.top - 6, 10),
        },
      ]}
    >
      <View style={styles.textContainer}>
        <Text style={styles.title}>You're offline</Text>
        <Text style={styles.subtitle}>{pendingCopy}</Text>
      </View>

      <TouchableOpacity
        accessibilityRole="button"
        accessibilityLabel="Retry pending actions"
        onPress={handleRetryPress}
        style={styles.button}
        activeOpacity={0.85}
      >
        <Text style={styles.buttonText}>Retry now</Text>
      </TouchableOpacity>
    </Animated.View>
  );
};

export const OfflineBanner = React.memo(Banner);

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#2f3640",
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    paddingHorizontal: 16,
    paddingBottom: 12,
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    color: "#f5f6fa",
    fontWeight: "600",
    fontSize: 16,
    marginBottom: 2,
  },
  subtitle: {
    color: "rgba(245, 246, 250, 0.8)",
    fontSize: 13,
    lineHeight: 16,
  },
  button: {
    backgroundColor: "#f72585",
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderRadius: 999,
  },
  buttonText: {
    color: "#ffffff",
    fontWeight: "600",
    fontSize: 13,
  },
});

export default OfflineBanner;
