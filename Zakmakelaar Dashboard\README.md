# ZakMakelaar Dashboard - API Integration

This dashboard has been successfully connected to the ZakMakelaar API backend running on `http://localhost:3000`.

## Features

✅ **API Service Layer** - Centralized API communication with error handling and authentication
✅ **Real Property Data** - Properties loaded from `/api/property-owner/properties` endpoint
✅ **Dashboard Statistics** - Live stats from `/api/property-owner/dashboard` and `/api/property-owner/statistics`
✅ **Dynamic Charts** - Charts updated with real data from API endpoints
✅ **Recent Activity** - Recent listings and notifications from API
✅ **Property Search** - Live property search with filters
✅ **Authentication** - Login/logout functionality with JWT tokens

## Setup Instructions

### Prerequisites

1. **Backend API Running**: Ensure the ZakMakelaar backend is running on `http://localhost:3000`
2. **Web Server**: Use a local web server to serve the dashboard files (due to CORS and module restrictions)

### Quick Start

1. **Start the backend API** (if not already running):
   ```bash
   # Navigate to your backend directory
   cd path/to/zakmakelaar-backend
   npm start
   ```

2. **Serve the dashboard** using one of these methods:

   **Option A: Python (if installed)**
   ```bash
   cd "C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\Zakmakelaar Dashboard"
   python -m http.server 8080
   ```
   Then open: http://localhost:8080

   **Option B: Node.js (if installed)**
   ```bash
   cd "C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\Zakmakelaar Dashboard"
   npx http-server -p 8080
   ```
   Then open: http://localhost:8080

   **Option C: Live Server Extension (VS Code)**
   - Right-click on `index.html` in VS Code
   - Select "Open with Live Server"

### Authentication

- Click the "Login" button in the top-right corner
- Use valid credentials from your backend API
- Once authenticated, you'll see enhanced features and real data

### API Integration Details

#### Endpoints Used:
- `GET /` - API health check
- `POST /api/auth/login` - User authentication
- `POST /api/auth/logout` - User logout
- `GET /api/property-owner/dashboard` - Dashboard statistics
- `GET /api/property-owner/statistics` - Detailed statistics
- `GET /api/property-owner/properties` - Property listings
- `GET /api/notifications` - Recent notifications
- `GET /api/properties/search` - Public property search

#### Fallback Behavior:
- If API is unavailable, the dashboard falls back to mock data
- Unauthenticated users see public data where available
- Error messages are displayed when API calls fail

## File Structure

```
Zakmakelaar Dashboard/
├── index.html          # Main dashboard HTML
├── main.js             # Dashboard functionality & API integration
├── api-service.js      # Centralized API service layer
├── properties.html     # Properties page
├── analytics.html      # Analytics page
└── README.md          # This file
```

## Development Notes

### API Data Mapping
The dashboard handles different API data formats by normalizing property data:
- Maps `rent.amount` to `price`
- Maps `propertyType` to `type`
- Maps `address.city` to `city`
- Extracts features from `features` and `policies` objects

### Authentication Flow
1. JWT tokens are stored in localStorage
2. Tokens are included in all API requests via Authorization header
3. Authentication state is checked on page load
4. UI updates based on authentication status

### Error Handling
- Network errors are caught and logged
- User-friendly error messages are displayed
- Graceful fallback to mock data when needed

## Troubleshooting

**Dashboard not loading data:**
- Check if the backend API is running on http://localhost:3000
- Check browser console for error messages
- Verify CORS settings on the backend

**Authentication issues:**
- Ensure backend auth endpoints are working
- Check browser network tab for failed requests
- Clear localStorage if needed: `localStorage.clear()`

**Charts not displaying:**
- Ensure ECharts library is loaded
- Check console for JavaScript errors
- Verify API statistics endpoints return expected data structure

## Browser Compatibility

- Modern browsers supporting ES6+ features
- Fetch API support required
- localStorage support required

## Security Considerations

- JWT tokens are stored in localStorage (consider httpOnly cookies for production)
- API requests include proper authentication headers
- Error messages don't expose sensitive information
- Input validation on forms